import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/users/me/preferences - Get the current user's preferences
export const GET: RequestHandler = async ({ locals }) => {
  try {
    // Check if user is authenticated
    if (!locals.user) {
      return json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }
    
    return json({
      success: true,
      data: locals.user.preferences
    });
  } catch (error) {
    console.error('Error fetching user preferences:', error);
    return json({
      success: false,
      error: 'Failed to fetch user preferences'
    }, { status: 500 });
  }
};

// PUT /api/users/me/preferences - Update the current user's preferences
export const PUT: RequestHandler = async ({ request, locals }) => {
  try {
    // Check if user is authenticated
    if (!locals.user) {
      return json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }
    
    const body = await request.json();
    
    // Validate preferences
    if (typeof body.highContrast !== 'boolean' || 
        typeof body.largeText !== 'boolean' || 
        typeof body.simplifiedInterface !== 'boolean') {
      return json({
        success: false,
        error: 'Invalid preferences format'
      }, { status: 400 });
    }
    
    // Update preferences in the database
    const result = await db.update(users)
      .set({
        preferences: {
          highContrast: body.highContrast,
          largeText: body.largeText,
          simplifiedInterface: body.simplifiedInterface
        }
      })
      .where(eq(users.id, locals.user.id))
      .returning();
    
    return json({
      success: true,
      data: result[0].preferences
    });
  } catch (error) {
    console.error('Error updating user preferences:', error);
    return json({
      success: false,
      error: 'Failed to update user preferences'
    }, { status: 500 });
  }
};
