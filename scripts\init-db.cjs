const Database = require('better-sqlite3');
const path = require('path');

// Connect to the database
const dbPath = path.join(__dirname, '..', 'local.db');
const db = new Database(dbPath);

console.log('Initializing database...');

try {
  // Check if admin user exists
  const existingAdmin = db.prepare('SELECT * FROM users WHERE email = ?').get('<EMAIL>');
  
  if (!existingAdmin) {
    console.log('Creating admin user...');
    
    // Insert admin user with a simple password hash for development
    // In production, you'd use proper password hashing
    const stmt = db.prepare(`
      INSERT INTO users (username, display_name, email, password_hash, role, created_at, updated_at, preferences)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const now = new Date().toISOString();
    const preferences = JSON.stringify({
      highContrast: false,
      largeText: false,
      simplifiedInterface: false
    });
    
    stmt.run(
      'admin',
      'Administrator',
      '<EMAIL>',
      'admin', // Simple password for development
      'admin',
      now,
      now,
      preferences
    );
    
    console.log('Admin user created successfully!');
  } else {
    console.log('Admin user already exists.');
  }
  
  // Check tables
  console.log('\nDatabase tables:');
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  tables.forEach(table => {
    const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
    console.log(`- ${table.name}: ${count.count} records`);
  });
  
} catch (error) {
  console.error('Error initializing database:', error);
} finally {
  db.close();
  console.log('Database initialization complete.');
}
