import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { auditLogs, users } from '$lib/server/db/schema';
import { eq, and, gte, lte, desc, like, or } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// GET /api/admin/audit-logs - Get audit logs with filters and pagination
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    // Parse query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const action = url.searchParams.get('action') || '';
    const adminUser = url.searchParams.get('adminUser') || '';
    const targetType = url.searchParams.get('targetType') || '';
    const dateFrom = url.searchParams.get('dateFrom');
    const dateTo = url.searchParams.get('dateTo');

    const offset = (page - 1) * limit;

    // Build conditions
    const conditions: any[] = [];

    // Filter by action
    if (action) {
      conditions.push(like(auditLogs.action, `%${action}%`));
    }

    // Filter by target type
    if (targetType) {
      conditions.push(eq(auditLogs.targetType, targetType));
    }

    // Filter by date range
    if (dateFrom) {
      conditions.push(gte(auditLogs.createdAt, dateFrom));
    }
    if (dateTo) {
      conditions.push(lte(auditLogs.createdAt, dateTo));
    }

    // Build the query with user joins
    let query = db.select({
      id: auditLogs.id,
      adminUserId: auditLogs.adminUserId,
      action: auditLogs.action,
      targetType: auditLogs.targetType,
      targetId: auditLogs.targetId,
      targetUserId: auditLogs.targetUserId,
      details: auditLogs.details,
      ipAddress: auditLogs.ipAddress,
      userAgent: auditLogs.userAgent,
      createdAt: auditLogs.createdAt,
      adminUser: {
        id: users.id,
        username: users.username,
        displayName: users.displayName
      }
    })
    .from(auditLogs)
    .leftJoin(users, eq(auditLogs.adminUserId, users.id));

    // Add target user join if needed
    const queryWithTargetUser = db.select({
      id: auditLogs.id,
      adminUserId: auditLogs.adminUserId,
      action: auditLogs.action,
      targetType: auditLogs.targetType,
      targetId: auditLogs.targetId,
      targetUserId: auditLogs.targetUserId,
      details: auditLogs.details,
      ipAddress: auditLogs.ipAddress,
      userAgent: auditLogs.userAgent,
      createdAt: auditLogs.createdAt,
      adminUser: {
        id: users.id,
        username: users.username,
        displayName: users.displayName
      },
      targetUser: {
        id: users.id,
        username: users.username,
        displayName: users.displayName
      }
    })
    .from(auditLogs)
    .leftJoin(users, eq(auditLogs.adminUserId, users.id))
    .leftJoin(users, eq(auditLogs.targetUserId, users.id));

    // Apply conditions
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Apply admin user filter if specified
    if (adminUser) {
      query = query.where(
        or(
          like(users.username, `%${adminUser}%`),
          like(users.displayName, `%${adminUser}%`)
        )
      );
    }

    // Get total count for pagination
    const countQuery = db.select({ count: auditLogs.id })
      .from(auditLogs)
      .leftJoin(users, eq(auditLogs.adminUserId, users.id));

    if (conditions.length > 0) {
      countQuery.where(and(...conditions));
    }

    if (adminUser) {
      countQuery.where(
        or(
          like(users.username, `%${adminUser}%`),
          like(users.displayName, `%${adminUser}%`)
        )
      );
    }

    // Execute queries
    const [logs, totalResult] = await Promise.all([
      query
        .orderBy(desc(auditLogs.createdAt))
        .limit(limit)
        .offset(offset),
      countQuery
    ]);

    const total = totalResult.length;

    // For each log, get target user info if targetUserId exists
    const logsWithTargetUsers = await Promise.all(
      logs.map(async (log) => {
        if (log.targetUserId) {
          const targetUser = await db.select({
            id: users.id,
            username: users.username,
            displayName: users.displayName
          })
          .from(users)
          .where(eq(users.id, log.targetUserId))
          .limit(1);

          return {
            ...log,
            targetUser: targetUser[0] || null
          };
        }
        return {
          ...log,
          targetUser: null
        };
      })
    );

    return json({
      success: true,
      data: {
        logs: logsWithTargetUsers,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    logger.error('Error fetching audit logs:', error);
    return json({
      success: false,
      error: 'Failed to fetch audit logs'
    }, { status: 500 });
  }
};
