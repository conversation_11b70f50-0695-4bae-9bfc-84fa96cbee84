import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { aiGenerationMetrics, users } from '$lib/server/db/schema';
import { eq, desc, and, gte, sql } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';
import { getUsageStats } from '$lib/server/services/rateLimiting';

// GET /api/admin/ai-usage-metrics - Get AI usage metrics and statistics
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const timeRange = url.searchParams.get('timeRange') || '24h';
    
    // Calculate time threshold based on range
    const now = new Date();
    let timeThreshold: Date;
    
    switch (timeRange) {
      case '1h':
        timeThreshold = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        timeThreshold = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        timeThreshold = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        timeThreshold = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        timeThreshold = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    const timeThresholdISO = timeThreshold.toISOString();

    // Get basic generation statistics
    const totalGenerations = await db
      .select({ count: sql<number>`count(*)` })
      .from(aiGenerationMetrics)
      .where(gte(aiGenerationMetrics.createdAt, timeThresholdISO));

    const successfulGenerations = await db
      .select({ count: sql<number>`count(*)` })
      .from(aiGenerationMetrics)
      .where(and(
        gte(aiGenerationMetrics.createdAt, timeThresholdISO),
        eq(aiGenerationMetrics.success, true)
      ));

    const failedGenerations = await db
      .select({ count: sql<number>`count(*)` })
      .from(aiGenerationMetrics)
      .where(and(
        gte(aiGenerationMetrics.createdAt, timeThresholdISO),
        eq(aiGenerationMetrics.success, false)
      ));

    // Get authenticity score statistics
    const authenticityStats = await db
      .select({
        avgScore: sql<number>`avg(authenticity_score)`,
        highQuality: sql<number>`count(case when authenticity_score >= 80 then 1 end)`,
        needsReview: sql<number>`count(case when authenticity_score >= 60 and authenticity_score < 80 then 1 end)`,
        rejected: sql<number>`count(case when authenticity_score < 60 then 1 end)`
      })
      .from(aiGenerationMetrics)
      .where(and(
        gte(aiGenerationMetrics.createdAt, timeThresholdISO),
        eq(aiGenerationMetrics.success, true)
      ));

    // Get performance statistics
    const performanceStats = await db
      .select({
        avgTime: sql<number>`avg(generation_time_ms)`,
        minTime: sql<number>`min(generation_time_ms)`,
        maxTime: sql<number>`max(generation_time_ms)`
      })
      .from(aiGenerationMetrics)
      .where(and(
        gte(aiGenerationMetrics.createdAt, timeThresholdISO),
        eq(aiGenerationMetrics.success, true)
      ));

    // Get content type breakdown
    const contentTypeBreakdown = await db
      .select({
        type: aiGenerationMetrics.contentType,
        count: sql<number>`count(*)`
      })
      .from(aiGenerationMetrics)
      .where(gte(aiGenerationMetrics.createdAt, timeThresholdISO))
      .groupBy(aiGenerationMetrics.contentType)
      .orderBy(desc(sql`count(*)`));

    // Get top users
    const topUsers = await db
      .select({
        userId: aiGenerationMetrics.userId,
        displayName: users.displayName,
        username: users.username,
        generationCount: sql<number>`count(*)`
      })
      .from(aiGenerationMetrics)
      .leftJoin(users, eq(aiGenerationMetrics.userId, users.id))
      .where(gte(aiGenerationMetrics.createdAt, timeThresholdISO))
      .groupBy(aiGenerationMetrics.userId, users.displayName, users.username)
      .orderBy(desc(sql`count(*)`))
      .limit(5);

    // Get recent activity
    const recentActivity = await db
      .select({
        contentType: aiGenerationMetrics.contentType,
        success: aiGenerationMetrics.success,
        authenticityScore: aiGenerationMetrics.authenticityScore,
        createdAt: aiGenerationMetrics.createdAt,
        userName: users.displayName
      })
      .from(aiGenerationMetrics)
      .leftJoin(users, eq(aiGenerationMetrics.userId, users.id))
      .where(gte(aiGenerationMetrics.createdAt, timeThresholdISO))
      .orderBy(desc(aiGenerationMetrics.createdAt))
      .limit(10);

    // Get rate limiting statistics
    const rateLimitStats = getUsageStats();

    // Calculate derived metrics
    const totalCount = totalGenerations[0]?.count || 0;
    const successCount = successfulGenerations[0]?.count || 0;
    const failedCount = failedGenerations[0]?.count || 0;
    const successRate = totalCount > 0 ? (successCount / totalCount) * 100 : 0;

    const authStats = authenticityStats[0];
    const avgAuthenticityScore = authStats?.avgScore || 0;
    const highQualityCount = authStats?.highQuality || 0;
    const needsReviewCount = authStats?.needsReview || 0;
    const rejectedCount = authStats?.rejected || 0;

    const perfStats = performanceStats[0];
    const avgGenerationTime = perfStats?.avgTime || 0;
    const fastestGeneration = perfStats?.minTime || 0;
    const slowestGeneration = perfStats?.maxTime || 0;

    // Calculate safety metrics (placeholder - would need actual moderation data)
    const safeContentCount = successCount; // Assume all successful generations are safe for now
    const flaggedContentCount = 0; // Would need to track moderation flags
    const blockedContentCount = failedCount; // Simplified assumption
    const safetyRate = totalCount > 0 ? (safeContentCount / totalCount) * 100 : 100;

    const metrics = {
      // Generation Statistics
      totalGenerations: totalCount,
      successfulGenerations: successCount,
      failedGenerations: failedCount,
      successRate,

      // Content Quality
      avgAuthenticityScore,
      highQualityCount,
      needsReviewCount,
      rejectedCount,

      // Content Safety
      safeContentCount,
      flaggedContentCount,
      blockedContentCount,
      safetyRate,

      // Performance
      avgGenerationTime,
      fastestGeneration,
      slowestGeneration,
      rateLimitHits: 0, // Would need to track from rate limiting service

      // Breakdowns
      contentTypeBreakdown: contentTypeBreakdown.map(item => ({
        type: item.type,
        count: item.count
      })),
      
      topUsers: topUsers.map(user => ({
        displayName: user.displayName || 'Unknown User',
        username: user.username || 'unknown',
        generationCount: user.generationCount
      })),

      // Recent Activity
      recentActivity: recentActivity.map(activity => ({
        contentType: activity.contentType,
        success: activity.success,
        authenticityScore: activity.authenticityScore,
        createdAt: activity.createdAt,
        userName: activity.userName || 'Unknown User'
      })),

      // Rate Limiting Stats
      rateLimitingStats: rateLimitStats,

      // Metadata
      timeRange,
      generatedAt: new Date().toISOString()
    };

    logger.info('AI usage metrics requested', {
      timeRange,
      totalGenerations: totalCount,
      requestedBy: locals.user.username
    });

    return json({
      success: true,
      data: metrics
    });

  } catch (error) {
    logger.error('Error fetching AI usage metrics:', error);
    return json({
      success: false,
      error: 'Failed to fetch AI usage metrics'
    }, { status: 500 });
  }
};
