import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { interactionTemplates, auditLogs } from '$lib/server/db/schema';
import { eq, desc } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// GET /api/admin/interaction-templates - Get all interaction templates
export const GET: RequestHandler = async ({ locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    // Get all templates
    const templates = await db.select()
      .from(interactionTemplates)
      .orderBy(desc(interactionTemplates.createdAt));

    return json({
      success: true,
      data: templates
    });

  } catch (error) {
    logger.error('Error fetching interaction templates:', error);
    return json({
      success: false,
      error: 'Failed to fetch interaction templates'
    }, { status: 500 });
  }
};

// POST /api/admin/interaction-templates - Create new interaction template
export const POST: RequestHandler = async ({ request, locals, getClientAddress }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const body = await request.json();
    const { name, category, template, variables, personality } = body;

    // Validate required fields
    if (!name || !category || !template) {
      return json({
        success: false,
        error: 'Name, category, and template are required'
      }, { status: 400 });
    }

    // Create template
    const result = await db.insert(interactionTemplates).values({
      name,
      category,
      template,
      variables: JSON.stringify(variables || []),
      personality: JSON.stringify(personality || [])
    }).returning();

    // Log admin action
    await logAdminAction(
      locals.user.id,
      'create_interaction_template',
      'interaction_template',
      result[0].id,
      null,
      { name, category },
      getClientAddress()
    );

    logger.info('Interaction template created', {
      adminUser: locals.user.username,
      templateId: result[0].id,
      name,
      category
    });

    return json({
      success: true,
      data: result[0],
      message: 'Interaction template created successfully'
    });

  } catch (error) {
    logger.error('Error creating interaction template:', error);
    return json({
      success: false,
      error: 'Failed to create interaction template'
    }, { status: 500 });
  }
};

// Helper function to log admin actions
async function logAdminAction(
  adminUserId: number,
  action: string,
  targetType: string,
  targetId: number | null,
  targetUserId: number | null,
  details: any,
  ipAddress: string
) {
  try {
    await db.insert(auditLogs).values({
      adminUserId,
      action,
      targetType,
      targetId,
      targetUserId,
      details: JSON.stringify(details),
      ipAddress
    });
  } catch (error) {
    logger.error('Failed to log admin action:', error);
  }
}
