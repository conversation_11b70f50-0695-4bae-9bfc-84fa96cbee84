import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { migrate } from 'drizzle-orm/better-sqlite3/migrator';
import * as path from 'path';
import * as fs from 'fs';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get database URL from environment variables
const databaseUrl = process.env.DATABASE_URL || 'local.db';

// Create database directory if it doesn't exist
const dbDir = path.dirname(databaseUrl);
if (dbDir !== '.' && !fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Connect to the database
const sqlite = new Database(databaseUrl);
const db = drizzle(sqlite);

// Run migrations
console.log('Running migrations...');
migrate(db, { migrationsFolder: path.join(__dirname, 'migrations') });
console.log('Migrations completed successfully!');

// Close the database connection
sqlite.close();
