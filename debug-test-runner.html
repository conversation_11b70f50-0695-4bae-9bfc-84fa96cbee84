<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reactivity Debug Test Runner</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #f8f9fa;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.danger:hover {
            background: #c82333;
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #218838;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            border-left: 3px solid #007bff;
            background: white;
        }
        .step h3 {
            margin-top: 0;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Reactivity Debug Test Runner</h1>
            <p>Systematic testing for Svelte 5 reactivity issues</p>
        </div>

        <div class="instructions">
            <h3>📋 Testing Instructions</h3>
            <ol>
                <li><strong>Open the simulate interactions page</strong> in another tab: <a href="http://localhost:5174/admin/simulate-interactions" target="_blank">http://localhost:5174/admin/simulate-interactions</a></li>
                <li><strong>Load the test scripts</strong> by running the commands below in the browser console</li>
                <li><strong>Run the comprehensive tests</strong> to identify reactivity patterns</li>
                <li><strong>Monitor the results</strong> for any infinite loops or errors</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 1: Load Debug Test Scripts</h3>
            <p>Copy and paste these commands into the browser console on the simulate interactions page:</p>
            <div class="console-output">
// Load the debug crash recovery test script
fetch('/src/tests/admin/debug-crash-recovery.test.js')
  .then(response => response.text())
  .then(script => {
    eval(script);
    console.log('✅ Debug crash recovery tests loaded');
  });

// Load the reactivity stress test script
fetch('/src/tests/admin/reactivity-stress-test.js')
  .then(response => response.text())
  .then(script => {
    eval(script);
    console.log('✅ Reactivity stress tests loaded');
  });
            </div>
            <button class="button" onclick="copyToClipboard(this.previousElementSibling.textContent)">📋 Copy Commands</button>
        </div>

        <div class="step">
            <h3>Step 2: Run Baseline Tests</h3>
            <p>Verify the debug system is working correctly:</p>
            <div class="console-output">
// Check debug system status
window.debugTests.testDebugPersistence();
window.debugTests.checkErrorRecovery();
            </div>
            <button class="button" onclick="copyToClipboard(this.previousElementSibling.textContent)">📋 Copy Commands</button>
        </div>

        <div class="step">
            <h3>Step 3: Run Comprehensive Stress Tests</h3>
            <p>Execute the full test suite to identify reactivity issues:</p>
            <div class="console-output">
// Run all reactivity stress tests
window.reactivityStressTest.runComprehensiveReactivityTests()
  .then(results => {
    console.log('🏁 All tests completed');
    console.log('📊 Results:', results);
    
    // Export results
    window.reactivityStressTest.exportTestResults();
  });
            </div>
            <button class="button success" onclick="copyToClipboard(this.previousElementSibling.textContent)">📋 Copy Commands</button>
        </div>

        <div class="step">
            <h3>Step 4: Manual Stress Testing</h3>
            <p>If automated tests don't trigger issues, try manual stress testing:</p>
            <div class="console-output">
// Start reactive call monitoring
const stopMonitoring = window.debugTests.monitorReactiveCalls();

// Manually perform rapid interactions on the page:
// - Rapidly switch between gallery/news content types
// - Quickly change form inputs
// - Try to trigger rapid successive reactive calls

// Stop monitoring after testing
setTimeout(() => {
  stopMonitoring();
  console.log('🛑 Monitoring stopped');
}, 10000); // Stop after 10 seconds
            </div>
            <button class="button" onclick="copyToClipboard(this.previousElementSibling.textContent)">📋 Copy Commands</button>
        </div>

        <div class="step">
            <h3>Step 5: Simulate Error Conditions</h3>
            <p>Test the error boundary system:</p>
            <div class="console-output">
// Simulate a reactivity error
window.debugTests.simulateReactivityError();

// Check if error was captured
window.debugTests.checkErrorRecovery();

// Export all debug data
window.debugTests.exportAllDebugData();
            </div>
            <button class="button danger" onclick="copyToClipboard(this.previousElementSibling.textContent)">📋 Copy Commands</button>
        </div>

        <div class="test-section">
            <h3>🎯 What to Look For</h3>
            <ul>
                <li><strong>Rapid successive reactive calls</strong> - Look for timestamps less than 10ms apart</li>
                <li><strong>Infinite loop warnings</strong> - Console messages about rapid reactive calls</li>
                <li><strong>effect_update_depth_exceeded errors</strong> - The original Svelte 5 error</li>
                <li><strong>Error boundary activation</strong> - Red error panel appearing on the page</li>
                <li><strong>Performance degradation</strong> - Slow response to interactions</li>
                <li><strong>Browser freezing</strong> - Page becoming unresponsive</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📊 Expected Results</h3>
            <div class="status success">
                <strong>✅ Healthy System:</strong> Clean reactive execution, no rapid calls, all tests pass
            </div>
            <div class="status warning">
                <strong>⚠️ Potential Issues:</strong> Rapid successive calls detected, performance warnings
            </div>
            <div class="status error">
                <strong>🚨 Reactivity Problems:</strong> Infinite loops, effect_update_depth_exceeded errors, error boundary activation
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Troubleshooting</h3>
            <p>If you encounter issues:</p>
            <ol>
                <li><strong>Check browser console</strong> for error messages and debug logs</li>
                <li><strong>Monitor network tab</strong> for failed requests or excessive API calls</li>
                <li><strong>Use browser dev tools</strong> to inspect component state</li>
                <li><strong>Export debug data</strong> for detailed analysis</li>
                <li><strong>Clear localStorage</strong> if tests interfere with each other</li>
            </ol>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text.trim()).then(() => {
                // Show feedback
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '✅ Copied!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy text: ', err);
                alert('Failed to copy to clipboard. Please copy manually.');
            });
        }

        // Auto-open the simulate interactions page
        window.addEventListener('load', () => {
            console.log('🚀 Debug Test Runner loaded');
            console.log('📋 Follow the instructions above to run comprehensive reactivity tests');
        });
    </script>
</body>
</html>
