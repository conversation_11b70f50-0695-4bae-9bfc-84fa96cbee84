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
