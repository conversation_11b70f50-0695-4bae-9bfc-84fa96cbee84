<script lang="ts">
	import { theme, type ThemeState } from '$lib/stores/theme';
	import { onMount } from 'svelte';

	// Props
	export let size: 'sm' | 'md' | 'lg' = 'md';
	export let showLabel = true;
	export let variant: 'button' | 'icon' = 'button';

	// Current theme state
	let themeState: ThemeState;
	$: themeState = $theme;

	// Computed properties
	$: currentTheme = themeState?.current || 'light';
	$: preference = themeState?.preference || 'auto';
	$: systemTheme = themeState?.systemTheme || 'light';

	// Accessibility labels
	$: ariaLabel = getAriaLabel(preference, currentTheme, systemTheme);
	$: buttonTitle = getButtonTitle(preference, currentTheme, systemTheme);

	// Initialize theme on mount
	onMount(() => {
		theme.init();
	});

	/**
	 * Handle theme toggle - cycles through light -> dark -> auto
	 */
	function handleToggle() {
		theme.toggle();
	}

	/**
	 * Handle keyboard navigation
	 */
	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handleToggle();
		}
	}

	/**
	 * Get appropriate ARIA label
	 */
	function getAriaLabel(pref: string, current: string, system: string): string {
		switch (pref) {
			case 'light':
				return 'Switch to dark theme';
			case 'dark':
				return 'Switch to auto theme (follows system preference)';
			case 'auto':
				return `Switch to light theme (currently following system: ${system})`;
			default:
				return 'Toggle theme';
		}
	}

	/**
	 * Get appropriate button title
	 */
	function getButtonTitle(pref: string, current: string, system: string): string {
		switch (pref) {
			case 'light':
				return 'Light theme active. Click to switch to dark theme.';
			case 'dark':
				return 'Dark theme active. Click to switch to auto theme.';
			case 'auto':
				return `Auto theme active (${system}). Click to switch to light theme.`;
			default:
				return 'Toggle theme';
		}
	}

	/**
	 * Get icon for current preference
	 */
	function getThemeIcon(pref: string): string {
		switch (pref) {
			case 'light':
				return 'sun';
			case 'dark':
				return 'moon';
			case 'auto':
				return 'auto';
			default:
				return 'sun';
		}
	}
</script>

<button
	class="theme-toggle theme-toggle--{size} theme-toggle--{variant}"
	class:theme-toggle--light={preference === 'light'}
	class:theme-toggle--dark={preference === 'dark'}
	class:theme-toggle--auto={preference === 'auto'}
	on:click={handleToggle}
	on:keydown={handleKeyDown}
	title={buttonTitle}
	aria-label={ariaLabel}
	aria-pressed={preference !== 'auto'}
>
	<div class="toggle-container">
		<!-- Sun icon for light mode -->
		<svg
			class="icon sun-icon"
			class:active={preference === 'light'}
			width={size === 'sm' ? '16' : size === 'lg' ? '24' : '20'}
			height={size === 'sm' ? '16' : size === 'lg' ? '24' : '20'}
			viewBox="0 0 24 24"
			fill="none"
			stroke="currentColor"
			stroke-width="2"
			aria-hidden="true"
		>
			<circle cx="12" cy="12" r="5"/>
			<line x1="12" y1="1" x2="12" y2="3"/>
			<line x1="12" y1="21" x2="12" y2="23"/>
			<line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
			<line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
			<line x1="1" y1="12" x2="3" y2="12"/>
			<line x1="21" y1="12" x2="23" y2="12"/>
			<line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
			<line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
		</svg>

		<!-- Moon icon for dark mode -->
		<svg
			class="icon moon-icon"
			class:active={preference === 'dark'}
			width={size === 'sm' ? '16' : size === 'lg' ? '24' : '20'}
			height={size === 'sm' ? '16' : size === 'lg' ? '24' : '20'}
			viewBox="0 0 24 24"
			fill="none"
			stroke="currentColor"
			stroke-width="2"
			aria-hidden="true"
		>
			<path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
		</svg>

		<!-- Auto icon for auto mode -->
		<svg
			class="icon auto-icon"
			class:active={preference === 'auto'}
			width={size === 'sm' ? '16' : size === 'lg' ? '24' : '20'}
			height={size === 'sm' ? '16' : size === 'lg' ? '24' : '20'}
			viewBox="0 0 24 24"
			fill="none"
			stroke="currentColor"
			stroke-width="2"
			aria-hidden="true"
		>
			<circle cx="12" cy="12" r="3"/>
			<path d="M12 1v6m0 6v6"/>
			<path d="m4.93 4.93 4.24 4.24m5.66 5.66 4.24 4.24"/>
			<path d="M1 12h6m6 0h6"/>
			<path d="m4.93 19.07 4.24-4.24m5.66-5.66 4.24-4.24"/>
		</svg>

		{#if showLabel && variant === 'button'}
			<span class="theme-label">
				{#if preference === 'light'}
					Light
				{:else if preference === 'dark'}
					Dark
				{:else}
					Auto
				{/if}
			</span>
		{/if}
	</div>

	<!-- Status indicator for current theme when in auto mode -->
	{#if preference === 'auto'}
		<span class="auto-indicator" aria-hidden="true">
			<span class="auto-indicator__dot auto-indicator__dot--{currentTheme}"></span>
		</span>
	{/if}
</button>

<style>
	.theme-toggle {
		display: flex;
		align-items: center;
		gap: var(--space-sm);
		background: var(--color-button-secondary-bg);
		color: var(--color-button-secondary-text);
		border: var(--border-width-thin) solid var(--color-button-secondary-border);
		border-radius: var(--border-radius-md);
		cursor: pointer;
		transition: var(--transition-theme);
		font-family: inherit;
		font-weight: var(--font-weight-medium);
		position: relative;
		outline: none;
	}

	/* Size variants */
	.theme-toggle--sm {
		padding: var(--space-xs) var(--space-sm);
		font-size: var(--font-size-xs);
		gap: var(--space-xs);
	}

	.theme-toggle--md {
		padding: var(--space-sm) var(--space-md);
		font-size: var(--font-size-sm);
	}

	.theme-toggle--lg {
		padding: var(--space-md) var(--space-lg);
		font-size: var(--font-size-md);
	}

	/* Variant styles */
	.theme-toggle--icon {
		padding: var(--space-sm);
		border-radius: var(--border-radius-full);
		aspect-ratio: 1;
		justify-content: center;
	}

	.theme-toggle--icon.theme-toggle--sm {
		padding: var(--space-xs);
	}

	.theme-toggle--icon.theme-toggle--lg {
		padding: var(--space-md);
	}

	/* Hover states */
	.theme-toggle:hover {
		background: var(--color-button-secondary-hover-bg);
		color: var(--color-button-secondary-hover-text);
		border-color: var(--color-button-secondary-hover-bg);
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	/* Focus states */
	.theme-toggle:focus-visible {
		box-shadow: 0 0 0 3px var(--color-shadow-focus);
		border-color: var(--color-border-focus);
	}

	/* Active states */
	.theme-toggle:active {
		transform: translateY(0);
		box-shadow: var(--shadow-sm);
	}

	/* Theme state indicators */
	.theme-toggle--light {
		background: var(--color-warning-bg);
		border-color: var(--color-warning-border);
		color: var(--color-warning);
	}

	.theme-toggle--dark {
		background: var(--color-info-bg);
		border-color: var(--color-info-border);
		color: var(--color-info);
	}

	.theme-toggle--auto {
		background: var(--color-success-bg);
		border-color: var(--color-success-border);
		color: var(--color-success);
	}

	.toggle-container {
		display: flex;
		align-items: center;
		gap: var(--space-xs);
		position: relative;
	}

	.icon {
		transition: var(--transition-theme);
		opacity: 0.4;
		transform: scale(0.85);
		flex-shrink: 0;
	}

	.icon.active {
		opacity: 1;
		transform: scale(1);
	}

	.theme-label {
		font-size: inherit;
		font-weight: inherit;
		white-space: nowrap;
	}

	/* Auto mode indicator */
	.auto-indicator {
		position: absolute;
		top: -2px;
		right: -2px;
		width: 8px;
		height: 8px;
		border-radius: var(--border-radius-full);
		background: var(--color-surface-primary);
		border: 1px solid var(--color-border-primary);
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.auto-indicator__dot {
		width: 4px;
		height: 4px;
		border-radius: var(--border-radius-full);
		transition: var(--transition-theme);
	}

	.auto-indicator__dot--light {
		background: var(--color-warning);
	}

	.auto-indicator__dot--dark {
		background: var(--color-info);
	}

	/* High contrast mode support */
	:global(.high-contrast) .theme-toggle {
		border-width: var(--border-width-medium);
		font-weight: var(--font-weight-bold);
	}

	:global(.high-contrast) .icon.active {
		filter: brightness(1.3) contrast(1.2);
	}

	:global(.high-contrast) .auto-indicator {
		border-width: var(--border-width-medium);
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.theme-toggle,
		.icon,
		.auto-indicator__dot {
			transition: none;
		}

		.theme-toggle:hover {
			transform: none;
		}

		.theme-toggle:active {
			transform: none;
		}
	}

	/* Dark theme adjustments */
	:global([data-theme="dark"]) .auto-indicator {
		background: var(--color-surface-secondary);
		border-color: var(--color-border-secondary);
	}
</style>
