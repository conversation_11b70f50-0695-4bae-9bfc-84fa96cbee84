import logger from './logger';

/**
 * Rate limiting service for AI content generation
 */

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  requests: Array<{
    timestamp: number;
    success: boolean;
    userId: number;
  }>;
}

// In-memory store for rate limiting (in production, use Redis)
const rateLimitStore = new Map<string, RateLimitEntry>();

// Rate limit configurations for different operations
const RATE_LIMITS: Record<string, RateLimitConfig> = {
  // AI content generation limits
  'ai_generation_per_user': {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 20, // 20 generations per hour per user
    skipFailedRequests: true
  },
  'ai_generation_per_ip': {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 50, // 50 generations per hour per IP
    skipFailedRequests: true
  },
  'ai_generation_global': {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 generations per minute globally
    skipFailedRequests: true
  },
  
  // Review operations
  'review_operations_per_user': {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 review operations per minute per user
    skipFailedRequests: false
  },
  
  // Bulk operations
  'bulk_operations_per_user': {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 5, // 5 bulk operations per 5 minutes per user
    skipFailedRequests: false
  }
};

/**
 * Check if a request should be rate limited
 */
export function checkRateLimit(
  key: string,
  identifier: string,
  userId?: number
): { allowed: boolean; resetTime?: number; remaining?: number } {
  const config = RATE_LIMITS[key];
  if (!config) {
    logger.warn('Unknown rate limit key', { key });
    return { allowed: true };
  }

  const storeKey = `${key}:${identifier}`;
  const now = Date.now();
  
  // Get or create rate limit entry
  let entry = rateLimitStore.get(storeKey);
  if (!entry) {
    entry = {
      count: 0,
      resetTime: now + config.windowMs,
      requests: []
    };
    rateLimitStore.set(storeKey, entry);
  }

  // Reset if window has expired
  if (now >= entry.resetTime) {
    entry.count = 0;
    entry.resetTime = now + config.windowMs;
    entry.requests = [];
  }

  // Clean up old requests
  entry.requests = entry.requests.filter(req => req.timestamp > now - config.windowMs);

  // Check if limit is exceeded
  const remaining = Math.max(0, config.maxRequests - entry.count);
  const allowed = entry.count < config.maxRequests;

  if (!allowed) {
    logger.warn('Rate limit exceeded', {
      key,
      identifier,
      userId,
      count: entry.count,
      maxRequests: config.maxRequests,
      resetTime: entry.resetTime
    });
  }

  return {
    allowed,
    resetTime: entry.resetTime,
    remaining
  };
}

/**
 * Record a request for rate limiting
 */
export function recordRequest(
  key: string,
  identifier: string,
  success: boolean,
  userId?: number
): void {
  const config = RATE_LIMITS[key];
  if (!config) return;

  // Skip recording based on configuration
  if (config.skipSuccessfulRequests && success) return;
  if (config.skipFailedRequests && !success) return;

  const storeKey = `${key}:${identifier}`;
  const now = Date.now();
  
  let entry = rateLimitStore.get(storeKey);
  if (!entry) {
    entry = {
      count: 0,
      resetTime: now + config.windowMs,
      requests: []
    };
    rateLimitStore.set(storeKey, entry);
  }

  // Reset if window has expired
  if (now >= entry.resetTime) {
    entry.count = 0;
    entry.resetTime = now + config.windowMs;
    entry.requests = [];
  }

  // Record the request
  entry.count++;
  entry.requests.push({
    timestamp: now,
    success,
    userId: userId || 0
  });

  // Limit the number of stored requests to prevent memory issues
  if (entry.requests.length > config.maxRequests * 2) {
    entry.requests = entry.requests.slice(-config.maxRequests);
  }
}

/**
 * Get rate limit status for a key and identifier
 */
export function getRateLimitStatus(
  key: string,
  identifier: string
): { count: number; limit: number; resetTime: number; remaining: number } | null {
  const config = RATE_LIMITS[key];
  if (!config) return null;

  const storeKey = `${key}:${identifier}`;
  const entry = rateLimitStore.get(storeKey);
  
  if (!entry) {
    return {
      count: 0,
      limit: config.maxRequests,
      resetTime: Date.now() + config.windowMs,
      remaining: config.maxRequests
    };
  }

  const now = Date.now();
  
  // Reset if window has expired
  if (now >= entry.resetTime) {
    return {
      count: 0,
      limit: config.maxRequests,
      resetTime: now + config.windowMs,
      remaining: config.maxRequests
    };
  }

  return {
    count: entry.count,
    limit: config.maxRequests,
    resetTime: entry.resetTime,
    remaining: Math.max(0, config.maxRequests - entry.count)
  };
}

/**
 * Clear rate limit for a specific key and identifier
 */
export function clearRateLimit(key: string, identifier: string): void {
  const storeKey = `${key}:${identifier}`;
  rateLimitStore.delete(storeKey);
  
  logger.info('Rate limit cleared', { key, identifier });
}

/**
 * Get usage statistics for monitoring
 */
export function getUsageStats(): {
  totalKeys: number;
  activeWindows: number;
  topUsers: Array<{ identifier: string; count: number; key: string }>;
} {
  const now = Date.now();
  let activeWindows = 0;
  const userCounts: Array<{ identifier: string; count: number; key: string }> = [];

  for (const [storeKey, entry] of rateLimitStore.entries()) {
    if (now < entry.resetTime) {
      activeWindows++;
      const [key, identifier] = storeKey.split(':');
      userCounts.push({ identifier, count: entry.count, key });
    }
  }

  // Sort by count descending and take top 10
  const topUsers = userCounts
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);

  return {
    totalKeys: rateLimitStore.size,
    activeWindows,
    topUsers
  };
}

/**
 * Clean up expired entries (should be called periodically)
 */
export function cleanupExpiredEntries(): number {
  const now = Date.now();
  let cleaned = 0;

  for (const [storeKey, entry] of rateLimitStore.entries()) {
    if (now >= entry.resetTime) {
      rateLimitStore.delete(storeKey);
      cleaned++;
    }
  }

  if (cleaned > 0) {
    logger.info('Cleaned up expired rate limit entries', { count: cleaned });
  }

  return cleaned;
}

/**
 * Middleware function for Express/SvelteKit to check rate limits
 */
export function createRateLimitMiddleware(key: string) {
  return (identifier: string, userId?: number) => {
    const result = checkRateLimit(key, identifier, userId);
    
    if (!result.allowed) {
      const error = new Error('Rate limit exceeded');
      (error as any).status = 429;
      (error as any).resetTime = result.resetTime;
      (error as any).remaining = result.remaining;
      throw error;
    }
    
    return result;
  };
}

// Cleanup expired entries every 5 minutes
setInterval(cleanupExpiredEntries, 5 * 60 * 1000);
