import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { media } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/media - Get all media items
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Get query parameters
    const limit = Number(url.searchParams.get('limit') || '50');
    const offset = Number(url.searchParams.get('offset') || '0');
    const type = url.searchParams.get('type');
    
    // Build query
    let query = db.select().from(media);
    
    // Add type filter if specified
    if (type) {
      query = query.where(eq(media.type, type));
    }
    
    // Execute query with pagination
    const items = await query
      .limit(limit)
      .offset(offset)
      .orderBy(media.createdAt);
    
    return json({
      success: true,
      data: items
    });
  } catch (error) {
    console.error('Error fetching media items:', error);
    
    // Return a more detailed error in development
    const errorMessage = process.env.NODE_ENV === 'development' 
      ? `Failed to fetch media items: ${error.message}` 
      : 'Failed to fetch media items';
    
    return json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
};

// POST /api/media - Create a new media item (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.path || !body.filename || !body.originalName || !body.type || !body.mimeType || !body.size) {
      return json({
        success: false,
        error: 'Required fields missing',
        details: {
          path: !body.path ? 'Path is required' : null,
          filename: !body.filename ? 'Filename is required' : null,
          originalName: !body.originalName ? 'Original name is required' : null,
          type: !body.type ? 'Type is required' : null,
          mimeType: !body.mimeType ? 'MIME type is required' : null,
          size: !body.size ? 'Size is required' : null
        }
      }, { status: 400 });
    }
    
    // Insert new media item into the database
    const result = await db.insert(media).values({
      filename: body.filename,
      originalName: body.originalName,
      path: body.path,
      thumbnailPath: body.thumbnailPath || null,
      type: body.type,
      mimeType: body.mimeType,
      size: body.size,
      width: body.width || null,
      height: body.height || null,
      alt: body.alt || null,
      caption: body.caption || null,
      authorId: locals.user.id
    }).returning();
    
    return json({
      success: true,
      data: result[0]
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating media item:', error);
    return json({
      success: false,
      error: 'Failed to create media item',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
};
