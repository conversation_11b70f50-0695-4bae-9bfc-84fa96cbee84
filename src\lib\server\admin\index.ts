import AdminJ<PERSON> from 'adminjs';
import { Database, Resource } from '@adminjs/sql';
import express from 'express';
import AdminJSExpress from '@adminjs/express';
import session from 'express-session';
import formidable from 'express-formidable';
import { db } from '../db';
import * as schema from '../db/schema';
import { authenticateUser } from '../auth';

// Register the SQL adapter
AdminJS.registerAdapter({ Database, Resource });

// Create AdminJS instance
const admin = new AdminJS({
  databases: [db],
  rootPath: '/admin',
  branding: {
    companyName: 'Finn Wolfhard Fan Club Admin',
    logo: '/images/logo.png',
    favicon: '/favicon.png',
    withMadeWithLove: false,
  },
  dashboard: {
    handler: async () => {
      return { message: 'Welcome to Finn Wolfhard Fan Club Admin Panel' };
    },
    component: AdminJS.bundle('./components/dashboard'),
  },
  resources: [
    {
      resource: { model: schema.users, client: db },
      options: {
        navigation: {
          name: 'User Management',
          icon: 'User',
        },
        properties: {
          passwordHash: {
            isVisible: false,
          },
          preferences: {
            type: 'mixed',
          },
        },
        actions: {
          new: {
            before: async (request: any) => {
              if (request.payload.password) {
                // In a real app, you'd hash the password here
                request.payload.passwordHash = request.payload.password;
                delete request.payload.password;
              }
              return request;
            },
          },
        },
      },
    },
    {
      resource: { model: schema.news, client: db },
      options: {
        navigation: {
          name: 'Content',
          icon: 'Document',
        },
      },
    },
    {
      resource: { model: schema.gallery, client: db },
      options: {
        navigation: {
          name: 'Content',
          icon: 'Document',
        },
      },
    },
    {
      resource: { model: schema.messages, client: db },
      options: {
        navigation: {
          name: 'Community',
          icon: 'Chat',
        },
      },
    },
    {
      resource: { model: schema.replies, client: db },
      options: {
        navigation: {
          name: 'Community',
          icon: 'Chat',
        },
      },
    },
    {
      resource: { model: schema.comments, client: db },
      options: {
        navigation: {
          name: 'Community',
          icon: 'Chat',
        },
      },
    },
    {
      resource: { model: schema.siteSettings, client: db },
      options: {
        navigation: {
          name: 'Settings',
          icon: 'Settings',
        },
        properties: {
          settings: {
            type: 'mixed',
          },
        },
      },
    },
    {
      resource: { model: schema.messageOfTheDay, client: db },
      options: {
        navigation: {
          name: 'Settings',
          icon: 'Settings',
        },
      },
    },
  ],
});

// Authentication for AdminJS
const authenticate = async (email: string, password: string) => {
  const user = await authenticateUser(email, password);
  if (user && user.role === 'admin') {
    return {
      email: user.email,
      role: user.role,
    };
  }
  return null;
};

// Create Express app for AdminJS
const app = express();
app.use(formidable());

// Create AdminJS router with authentication
const router = AdminJSExpress.buildAuthenticatedRouter(
  admin,
  {
    authenticate,
    cookieName: 'adminjs',
    cookiePassword: 'some-secret-password-used-to-secure-cookie',
  },
  null,
  {
    resave: false,
    saveUninitialized: true,
    secret: 'session-secret',
    cookie: {
      httpOnly: process.env.NODE_ENV === 'production',
      secure: process.env.NODE_ENV === 'production',
    },
    name: 'adminjs',
  }
);

export { admin, app, router };
