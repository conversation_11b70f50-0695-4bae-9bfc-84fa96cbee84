// Form validation utilities for FWFC registration

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface FieldValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => ValidationResult;
}

// Email validation regex
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Username validation regex (alphanumeric, underscore, hyphen, 3-20 chars)
export const USERNAME_REGEX = /^[a-zA-Z0-9_-]{3,20}$/;

// Password validation (at least 8 chars, 1 uppercase, 1 lowercase, 1 number)
export const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;

// Name validation (letters, spaces, hyphens, apostrophes)
export const NAME_REGEX = /^[a-zA-Z\s'-]{2,50}$/;

/**
 * Validate a single field value
 */
export function validateField(value: string, rules: FieldValidation): ValidationResult {
  // Check required
  if (rules.required && (!value || value.trim().length === 0)) {
    return { isValid: false, error: 'This field is required' };
  }

  // If field is empty and not required, it's valid
  if (!value || value.trim().length === 0) {
    return { isValid: true };
  }

  // Check minimum length
  if (rules.minLength && value.length < rules.minLength) {
    return { 
      isValid: false, 
      error: `Must be at least ${rules.minLength} characters long` 
    };
  }

  // Check maximum length
  if (rules.maxLength && value.length > rules.maxLength) {
    return { 
      isValid: false, 
      error: `Must be no more than ${rules.maxLength} characters long` 
    };
  }

  // Check pattern
  if (rules.pattern && !rules.pattern.test(value)) {
    return { isValid: false, error: 'Invalid format' };
  }

  // Check custom validation
  if (rules.custom) {
    return rules.custom(value);
  }

  return { isValid: true };
}

/**
 * Validate username
 */
export function validateUsername(username: string): ValidationResult {
  return validateField(username, {
    required: true,
    minLength: 3,
    maxLength: 20,
    pattern: USERNAME_REGEX,
    custom: (value) => {
      if (!USERNAME_REGEX.test(value)) {
        return {
          isValid: false,
          error: 'Username can only contain letters, numbers, underscores, and hyphens'
        };
      }
      return { isValid: true };
    }
  });
}

/**
 * Validate email address
 */
export function validateEmail(email: string): ValidationResult {
  return validateField(email, {
    required: true,
    pattern: EMAIL_REGEX,
    custom: (value) => {
      if (!EMAIL_REGEX.test(value)) {
        return {
          isValid: false,
          error: 'Please enter a valid email address'
        };
      }
      return { isValid: true };
    }
  });
}

/**
 * Validate display name
 */
export function validateDisplayName(displayName: string): ValidationResult {
  return validateField(displayName, {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: NAME_REGEX,
    custom: (value) => {
      if (!NAME_REGEX.test(value)) {
        return {
          isValid: false,
          error: 'Name can only contain letters, spaces, hyphens, and apostrophes'
        };
      }
      return { isValid: true };
    }
  });
}

/**
 * Validate password
 */
export function validatePassword(password: string): ValidationResult {
  return validateField(password, {
    required: true,
    minLength: 8,
    custom: (value) => {
      if (!PASSWORD_REGEX.test(value)) {
        return {
          isValid: false,
          error: 'Password must contain at least 8 characters with uppercase, lowercase, and number'
        };
      }
      return { isValid: true };
    }
  });
}

/**
 * Validate password confirmation
 */
export function validatePasswordConfirmation(password: string, confirmPassword: string): ValidationResult {
  if (!confirmPassword) {
    return { isValid: false, error: 'Please confirm your password' };
  }
  
  if (password !== confirmPassword) {
    return { isValid: false, error: 'Passwords do not match' };
  }
  
  return { isValid: true };
}

/**
 * Validate entire registration form
 */
export interface RegistrationFormData {
  username: string;
  displayName: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
  acceptPrivacy: boolean;
}

export interface FormValidationErrors {
  username?: string;
  displayName?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  acceptTerms?: string;
  acceptPrivacy?: string;
}

export function validateRegistrationForm(data: RegistrationFormData): {
  isValid: boolean;
  errors: FormValidationErrors;
} {
  const errors: FormValidationErrors = {};

  // Validate username
  const usernameResult = validateUsername(data.username);
  if (!usernameResult.isValid) {
    errors.username = usernameResult.error;
  }

  // Validate display name
  const displayNameResult = validateDisplayName(data.displayName);
  if (!displayNameResult.isValid) {
    errors.displayName = displayNameResult.error;
  }

  // Validate email
  const emailResult = validateEmail(data.email);
  if (!emailResult.isValid) {
    errors.email = emailResult.error;
  }

  // Validate password
  const passwordResult = validatePassword(data.password);
  if (!passwordResult.isValid) {
    errors.password = passwordResult.error;
  }

  // Validate password confirmation
  const confirmPasswordResult = validatePasswordConfirmation(data.password, data.confirmPassword);
  if (!confirmPasswordResult.isValid) {
    errors.confirmPassword = confirmPasswordResult.error;
  }

  // Validate terms acceptance
  if (!data.acceptTerms) {
    errors.acceptTerms = 'You must accept the terms of service';
  }

  // Validate privacy policy acceptance
  if (!data.acceptPrivacy) {
    errors.acceptPrivacy = 'You must accept the privacy policy';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Debounce function for real-time validation
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
