import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { scheduledContent, users, news, gallery, comments, messages, contentAuthorship, auditLogs } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// POST /api/admin/scheduled-content/[id]/publish - Publish scheduled content immediately
export const POST: RequestHandler = async ({ params, locals, getClientAddress }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const itemId = parseInt(params.id);
    if (isNaN(itemId)) {
      return json({
        success: false,
        error: 'Invalid item ID'
      }, { status: 400 });
    }

    // Get the scheduled content item
    const scheduledItem = await db.select()
      .from(scheduledContent)
      .where(eq(scheduledContent.id, itemId))
      .limit(1);

    if (scheduledItem.length === 0) {
      return json({
        success: false,
        error: 'Scheduled content item not found'
      }, { status: 404 });
    }

    const item = scheduledItem[0];

    // Check if item can be published
    if (item.status === 'published') {
      return json({
        success: false,
        error: 'Content has already been published'
      }, { status: 400 });
    }

    if (item.status === 'cancelled') {
      return json({
        success: false,
        error: 'Cannot publish cancelled content'
      }, { status: 400 });
    }

    // Parse content data
    let contentData: any;
    try {
      contentData = JSON.parse(item.contentData);
    } catch (error) {
      return json({
        success: false,
        error: 'Invalid content data format'
      }, { status: 400 });
    }

    // Check if target user exists
    const targetUser = await db.select()
      .from(users)
      .where(eq(users.id, item.asUserId))
      .limit(1);

    if (targetUser.length === 0) {
      return json({
        success: false,
        error: 'Target user not found'
      }, { status: 404 });
    }

    let contentId: number;
    let result: any;

    try {
      // Publish content based on type
      switch (item.contentType) {
        case 'news':
          result = await db.insert(news).values({
            title: contentData.title,
            content: contentData.content,
            imageUrl: contentData.imageUrl || null,
            authorId: item.asUserId,
            published: true
          }).returning();
          contentId = result[0].id;
          break;

        case 'gallery':
          result = await db.insert(gallery).values({
            title: contentData.title,
            description: contentData.description || null,
            imageUrl: contentData.imageUrl,
            thumbnailUrl: contentData.thumbnailUrl || contentData.imageUrl,
            authorId: item.asUserId,
            published: true
          }).returning();
          contentId = result[0].id;
          break;

        case 'comment':
          result = await db.insert(comments).values({
            userId: item.asUserId,
            content: contentData.content,
            itemType: contentData.itemType as 'news' | 'gallery',
            itemId: contentData.itemId,
            approved: true
          }).returning();
          contentId = result[0].id;
          break;

        case 'message':
          result = await db.insert(messages).values({
            userId: item.asUserId,
            content: contentData.content,
            approved: true
          }).returning();
          contentId = result[0].id;
          break;

        default:
          return json({
            success: false,
            error: 'Unsupported content type'
          }, { status: 400 });
      }

      // Record content authorship
      await db.insert(contentAuthorship).values({
        contentType: item.contentType,
        contentId,
        actualAuthorId: locals.user.id,
        displayAuthorId: item.asUserId,
        isSimulated: true
      });

      // Update scheduled content status
      await db.update(scheduledContent)
        .set({
          status: 'published',
          publishedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })
        .where(eq(scheduledContent.id, itemId));

      // Update user's last active time
      await db.update(users)
        .set({ lastActiveAt: new Date().toISOString() })
        .where(eq(users.id, item.asUserId));

      // Log admin action
      await logAdminAction(
        locals.user.id,
        'publish_scheduled_content',
        'scheduled_content',
        itemId,
        item.asUserId,
        {
          contentType: item.contentType,
          contentId,
          originalScheduledFor: item.scheduledFor,
          publishedAt: new Date().toISOString()
        },
        getClientAddress()
      );

      logger.info('Scheduled content published immediately', {
        adminUser: locals.user.username,
        targetUser: targetUser[0].username,
        contentType: item.contentType,
        contentId,
        scheduledItemId: itemId
      });

      return json({
        success: true,
        data: {
          contentId,
          contentType: item.contentType,
          publishedContent: result[0]
        },
        message: `${item.contentType.charAt(0).toUpperCase() + item.contentType.slice(1)} published successfully as ${targetUser[0].displayName}`
      });

    } catch (publishError) {
      // Update scheduled content with error
      await db.update(scheduledContent)
        .set({
          status: 'failed',
          errorMessage: publishError instanceof Error ? publishError.message : 'Unknown error',
          updatedAt: new Date().toISOString()
        })
        .where(eq(scheduledContent.id, itemId));

      logger.error('Error publishing scheduled content:', publishError);
      
      return json({
        success: false,
        error: 'Failed to publish content'
      }, { status: 500 });
    }

  } catch (error) {
    logger.error('Error in publish scheduled content endpoint:', error);
    return json({
      success: false,
      error: 'Failed to publish scheduled content'
    }, { status: 500 });
  }
};

// Helper function to log admin actions
async function logAdminAction(
  adminUserId: number,
  action: string,
  targetType: string,
  targetId: number | null,
  targetUserId: number | null,
  details: any,
  ipAddress: string
) {
  try {
    await db.insert(auditLogs).values({
      adminUserId,
      action,
      targetType,
      targetId,
      targetUserId,
      details: JSON.stringify(details),
      ipAddress
    });
  } catch (error) {
    logger.error('Failed to log admin action:', error);
  }
}
