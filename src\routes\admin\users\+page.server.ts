import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	// Check if user is logged in and has admin/moderator role
	if (!locals.user || (locals.user.role !== 'admin' && locals.user.role !== 'moderator')) {
		// Redirect to login page if not authenticated as admin/moderator
		throw redirect(302, '/login');
	}
	
	// Return user info for the page
	return {
		user: locals.user
	};
};
