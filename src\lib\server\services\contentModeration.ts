import logger from './logger';

/**
 * Content moderation service for AI-generated content
 */

// Inappropriate content patterns
const INAPPROPRIATE_PATTERNS = [
  // Explicit content
  /\b(explicit|sexual|adult|nsfw)\b/i,
  
  // Violence and harmful content
  /\b(violence|violent|harm|hurt|kill|death|suicide)\b/i,
  
  // Hate speech and discrimination
  /\b(hate|racist|sexist|homophobic|discrimination)\b/i,
  
  // Personal information
  /\b(\d{3}-\d{2}-\d{4}|\d{3}\.\d{2}\.\d{4})\b/, // SSN patterns
  /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email patterns
  /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/, // Credit card patterns
  
  // Spam and promotional content
  /\b(buy now|click here|limited time|act now|free money)\b/i,
  
  // Off-topic content (not related to <PERSON>)
  /\b(politics|political|election|vote|democrat|republican)\b/i
];

// Finn <PERSON> related keywords (positive indicators)
const FINN_WOLFHARD_KEYWORDS = [
  'finn wolfhard', 'stranger things', 'mike wheeler', 'it movie', 'it chapter',
  'the aubreys', 'calpurnia', 'ghostbusters', 'afterlife', 'turning red',
  'when you finish saving the world', 'the goldfinch', 'dog days',
  'carmen sandiego', 'netflix', 'hawkins', 'upside down'
];

// Profanity filter (basic list)
const PROFANITY_PATTERNS = [
  /\b(damn|hell|crap|stupid|idiot|moron)\b/i, // Mild profanity
  // Add more patterns as needed, but keep it appropriate for a fan community
];

export interface ModerationResult {
  isAppropriate: boolean;
  confidence: number;
  flags: string[];
  suggestions?: string[];
  blockedReasons?: string[];
}

/**
 * Moderate content for appropriateness
 */
export function moderateContent(content: string, contentType: string = 'general'): ModerationResult {
  const flags: string[] = [];
  const blockedReasons: string[] = [];
  const suggestions: string[] = [];
  let confidence = 100;

  // Check for inappropriate patterns
  for (const pattern of INAPPROPRIATE_PATTERNS) {
    if (pattern.test(content)) {
      flags.push('inappropriate_content');
      blockedReasons.push('Contains inappropriate or harmful content');
      confidence -= 30;
      break;
    }
  }

  // Check for profanity
  for (const pattern of PROFANITY_PATTERNS) {
    if (pattern.test(content)) {
      flags.push('profanity');
      suggestions.push('Consider using more family-friendly language');
      confidence -= 15;
      break;
    }
  }

  // Check for personal information
  const emailMatch = content.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/);
  if (emailMatch) {
    flags.push('personal_info');
    blockedReasons.push('Contains personal information (email address)');
    confidence -= 40;
  }

  const phoneMatch = content.match(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/);
  if (phoneMatch) {
    flags.push('personal_info');
    blockedReasons.push('Contains personal information (phone number)');
    confidence -= 40;
  }

  // Check for spam patterns
  const spamPatterns = /\b(buy now|click here|limited time|act now|free money|visit our website)\b/i;
  if (spamPatterns.test(content)) {
    flags.push('spam');
    blockedReasons.push('Contains promotional or spam-like content');
    confidence -= 25;
  }

  // Check relevance to Finn Wolfhard (positive indicator)
  const hasRelevantKeywords = FINN_WOLFHARD_KEYWORDS.some(keyword => 
    content.toLowerCase().includes(keyword.toLowerCase())
  );

  if (!hasRelevantKeywords && contentType !== 'comment' && contentType !== 'message') {
    flags.push('off_topic');
    suggestions.push('Consider adding more content related to Finn Wolfhard or his work');
    confidence -= 10;
  }

  // Check content length appropriateness
  if (content.length < 10) {
    flags.push('too_short');
    suggestions.push('Content might be too short to be meaningful');
    confidence -= 5;
  }

  if (content.length > 5000) {
    flags.push('too_long');
    suggestions.push('Content might be too long for this format');
    confidence -= 5;
  }

  // Check for repetitive content
  const words = content.toLowerCase().split(/\s+/);
  const uniqueWords = new Set(words);
  const repetitionRatio = uniqueWords.size / words.length;
  
  if (repetitionRatio < 0.3 && words.length > 20) {
    flags.push('repetitive');
    suggestions.push('Content appears to be repetitive');
    confidence -= 15;
  }

  // Determine if content is appropriate
  const isAppropriate = blockedReasons.length === 0 && confidence >= 50;

  // Ensure confidence is within bounds
  confidence = Math.max(0, Math.min(100, confidence));

  const result: ModerationResult = {
    isAppropriate,
    confidence,
    flags,
    suggestions: suggestions.length > 0 ? suggestions : undefined,
    blockedReasons: blockedReasons.length > 0 ? blockedReasons : undefined
  };

  // Log moderation results for monitoring
  logger.info('Content moderation completed', {
    contentLength: content.length,
    contentType,
    isAppropriate,
    confidence,
    flags,
    hasBlockedReasons: blockedReasons.length > 0
  });

  return result;
}

/**
 * Check if content contains personal information
 */
export function containsPersonalInfo(content: string): boolean {
  const personalInfoPatterns = [
    /\b\d{3}-\d{2}-\d{4}\b/, // SSN
    /\b\d{3}\.\d{2}\.\d{4}\b/, // SSN with dots
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email
    /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/, // Phone number
    /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/, // Credit card
    /\b\d{1,5}\s\w+\s(street|st|avenue|ave|road|rd|drive|dr|lane|ln|boulevard|blvd)\b/i // Address
  ];

  return personalInfoPatterns.some(pattern => pattern.test(content));
}

/**
 * Check if content is spam-like
 */
export function isSpamLike(content: string): boolean {
  const spamIndicators = [
    /\b(buy now|click here|limited time|act now|free money|visit our website)\b/i,
    /\b(amazing deal|incredible offer|don't miss out|hurry up|last chance)\b/i,
    /\b(make money|earn cash|work from home|get rich quick)\b/i,
    /\b(viagra|cialis|pharmacy|prescription|medication)\b/i
  ];

  // Check for excessive capitalization
  const capsRatio = (content.match(/[A-Z]/g) || []).length / content.length;
  if (capsRatio > 0.5 && content.length > 20) {
    return true;
  }

  // Check for excessive punctuation
  const punctuationRatio = (content.match(/[!?]{2,}/g) || []).length;
  if (punctuationRatio > 3) {
    return true;
  }

  return spamIndicators.some(pattern => pattern.test(content));
}

/**
 * Get content safety score (0-100, higher is safer)
 */
export function getContentSafetyScore(content: string, contentType: string = 'general'): number {
  const moderation = moderateContent(content, contentType);
  return moderation.confidence;
}

/**
 * Sanitize content by removing or replacing inappropriate elements
 */
export function sanitizeContent(content: string): string {
  let sanitized = content;

  // Remove email addresses
  sanitized = sanitized.replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL REMOVED]');

  // Remove phone numbers
  sanitized = sanitized.replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '[PHONE REMOVED]');

  // Remove potential SSNs
  sanitized = sanitized.replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN REMOVED]');
  sanitized = sanitized.replace(/\b\d{3}\.\d{2}\.\d{4}\b/g, '[SSN REMOVED]');

  // Remove potential credit card numbers
  sanitized = sanitized.replace(/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, '[CARD NUMBER REMOVED]');

  // Reduce excessive punctuation
  sanitized = sanitized.replace(/[!]{3,}/g, '!!');
  sanitized = sanitized.replace(/[?]{3,}/g, '??');

  // Reduce excessive capitalization (convert to sentence case)
  sanitized = sanitized.replace(/\b[A-Z]{4,}\b/g, (match) => {
    return match.charAt(0) + match.slice(1).toLowerCase();
  });

  return sanitized.trim();
}
