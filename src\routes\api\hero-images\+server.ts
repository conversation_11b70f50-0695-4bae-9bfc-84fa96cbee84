import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { heroImages } from '$lib/server/db/schema';
import { eq, desc } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/hero-images - Get hero images (active only for public, all for admin)
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Get query parameters
    const all = url.searchParams.get('all') === 'true';
    
    // Check if user is admin and requesting all items
    const isAdmin = locals.user && locals.user.role === 'admin';
    const showAll = all && isAdmin;
    
    // Build query
    let query = db.select().from(heroImages);
    
    // If not admin or not requesting all, only show active items
    if (!showAll) {
      query = query.where(eq(heroImages.active, true));
    }
    
    // Apply ordering
    const items = await query.orderBy(heroImages.sortOrder, desc(heroImages.createdAt));
    
    return json({
      success: true,
      data: items
    });
  } catch (error) {
    console.error('Error fetching hero images:', error);
    return json({
      success: false,
      error: 'Failed to fetch hero images'
    }, { status: 500 });
  }
};

// POST /api/hero-images - Create a new hero image (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.title || !body.imageUrl) {
      return json({
        success: false,
        error: 'Title and image URL are required'
      }, { status: 400 });
    }
    
    // If this hero image is being set as active, deactivate all others
    if (body.active) {
      await db.update(heroImages)
        .set({ active: false })
        .where(eq(heroImages.active, true));
    }
    
    // Insert new hero image into the database
    const result = await db.insert(heroImages).values({
      title: body.title,
      subtitle: body.subtitle || null,
      imageUrl: body.imageUrl,
      active: body.active || false,
      sortOrder: body.sortOrder || 0,
      authorId: locals.user.id
    }).returning();
    
    return json({
      success: true,
      data: result[0]
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating hero image:', error);
    return json({
      success: false,
      error: 'Failed to create hero image'
    }, { status: 500 });
  }
};
