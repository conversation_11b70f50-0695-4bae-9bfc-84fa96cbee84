<script>
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import ImageUploader from '$lib/components/admin/ImageUploader.svelte';

	// State variables
	let settings = {};
	let loading = true;
	let saving = false;
	let error = null;
	let successMessage = null;
	let hasChanges = false;
	let originalSettings = {};

	// Auto-save functionality
	let autoSaveTimeout = null;
	let lastSaved = null;

	// Fetch site settings from API
	async function fetchSettings() {
		loading = true;
		error = null;

		try {
			const response = await fetch('/api/site-settings');
			const data = await response.json();

			if (data.success) {
				settings = data.data;
				originalSettings = JSON.parse(JSON.stringify(data.data));
				console.log('Site settings loaded:', settings);
			} else {
				error = data.error || 'Failed to load site settings';
			}
		} catch (err) {
			console.error('Error fetching site settings:', err);
			error = 'Failed to load site settings';
		} finally {
			loading = false;
		}
	}

	// Save settings to API
	async function saveSettings(showMessage = true) {
		if (!hasChanges) return;

		saving = true;
		error = null;
		successMessage = null;

		try {
			// Flatten settings for API
			const settingsToUpdate = [];
			Object.values(settings).flat().forEach(setting => {
				const original = Object.values(originalSettings).flat().find(s => s.setting_key === setting.setting_key);
				if (!original || original.setting_value !== setting.setting_value) {
					settingsToUpdate.push({
						key: setting.setting_key,
						value: setting.setting_value
					});
				}
			});

			if (settingsToUpdate.length === 0) {
				hasChanges = false;
				saving = false;
				return;
			}

			const response = await fetch('/api/site-settings', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ settings: settingsToUpdate })
			});

			const data = await response.json();

			if (data.success) {
				originalSettings = JSON.parse(JSON.stringify(settings));
				hasChanges = false;
				lastSaved = new Date();
				if (showMessage) {
					successMessage = 'Settings saved successfully!';
					setTimeout(() => successMessage = null, 3000);
				}
			} else {
				error = data.error || 'Failed to save settings';
			}
		} catch (err) {
			console.error('Error saving settings:', err);
			error = 'Failed to save settings';
		} finally {
			saving = false;
		}
	}

	// Handle input changes
	function handleInputChange(categoryKey, settingIndex, value) {
		settings[categoryKey][settingIndex].setting_value = value;
		hasChanges = true;
		
		// Auto-save after 2 seconds of no changes
		if (autoSaveTimeout) {
			clearTimeout(autoSaveTimeout);
		}
		autoSaveTimeout = setTimeout(() => {
			saveSettings(false);
		}, 2000);
	}

	// Handle image upload
	function handleImageUpload(settingKey, imageUrl) {
		// Find and update the setting
		Object.keys(settings).forEach(categoryKey => {
			const settingIndex = settings[categoryKey].findIndex(s => s.setting_key === settingKey);
			if (settingIndex !== -1) {
				handleInputChange(categoryKey, settingIndex, imageUrl);
			}
		});
	}

	// Preview logo change
	function previewLogo(imageUrl) {
		if (browser) {
			const logoElements = document.querySelectorAll('header .logo img');
			logoElements.forEach(img => {
				img.src = imageUrl;
			});
		}
	}

	// Reset to original values
	function resetSettings() {
		settings = JSON.parse(JSON.stringify(originalSettings));
		hasChanges = false;
		successMessage = 'Settings reset to last saved values';
		setTimeout(() => successMessage = null, 3000);
	}

	// Load settings on mount
	onMount(() => {
		fetchSettings();
	});

	// Prevent navigation if there are unsaved changes
	$: {
		if (browser && hasChanges) {
			const handleBeforeUnload = (e) => {
				e.preventDefault();
				e.returnValue = '';
			};
			window.addEventListener('beforeunload', handleBeforeUnload);
		}
	}
</script>

<svelte:head>
	<title>Site Settings - Admin</title>
</svelte:head>

<div class="settings-container">
	<div class="header">
		<h1>Site Settings</h1>
		<div class="header-actions">
			{#if hasChanges}
				<span class="unsaved-indicator">Unsaved changes</span>
			{/if}
			{#if lastSaved}
				<span class="last-saved">Last saved: {lastSaved.toLocaleTimeString()}</span>
			{/if}
			<button 
				class="btn secondary" 
				on:click={resetSettings}
				disabled={!hasChanges || saving}
			>
				Reset
			</button>
			<button 
				class="btn primary" 
				on:click={() => saveSettings(true)}
				disabled={!hasChanges || saving}
			>
				{saving ? 'Saving...' : 'Save Settings'}
			</button>
		</div>
	</div>

	{#if error}
		<div class="error-message">
			<p>{error}</p>
			<button on:click={() => error = null}>×</button>
		</div>
	{/if}

	{#if successMessage}
		<div class="success-message">
			<p>{successMessage}</p>
			<button on:click={() => successMessage = null}>×</button>
		</div>
	{/if}

	{#if loading}
		<div class="loading">
			<p>Loading site settings...</p>
		</div>
	{:else}
		<div class="settings-grid">
			{#each Object.entries(settings) as [categoryKey, categorySettings]}
				<div class="settings-category">
					<h2>{categoryKey.charAt(0).toUpperCase() + categoryKey.slice(1)}</h2>
					
					<div class="settings-list">
						{#each categorySettings as setting, index}
							<div class="setting-item">
								<label for={setting.setting_key}>
									{setting.display_name}
									{#if setting.description}
										<span class="description">{setting.description}</span>
									{/if}
								</label>
								
								{#if setting.setting_type === 'image'}
									<div class="image-setting">
										<div class="current-image">
											<img src={setting.setting_value} alt={setting.display_name} />
										</div>
										<ImageUploader
											on:upload={(e) => handleImageUpload(setting.setting_key, e.detail.url)}
											accept="image/*"
											maxSize={5242880}
											buttonText="Upload New Image"
										/>
									</div>
								{:else if setting.setting_type === 'textarea'}
									<textarea
										id={setting.setting_key}
										bind:value={setting.setting_value}
										on:input={(e) => handleInputChange(categoryKey, index, e.target.value)}
										rows="4"
									></textarea>
								{:else if setting.setting_type === 'boolean'}
									<label class="checkbox-label">
										<input
											type="checkbox"
											id={setting.setting_key}
											checked={setting.setting_value === '1' || setting.setting_value === 'true'}
											on:change={(e) => handleInputChange(categoryKey, index, e.target.checked ? '1' : '0')}
										/>
										<span class="checkbox-text">Enable</span>
									</label>
								{:else if setting.setting_type === 'number'}
									<input
										type="number"
										id={setting.setting_key}
										bind:value={setting.setting_value}
										on:input={(e) => handleInputChange(categoryKey, index, e.target.value)}
									/>
								{:else if setting.setting_type === 'email'}
									<input
										type="email"
										id={setting.setting_key}
										bind:value={setting.setting_value}
										on:input={(e) => handleInputChange(categoryKey, index, e.target.value)}
									/>
								{:else if setting.setting_type === 'url'}
									<input
										type="url"
										id={setting.setting_key}
										bind:value={setting.setting_value}
										on:input={(e) => handleInputChange(categoryKey, index, e.target.value)}
										placeholder="https://"
									/>
								{:else}
									<input
										type="text"
										id={setting.setting_key}
										bind:value={setting.setting_value}
										on:input={(e) => handleInputChange(categoryKey, index, e.target.value)}
									/>
								{/if}
							</div>
						{/each}
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>

<style>
	.settings-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 1px solid var(--theme-border);
	}

	.header h1 {
		margin: 0;
		color: var(--theme-text-primary);
	}

	.header-actions {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.unsaved-indicator {
		color: var(--theme-accent-warning);
		font-weight: bold;
		font-size: 0.9rem;
	}

	.last-saved {
		color: var(--theme-text-muted);
		font-size: 0.8rem;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		gap: 0.5rem;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border: 1px solid var(--theme-accent-primary);
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
	}

	.btn.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border: 1px solid var(--theme-border);
	}

	.btn.secondary:hover:not(:disabled) {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
	}

	.error-message {
		background-color: var(--theme-accent-danger);
		color: white;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border: 1px solid var(--theme-accent-danger);
	}

	.success-message {
		background-color: var(--theme-accent-success);
		color: white;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border: 1px solid var(--theme-accent-success);
	}

	.loading {
		text-align: center;
		padding: 3rem;
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		color: var(--theme-text-primary);
	}

	.settings-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
		gap: 2rem;
	}

	.settings-category {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 8px;
		padding: 1.5rem;
		box-shadow: 0 2px 4px var(--theme-shadow);
	}

	.settings-category h2 {
		margin: 0 0 1.5rem 0;
		color: var(--theme-text-primary);
		font-size: 1.3rem;
		padding-bottom: 0.5rem;
		border-bottom: 1px solid var(--theme-border);
	}

	.settings-list {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.setting-item {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.setting-item label {
		font-weight: bold;
		color: var(--theme-text-primary);
		font-size: 0.95rem;
	}

	.description {
		display: block;
		font-weight: normal;
		color: var(--theme-text-secondary);
		font-size: 0.85rem;
		margin-top: 0.25rem;
		font-style: italic;
	}

	input[type="text"],
	input[type="email"],
	input[type="url"],
	input[type="number"],
	textarea {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid var(--theme-input-border);
		border-radius: 4px;
		font-size: 1rem;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		transition: border-color 0.2s ease;
	}

	input:focus,
	textarea:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
	}

	.checkbox-label {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		cursor: pointer;
	}

	.checkbox-label input[type="checkbox"] {
		width: auto;
	}

	.checkbox-text {
		color: var(--theme-text-primary);
	}

	.image-setting {
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.current-image {
		max-width: 200px;
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		overflow: hidden;
		background-color: var(--theme-bg-secondary);
	}

	.current-image img {
		width: 100%;
		height: auto;
		display: block;
	}

	@media (max-width: 768px) {
		.settings-container {
			padding: 1rem;
		}

		.header {
			flex-direction: column;
			align-items: flex-start;
			gap: 1rem;
		}

		.header-actions {
			flex-wrap: wrap;
		}

		.settings-grid {
			grid-template-columns: 1fr;
		}
	}
</style>
