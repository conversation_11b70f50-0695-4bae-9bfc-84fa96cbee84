import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

describe('Reactivity Fix - Message Auto-Clear', () => {
  let setTimeoutSpy: any;
  let clearTimeoutSpy: any;

  beforeEach(() => {
    // Mock setTimeout and clearTimeout
    setTimeoutSpy = vi.spyOn(global, 'setTimeout');
    clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  describe('Success Message Management', () => {
    it('should set success message and auto-clear after 5 seconds', () => {
      let successMessage = '';
      let successMessageTimeout: NodeJS.Timeout | null = null;

      // Simulate the setSuccessMessage function
      function setSuccessMessage(message: string) {
        successMessage = message;
        if (successMessageTimeout) {
          clearTimeout(successMessageTimeout);
        }
        successMessageTimeout = setTimeout(() => {
          successMessage = '';
          successMessageTimeout = null;
        }, 5000);
      }

      // Test setting a success message
      setSuccessMessage('Test success message');
      
      expect(successMessage).toBe('Test success message');
      expect(setTimeoutSpy).toHaveBeenCalledWith(expect.any(Function), 5000);

      // Fast-forward time by 5 seconds
      vi.advanceTimersByTime(5000);

      expect(successMessage).toBe('');
      expect(successMessageTimeout).toBeNull();
    });

    it('should clear previous timeout when setting new success message', () => {
      let successMessage = '';
      let successMessageTimeout: NodeJS.Timeout | null = null;

      function setSuccessMessage(message: string) {
        successMessage = message;
        if (successMessageTimeout) {
          clearTimeout(successMessageTimeout);
        }
        successMessageTimeout = setTimeout(() => {
          successMessage = '';
          successMessageTimeout = null;
        }, 5000);
      }

      // Set first message
      setSuccessMessage('First message');
      const firstTimeout = successMessageTimeout;

      // Set second message before first timeout expires
      vi.advanceTimersByTime(2000); // Only 2 seconds
      setSuccessMessage('Second message');

      expect(clearTimeoutSpy).toHaveBeenCalledWith(firstTimeout);
      expect(successMessage).toBe('Second message');

      // Fast-forward remaining time
      vi.advanceTimersByTime(5000);
      expect(successMessage).toBe('');
    });
  });

  describe('Error Message Management', () => {
    it('should set error message and auto-clear after 8 seconds', () => {
      let error = '';
      let errorMessageTimeout: NodeJS.Timeout | null = null;

      // Simulate the setErrorMessage function
      function setErrorMessage(message: string) {
        error = message;
        if (errorMessageTimeout) {
          clearTimeout(errorMessageTimeout);
        }
        errorMessageTimeout = setTimeout(() => {
          error = '';
          errorMessageTimeout = null;
        }, 8000);
      }

      // Test setting an error message
      setErrorMessage('Test error message');
      
      expect(error).toBe('Test error message');
      expect(setTimeoutSpy).toHaveBeenCalledWith(expect.any(Function), 8000);

      // Fast-forward time by 8 seconds
      vi.advanceTimersByTime(8000);

      expect(error).toBe('');
      expect(errorMessageTimeout).toBeNull();
    });

    it('should clear previous timeout when setting new error message', () => {
      let error = '';
      let errorMessageTimeout: NodeJS.Timeout | null = null;

      function setErrorMessage(message: string) {
        error = message;
        if (errorMessageTimeout) {
          clearTimeout(errorMessageTimeout);
        }
        errorMessageTimeout = setTimeout(() => {
          error = '';
          errorMessageTimeout = null;
        }, 8000);
      }

      // Set first error
      setErrorMessage('First error');
      const firstTimeout = errorMessageTimeout;

      // Set second error before first timeout expires
      vi.advanceTimersByTime(3000); // Only 3 seconds
      setErrorMessage('Second error');

      expect(clearTimeoutSpy).toHaveBeenCalledWith(firstTimeout);
      expect(error).toBe('Second error');

      // Fast-forward remaining time
      vi.advanceTimersByTime(8000);
      expect(error).toBe('');
    });
  });

  describe('Manual Dismissal', () => {
    it('should handle manual error dismissal correctly', () => {
      let error = '';
      let errorMessageTimeout: NodeJS.Timeout | null = null;

      function setErrorMessage(message: string) {
        error = message;
        if (errorMessageTimeout) {
          clearTimeout(errorMessageTimeout);
        }
        errorMessageTimeout = setTimeout(() => {
          error = '';
          errorMessageTimeout = null;
        }, 8000);
      }

      // Simulate manual dismissal function
      function dismissError() {
        error = '';
        if (errorMessageTimeout) {
          clearTimeout(errorMessageTimeout);
          errorMessageTimeout = null;
        }
      }

      // Set error message
      setErrorMessage('Test error');
      expect(error).toBe('Test error');

      // Manually dismiss before timeout
      vi.advanceTimersByTime(2000); // Only 2 seconds
      dismissError();

      expect(error).toBe('');
      expect(clearTimeoutSpy).toHaveBeenCalled();

      // Advance remaining time to ensure no side effects
      vi.advanceTimersByTime(6000);
      expect(error).toBe(''); // Should remain empty
    });
  });

  describe('Reactivity Loop Prevention', () => {
    it('should not create infinite loops with reactive statements', () => {
      let error = '';
      let errorMessageTimeout: NodeJS.Timeout | null = null;
      let reactiveCallCount = 0;

      function setErrorMessage(message: string) {
        error = message;
        if (errorMessageTimeout) {
          clearTimeout(errorMessageTimeout);
        }
        errorMessageTimeout = setTimeout(() => {
          error = '';
          errorMessageTimeout = null;
        }, 8000);
      }

      // Simulate the old problematic reactive statement
      function simulateOldReactiveStatement() {
        if (error) {
          reactiveCallCount++;
          // This would have created the infinite loop in the old implementation
          setTimeout(() => {
            error = '';
          }, 8000);
        }
      }

      // Test that our new approach doesn't trigger multiple reactive calls
      setErrorMessage('Test error');
      
      // Simulate reactive statement being called
      simulateOldReactiveStatement();
      
      expect(reactiveCallCount).toBe(1);
      
      // Fast-forward time
      vi.advanceTimersByTime(8000);
      
      // The reactive statement shouldn't be called again
      expect(reactiveCallCount).toBe(1);
      expect(error).toBe('');
    });

    it('should handle rapid successive message updates without memory leaks', () => {
      let successMessage = '';
      let successMessageTimeout: NodeJS.Timeout | null = null;

      function setSuccessMessage(message: string) {
        successMessage = message;
        if (successMessageTimeout) {
          clearTimeout(successMessageTimeout);
        }
        successMessageTimeout = setTimeout(() => {
          successMessage = '';
          successMessageTimeout = null;
        }, 5000);
      }

      // Rapidly set multiple messages
      for (let i = 0; i < 10; i++) {
        setSuccessMessage(`Message ${i}`);
        vi.advanceTimersByTime(100); // Small delay between messages
      }

      // Should have cleared previous timeouts
      expect(clearTimeoutSpy).toHaveBeenCalledTimes(9); // 9 clears for 10 messages

      // Final message should be the last one
      expect(successMessage).toBe('Message 9');

      // Fast-forward to clear final message
      vi.advanceTimersByTime(5000);
      expect(successMessage).toBe('');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty messages correctly', () => {
      let error = '';
      let errorMessageTimeout: NodeJS.Timeout | null = null;

      function setErrorMessage(message: string) {
        error = message;
        if (errorMessageTimeout) {
          clearTimeout(errorMessageTimeout);
        }
        errorMessageTimeout = setTimeout(() => {
          error = '';
          errorMessageTimeout = null;
        }, 8000);
      }

      // Set empty message
      setErrorMessage('');
      expect(error).toBe('');
      expect(setTimeoutSpy).toHaveBeenCalled();

      // Should still set timeout even for empty message
      vi.advanceTimersByTime(8000);
      expect(error).toBe('');
    });

    it('should handle null timeout correctly', () => {
      let successMessage = '';
      let successMessageTimeout: NodeJS.Timeout | null = null;

      function setSuccessMessage(message: string) {
        successMessage = message;
        if (successMessageTimeout) {
          clearTimeout(successMessageTimeout);
        }
        successMessageTimeout = setTimeout(() => {
          successMessage = '';
          successMessageTimeout = null;
        }, 5000);
      }

      // First call with null timeout
      expect(successMessageTimeout).toBeNull();
      setSuccessMessage('First message');
      
      expect(clearTimeoutSpy).not.toHaveBeenCalled(); // No timeout to clear initially
      expect(successMessageTimeout).not.toBeNull();

      // Second call should clear the previous timeout
      setSuccessMessage('Second message');
      expect(clearTimeoutSpy).toHaveBeenCalledTimes(1);
    });
  });
});
