<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';

	// State
	let isLoading = true;
	let verificationStatus: 'loading' | 'success' | 'error' = 'loading';
	let message = '';
	let email = '';
	let token = '';

	// Verify email on mount
	onMount(async () => {
		// Get token from URL parameters
		const urlParams = new URLSearchParams(window.location.search);
		token = urlParams.get('token') || '';

		if (!token) {
			verificationStatus = 'error';
			message = 'No verification token provided. Please check your email for the correct verification link.';
			isLoading = false;
			return;
		}

		try {
			const response = await fetch(`/api/auth/verify-email?token=${encodeURIComponent(token)}`);
			const data = await response.json();

			if (response.ok && data.success) {
				verificationStatus = 'success';
				message = data.message;
				email = data.email;
				
				// Redirect to login page after 3 seconds
				setTimeout(() => {
					goto('/login?verified=true');
				}, 3000);
			} else {
				verificationStatus = 'error';
				message = data.error || 'Email verification failed. Please try again.';
			}
		} catch (error) {
			console.error('Verification error:', error);
			verificationStatus = 'error';
			message = 'An error occurred during email verification. Please try again.';
		} finally {
			isLoading = false;
		}
	});

	// Resend verification email
	async function resendVerification() {
		if (!email) return;

		isLoading = true;
		try {
			const response = await fetch('/api/auth/verify-email', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ email })
			});

			const data = await response.json();

			if (response.ok && data.success) {
				message = 'New verification email sent! Please check your email.';
				verificationStatus = 'success';
			} else {
				message = data.error || 'Failed to resend verification email.';
				verificationStatus = 'error';
			}
		} catch (error) {
			console.error('Resend error:', error);
			message = 'An error occurred while resending verification email.';
			verificationStatus = 'error';
		} finally {
			isLoading = false;
		}
	}
</script>

<svelte:head>
	<title>Email Verification - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Verify your email address for FWFC" />
</svelte:head>

<div class="verification-container">
	<div class="verification-card">
		{#if isLoading}
			<div class="loading-section">
				<LoadingSpinner size="large" message="Verifying your email..." />
			</div>
		{:else if verificationStatus === 'success'}
			<div class="success-section">
				<div class="success-icon">✅</div>
				<h1>Email Verified Successfully!</h1>
				<p class="success-message">{message}</p>
				{#if email}
					<p class="email-info">
						Your email address <strong>{email}</strong> has been verified.
					</p>
				{/if}
				<div class="redirect-info">
					<p>You'll be redirected to the login page in a few seconds...</p>
					<a href="/login" class="btn primary">Go to Login Now</a>
				</div>
			</div>
		{:else}
			<div class="error-section">
				<div class="error-icon">❌</div>
				<h1>Email Verification Failed</h1>
				<ErrorMessage 
					title="Verification Error"
					message={message}
					type="error"
				/>
				
				<div class="help-section">
					<h2>What can you do?</h2>
					<ul>
						<li>Check if you clicked the correct verification link from your email</li>
						<li>Make sure the verification link hasn't expired (links expire after 24 hours)</li>
						<li>Try requesting a new verification email</li>
						<li>Contact support if the problem persists</li>
					</ul>

					{#if email}
						<div class="resend-section">
							<button 
								class="btn secondary"
								onclick={resendVerification}
								disabled={isLoading}
							>
								{isLoading ? 'Sending...' : 'Resend Verification Email'}
							</button>
						</div>
					{/if}

					<div class="contact-section">
						<p>
							Need help? Contact us at 
							<a href="mailto:<EMAIL>"><EMAIL></a>
						</p>
					</div>
				</div>
			</div>
		{/if}

		<div class="navigation-links">
			<a href="/" class="nav-link">← Back to Home</a>
			<a href="/register" class="nav-link">Register New Account</a>
			<a href="/login" class="nav-link">Login</a>
		</div>
	</div>
</div>

<style>
	.verification-container {
		max-width: 600px;
		margin: 0 auto;
		padding: 2rem 1rem;
		min-height: 60vh;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.verification-card {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 12px;
		box-shadow: 0 4px 6px var(--theme-shadow);
		padding: 2.5rem;
		color: var(--theme-text-primary);
		text-align: center;
		width: 100%;
	}

	.loading-section {
		padding: 2rem 0;
	}

	.success-section,
	.error-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1.5rem;
	}

	.success-icon,
	.error-icon {
		font-size: 4rem;
		margin-bottom: 1rem;
	}

	h1 {
		margin: 0;
		font-size: 2rem;
		font-weight: 700;
		color: var(--theme-text-primary);
	}

	.success-section h1 {
		color: var(--theme-accent-success, #28a745);
	}

	.error-section h1 {
		color: var(--theme-accent-danger, #dc3545);
	}

	.success-message {
		font-size: 1.1rem;
		color: var(--theme-text-secondary);
		margin: 0;
		line-height: 1.5;
	}

	.email-info {
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		padding: 1rem;
		margin: 1rem 0;
		font-size: 0.95rem;
		color: var(--theme-text-secondary);
	}

	.email-info strong {
		color: var(--theme-text-primary);
		font-weight: 600;
	}

	.redirect-info {
		margin-top: 2rem;
		padding-top: 1.5rem;
		border-top: 1px solid var(--theme-border);
	}

	.redirect-info p {
		margin: 0 0 1rem 0;
		color: var(--theme-text-muted);
		font-size: 0.9rem;
	}

	.help-section {
		text-align: left;
		margin-top: 1.5rem;
		width: 100%;
	}

	.help-section h2 {
		margin: 0 0 1rem 0;
		font-size: 1.3rem;
		color: var(--theme-text-primary);
		text-align: center;
	}

	.help-section ul {
		margin: 0 0 1.5rem 0;
		padding-left: 1.5rem;
		color: var(--theme-text-secondary);
		line-height: 1.6;
	}

	.help-section li {
		margin-bottom: 0.5rem;
	}

	.resend-section {
		text-align: center;
		margin: 1.5rem 0;
		padding: 1rem;
		background-color: var(--theme-bg-secondary);
		border-radius: 8px;
		border: 1px solid var(--theme-border);
	}

	.contact-section {
		text-align: center;
		margin-top: 1.5rem;
		padding-top: 1rem;
		border-top: 1px solid var(--theme-border);
	}

	.contact-section p {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	.contact-section a {
		color: var(--theme-accent-primary);
		text-decoration: none;
		font-weight: 600;
	}

	.contact-section a:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	.btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		padding: 0.875rem 1.5rem;
		border: 2px solid var(--theme-border);
		border-radius: 8px;
		font-size: 1rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
		min-height: 44px;
		box-sizing: border-box;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border-color: var(--theme-accent-primary);
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
	}

	.btn.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border-color: var(--theme-border);
	}

	.btn.secondary:hover:not(:disabled) {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-1px);
	}

	.navigation-links {
		margin-top: 2rem;
		padding-top: 1.5rem;
		border-top: 1px solid var(--theme-border);
		display: flex;
		justify-content: center;
		gap: 1rem;
		flex-wrap: wrap;
	}

	.nav-link {
		color: var(--color-interactive-primary);
		text-decoration: none;
		font-weight: var(--font-weight-semibold);
		font-size: var(--font-size-sm);
		padding: var(--space-sm);
		border-radius: var(--border-radius-md);
		transition: var(--transition-theme);
	}

	.nav-link:hover {
		color: var(--color-interactive-primary-hover);
		background-color: var(--color-surface-secondary);
		text-decoration: underline;
	}

	/* Responsive Design */
	@media (max-width: 640px) {
		.verification-container {
			padding: 1rem;
		}

		.verification-card {
			padding: 1.5rem;
		}

		h1 {
			font-size: 1.5rem;
		}

		.success-icon,
		.error-icon {
			font-size: 3rem;
		}

		.navigation-links {
			flex-direction: column;
			align-items: center;
		}
	}

	/* High Contrast Mode */
	:global(.high-contrast) .verification-card {
		border-width: 3px;
	}

	:global(.high-contrast) .btn {
		border-width: 3px;
	}

	/* Large Text Mode */
	:global(.large-text) h1 {
		font-size: 2.5rem;
	}

	:global(.large-text) .success-icon,
	:global(.large-text) .error-icon {
		font-size: 5rem;
	}

	:global(.large-text) .btn {
		padding: 1rem 2rem;
		font-size: 1.2rem;
	}

	/* Focus Management */
	.btn:focus,
	.nav-link:focus,
	.contact-section a:focus {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
	}
</style>
