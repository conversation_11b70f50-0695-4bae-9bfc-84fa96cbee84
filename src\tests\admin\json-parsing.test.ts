import { describe, it, expect } from 'vitest';

describe('JSON Parsing Safety', () => {
  // Simulate the safeJsonParse function from the component
  function safeJsonParse(jsonString: string | null | undefined, fallback: any = []): any {
    if (!jsonString) return fallback;
    
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.warn('Failed to parse JSON:', jsonString, error);
      return fallback;
    }
  }

  // Simulate the validatePersonalityJson function
  function validatePersonalityJson(personalityString: string | null): string | null {
    if (!personalityString || personalityString.trim() === '') {
      return null;
    }

    try {
      // Try to parse the JSON to validate it
      const parsed = JSON.parse(personalityString);
      // Return the properly formatted JSON string
      return JSON.stringify(parsed);
    } catch (error) {
      // If it's not valid JSON, try to create a simple structure
      console.warn('Invalid personality JSON, attempting to create valid structure:', error);
      
      // If it looks like a simple list of traits, convert it
      if (personalityString.includes(',') && !personalityString.includes('{')) {
        const traits = personalityString.split(',').map(trait => trait.trim().replace(/['"]/g, ''));
        return JSON.stringify({
          traits: traits,
          writingStyle: 'casual',
          activityLevel: 'medium'
        });
      }
      
      // Otherwise, wrap it in a basic structure
      return JSON.stringify({
        traits: [personalityString.trim()],
        writingStyle: 'casual',
        activityLevel: 'medium'
      });
    }
  }

  describe('safeJsonParse', () => {
    it('should parse valid JSON correctly', () => {
      const validJson = '["friendly", "enthusiastic"]';
      const result = safeJsonParse(validJson, []);
      expect(result).toEqual(['friendly', 'enthusiastic']);
    });

    it('should return fallback for invalid JSON', () => {
      const invalidJson = 'enthusiastic, friendly';
      const result = safeJsonParse(invalidJson, []);
      expect(result).toEqual([]);
    });

    it('should return fallback for null/undefined', () => {
      expect(safeJsonParse(null, [])).toEqual([]);
      expect(safeJsonParse(undefined, [])).toEqual([]);
      expect(safeJsonParse('', [])).toEqual([]);
    });

    it('should handle complex JSON objects', () => {
      const complexJson = '{"traits": ["friendly", "enthusiastic"], "writingStyle": "casual"}';
      const result = safeJsonParse(complexJson, {});
      expect(result).toEqual({
        traits: ['friendly', 'enthusiastic'],
        writingStyle: 'casual'
      });
    });

    it('should handle malformed JSON with quotes', () => {
      const malformedJson = '"enthusiastic", "friendly"';
      const result = safeJsonParse(malformedJson, []);
      expect(result).toEqual([]);
    });
  });

  describe('validatePersonalityJson', () => {
    it('should validate and return properly formatted JSON', () => {
      const validInput = '{"traits": ["friendly", "enthusiastic"], "writingStyle": "casual"}';
      const result = validatePersonalityJson(validInput);
      expect(result).toBe('{"traits":["friendly","enthusiastic"],"writingStyle":"casual"}');
    });

    it('should convert comma-separated traits to valid JSON', () => {
      const traitsList = 'friendly, enthusiastic, helpful';
      const result = validatePersonalityJson(traitsList);
      const parsed = JSON.parse(result!);
      
      expect(parsed.traits).toEqual(['friendly', 'enthusiastic', 'helpful']);
      expect(parsed.writingStyle).toBe('casual');
      expect(parsed.activityLevel).toBe('medium');
    });

    it('should handle single trait input', () => {
      const singleTrait = 'enthusiastic';
      const result = validatePersonalityJson(singleTrait);
      const parsed = JSON.parse(result!);
      
      expect(parsed.traits).toEqual(['enthusiastic']);
      expect(parsed.writingStyle).toBe('casual');
      expect(parsed.activityLevel).toBe('medium');
    });

    it('should return null for empty input', () => {
      expect(validatePersonalityJson('')).toBeNull();
      expect(validatePersonalityJson(null)).toBeNull();
      expect(validatePersonalityJson('   ')).toBeNull();
    });

    it('should handle traits with quotes', () => {
      const quotedTraits = '"friendly", "enthusiastic"';
      const result = validatePersonalityJson(quotedTraits);
      const parsed = JSON.parse(result!);
      
      expect(parsed.traits).toEqual(['friendly', 'enthusiastic']);
    });

    it('should preserve valid JSON structure', () => {
      const validJson = {
        traits: ['friendly', 'enthusiastic'],
        interests: ['music', 'movies'],
        writingStyle: 'casual',
        activityLevel: 'high'
      };
      
      const result = validatePersonalityJson(JSON.stringify(validJson));
      expect(JSON.parse(result!)).toEqual(validJson);
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle the problematic "enthusiast" case', () => {
      // This is the case that was causing the original error
      const problematicInput = 'enthusiast';
      const result = safeJsonParse(problematicInput, []);
      expect(result).toEqual([]); // Should fallback safely
      
      // And validation should fix it
      const validated = validatePersonalityJson(problematicInput);
      const parsed = JSON.parse(validated!);
      expect(parsed.traits).toEqual(['enthusiast']);
    });

    it('should handle database data that might be corrupted', () => {
      const corruptedData = 'enthusiastic", "friendly';
      const result = safeJsonParse(corruptedData, []);
      expect(result).toEqual([]);
      
      const validated = validatePersonalityJson(corruptedData);
      expect(validated).toBeTruthy();
      expect(() => JSON.parse(validated!)).not.toThrow();
    });

    it('should handle mixed format inputs', () => {
      const mixedInputs = [
        'friendly, enthusiastic',
        '["friendly", "enthusiastic"]',
        '{"traits": ["friendly"]}',
        'just a single trait',
        ''
      ];

      mixedInputs.forEach(input => {
        const result = safeJsonParse(input, []);
        expect(result).toBeDefined(); // Should not throw

        if (input.trim()) {
          const validated = validatePersonalityJson(input);
          if (validated) {
            expect(() => JSON.parse(validated)).not.toThrow();
          }
        }
      });
    });
  });

  describe('Template parsing', () => {
    it('should safely parse template variables', () => {
      const template = {
        variables: '["username", "topic"]',
        personality: '["enthusiastic", "friendly"]'
      };

      const variables = safeJsonParse(template.variables, []);
      const personality = safeJsonParse(template.personality, []);

      expect(variables).toEqual(['username', 'topic']);
      expect(personality).toEqual(['enthusiastic', 'friendly']);
    });

    it('should handle malformed template data', () => {
      const template = {
        variables: 'username, topic',
        personality: 'enthusiastic, friendly'
      };

      const variables = safeJsonParse(template.variables, []);
      const personality = safeJsonParse(template.personality, []);

      expect(variables).toEqual([]); // Fallback
      expect(personality).toEqual([]); // Fallback
    });
  });
});
