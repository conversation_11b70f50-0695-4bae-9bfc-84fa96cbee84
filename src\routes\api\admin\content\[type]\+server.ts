import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { news, gallery, users } from '$lib/server/db/schema';
import { eq, desc } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// GET /api/admin/content/[type] - Get content items by type
export const GET: RequestHandler = async ({ params, url, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const contentType = params.type;
    const published = url.searchParams.get('published') === 'true';
    const limit = parseInt(url.searchParams.get('limit') || '50');

    if (!['news', 'gallery'].includes(contentType)) {
      return json({
        success: false,
        error: 'Invalid content type'
      }, { status: 400 });
    }

    let contentItems: any[] = [];

    if (contentType === 'news') {
      const query = db.select({
        id: news.id,
        title: news.title,
        content: news.content,
        imageUrl: news.imageUrl,
        authorId: news.authorId,
        published: news.published,
        createdAt: news.createdAt,
        updatedAt: news.updatedAt,
        author: {
          id: users.id,
          username: users.username,
          displayName: users.displayName
        }
      })
      .from(news)
      .leftJoin(users, eq(news.authorId, users.id));

      if (published) {
        contentItems = await query
          .where(eq(news.published, true))
          .orderBy(desc(news.createdAt))
          .limit(limit);
      } else {
        contentItems = await query
          .orderBy(desc(news.createdAt))
          .limit(limit);
      }
    } else if (contentType === 'gallery') {
      const query = db.select({
        id: gallery.id,
        title: gallery.title,
        description: gallery.description,
        imageUrl: gallery.imageUrl,
        thumbnailUrl: gallery.thumbnailUrl,
        authorId: gallery.authorId,
        published: gallery.published,
        createdAt: gallery.createdAt,
        updatedAt: gallery.updatedAt,
        author: {
          id: users.id,
          username: users.username,
          displayName: users.displayName
        }
      })
      .from(gallery)
      .leftJoin(users, eq(gallery.authorId, users.id));

      if (published) {
        contentItems = await query
          .where(eq(gallery.published, true))
          .orderBy(desc(gallery.createdAt))
          .limit(limit);
      } else {
        contentItems = await query
          .orderBy(desc(gallery.createdAt))
          .limit(limit);
      }
    }

    return json({
      success: true,
      data: contentItems
    });

  } catch (error) {
    logger.error('Error fetching content items:', error);
    return json({
      success: false,
      error: 'Failed to fetch content items'
    }, { status: 500 });
  }
};
