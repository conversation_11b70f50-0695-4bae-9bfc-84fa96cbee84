import { sql } from 'drizzle-orm';

export async function up(db: any) {
  console.log('Running migration: Enhanced User Management System');

  // Add new columns to users table
  console.log('Adding new columns to users table...');
  await db.run(sql`
    ALTER TABLE users ADD COLUMN bio TEXT;
  `);
  await db.run(sql`
    ALTER TABLE users ADD COLUMN avatar_url TEXT;
  `);
  await db.run(sql`
    ALTER TABLE users ADD COLUMN location TEXT;
  `);
  await db.run(sql`
    ALTER TABLE users ADD COLUMN website TEXT;
  `);
  await db.run(sql`
    ALTER TABLE users ADD COLUMN birth_date TEXT;
  `);
  await db.run(sql`
    ALTER TABLE users ADD COLUMN interests TEXT DEFAULT '[]';
  `);
  await db.run(sql`
    ALTER TABLE users ADD COLUMN status TEXT DEFAULT 'active' NOT NULL;
  `);
  await db.run(sql`
    ALTER TABLE users ADD COLUMN is_simulated INTEGER DEFAULT 0 NOT NULL;
  `);
  await db.run(sql`
    ALTER TABLE users ADD COLUMN simulated_personality TEXT;
  `);
  await db.run(sql`
    ALTER TABLE users ADD COLUMN last_active_at TEXT;
  `);

  // Create audit_logs table
  console.log('Creating audit_logs table...');
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS audit_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      admin_user_id INTEGER NOT NULL,
      action TEXT NOT NULL,
      target_type TEXT NOT NULL,
      target_id INTEGER,
      target_user_id INTEGER,
      details TEXT,
      ip_address TEXT,
      user_agent TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (admin_user_id) REFERENCES users(id),
      FOREIGN KEY (target_user_id) REFERENCES users(id)
    )
  `);

  // Create scheduled_content table
  console.log('Creating scheduled_content table...');
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS scheduled_content (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      content_type TEXT NOT NULL,
      content_data TEXT NOT NULL,
      as_user_id INTEGER NOT NULL,
      scheduled_for TEXT NOT NULL,
      status TEXT DEFAULT 'pending' NOT NULL,
      created_by_user_id INTEGER NOT NULL,
      published_at TEXT,
      error_message TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (as_user_id) REFERENCES users(id),
      FOREIGN KEY (created_by_user_id) REFERENCES users(id)
    )
  `);

  // Create content_authorship table
  console.log('Creating content_authorship table...');
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS content_authorship (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      content_type TEXT NOT NULL,
      content_id INTEGER NOT NULL,
      actual_author_id INTEGER NOT NULL,
      display_author_id INTEGER NOT NULL,
      is_simulated INTEGER DEFAULT 1 NOT NULL,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (actual_author_id) REFERENCES users(id),
      FOREIGN KEY (display_author_id) REFERENCES users(id)
    )
  `);

  // Create interaction_templates table
  console.log('Creating interaction_templates table...');
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS interaction_templates (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      category TEXT NOT NULL,
      template TEXT NOT NULL,
      variables TEXT DEFAULT '[]',
      personality TEXT DEFAULT '[]',
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Insert some default interaction templates
  console.log('Inserting default interaction templates...');
  await db.run(sql`
    INSERT INTO interaction_templates (name, category, template, variables, personality)
    VALUES 
      ('Enthusiastic Comment', 'comment', 'OMG {username}, this is amazing! I love {topic} so much! 😍', '["username", "topic"]', '["enthusiastic", "friendly"]'),
      ('Supportive Reply', 'reply', 'I totally agree with you {username}! {topic} is definitely one of my favorites too.', '["username", "topic"]', '["supportive", "agreeable"]'),
      ('Question About Topic', 'question', 'Hey {username}, what do you think about {topic}? I''d love to hear your thoughts!', '["username", "topic"]', '["curious", "engaging"]'),
      ('Excited Reaction', 'reaction', 'This is so cool! Thanks for sharing {username}! 🎉', '["username"]', '["excited", "grateful"]'),
      ('Thoughtful Response', 'comment', 'This really makes me think about {topic}. Thanks for posting this {username}!', '["username", "topic"]', '["thoughtful", "appreciative"]')
  `);

  console.log('Migration completed successfully!');
}

export async function down(db: any) {
  console.log('Rolling back migration: Enhanced User Management System');

  // Drop new tables
  await db.run(sql`DROP TABLE IF EXISTS interaction_templates`);
  await db.run(sql`DROP TABLE IF EXISTS content_authorship`);
  await db.run(sql`DROP TABLE IF EXISTS scheduled_content`);
  await db.run(sql`DROP TABLE IF EXISTS audit_logs`);

  // Note: SQLite doesn't support dropping columns, so we can't easily roll back the user table changes
  console.log('Note: User table column additions cannot be rolled back in SQLite');
  console.log('Rollback completed (partial - user table columns remain)');
}
