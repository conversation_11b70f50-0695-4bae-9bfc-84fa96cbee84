import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// POST /api/auth/login - Authenticate a user
export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.email || !body.password) {
      return json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }

    // For development purposes, allow admin login with hardcoded credentials
    if (process.env.NODE_ENV === 'development' &&
        body.email === '<EMAIL>' &&
        body.password === 'admin') {

      logger.info('Development mode: Admin login successful', { email: body.email });

      // Create a session
      const sessionId = crypto.randomUUID();

      // Set the session cookie
      cookies.set('session_id', sessionId, {
        path: '/',
        httpOnly: true,
        sameSite: 'strict',
        secure: process.env.NODE_ENV === 'production',
        maxAge: 60 * 60 * 24 * 7 // 1 week
      });

      // Return success with admin role
      return json({
        success: true,
        data: {
          id: 1,
          username: 'admin',
          displayName: 'Admin User',
          email: '<EMAIL>',
          role: 'admin'
        }
      });
    }

    // Find the user by email
    const user = await db.select()
      .from(users)
      .where(eq(users.email, body.email))
      .limit(1);

    if (user.length === 0) {
      logger.warn('Login failed: User not found', { email: body.email });
      return json({
        success: false,
        error: 'Invalid email or password'
      }, { status: 401 });
    }

    // In a real app, you'd verify the password hash here
    // For now, we'll just check if the password is 'password' for simplicity
    if (body.password !== 'password') {
      logger.warn('Login failed: Invalid password', { email: body.email });
      return json({
        success: false,
        error: 'Invalid email or password'
      }, { status: 401 });
    }

    // Create a session (in a real app, you'd use a proper session management system)
    const sessionId = crypto.randomUUID();
    cookies.set('session_id', sessionId, {
      path: '/',
      httpOnly: true,
      sameSite: 'strict',
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60 * 24 * 7 // 1 week
    });

    // Return user data (excluding sensitive information)
    const { passwordHash, ...userData } = user[0];

    logger.info('User login successful', { email: body.email, role: userData.role });

    return json({
      success: true,
      data: {
        ...userData,
        role: userData.role || 'user' // Default to 'user' if role is not set
      }
    });
  } catch (error) {
    logger.error('Error logging in:', { error });
    return json({
      success: false,
      error: 'Failed to log in'
    }, { status: 500 });
  }
};
