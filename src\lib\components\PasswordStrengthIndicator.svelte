<script lang="ts">
	import { calculatePasswordStrength, getPasswordStrengthPercentage } from '$lib/utils/password';
	import type { PasswordStrength } from '$lib/utils/password';

	export let password: string = '';
	export let showRequirements: boolean = true;
	export let showFeedback: boolean = true;
	export let compact: boolean = false;

	$: strength = calculatePasswordStrength(password);
	$: percentage = getPasswordStrengthPercentage(strength.score);
</script>

<div class="password-strength-indicator" class:compact>
	<!-- Strength Bar -->
	<div class="strength-bar-container">
		<div class="strength-bar">
			<div 
				class="strength-fill" 
				style="width: {percentage}%; background-color: {strength.color};"
				role="progressbar"
				aria-valuenow={strength.score}
				aria-valuemin="0"
				aria-valuemax="4"
				aria-label="Password strength: {strength.label}"
			></div>
		</div>
		<span class="strength-label" style="color: {strength.color};">
			{strength.label}
		</span>
	</div>

	<!-- Requirements Checklist -->
	{#if showRequirements && !compact}
		<div class="requirements-section" role="group" aria-label="Password requirements">
			<h4 class="requirements-title">Password Requirements:</h4>
			<ul class="requirements-list">
				{#each strength.requirements as requirement}
					<li 
						class="requirement-item"
						class:met={requirement.met}
						class:unmet={!requirement.met}
					>
						<span class="requirement-icon" aria-hidden="true">
							{requirement.met ? '✓' : '○'}
						</span>
						<span class="requirement-text">
							{requirement.label}
						</span>
						<span class="sr-only">
							{requirement.met ? 'Met' : 'Not met'}: {requirement.description}
						</span>
					</li>
				{/each}
			</ul>
		</div>
	{/if}

	<!-- Feedback Messages -->
	{#if showFeedback && strength.feedback.length > 0}
		<div class="feedback-section" role="group" aria-label="Password feedback">
			<ul class="feedback-list">
				{#each strength.feedback as feedback}
					<li class="feedback-item">
						<span class="feedback-icon" aria-hidden="true">💡</span>
						{feedback}
					</li>
				{/each}
			</ul>
		</div>
	{/if}
</div>

<style>
	.password-strength-indicator {
		margin-top: 0.5rem;
		font-size: 0.9rem;
	}

	.password-strength-indicator.compact {
		margin-top: 0.25rem;
	}

	.strength-bar-container {
		display: flex;
		align-items: center;
		gap: 0.75rem;
		margin-bottom: 1rem;
	}

	.compact .strength-bar-container {
		margin-bottom: 0.5rem;
	}

	.strength-bar {
		flex: 1;
		height: 8px;
		background-color: var(--theme-bg-tertiary);
		border-radius: 4px;
		overflow: hidden;
		border: 1px solid var(--theme-border-light);
	}

	.strength-fill {
		height: 100%;
		transition: width 0.3s ease, background-color 0.3s ease;
		border-radius: 3px;
	}

	.strength-label {
		font-weight: 600;
		font-size: 0.85rem;
		min-width: 80px;
		text-align: right;
		transition: color 0.3s ease;
	}

	.requirements-section {
		margin-bottom: 1rem;
	}

	.requirements-title {
		margin: 0 0 0.5rem 0;
		font-size: 0.9rem;
		font-weight: 600;
		color: var(--theme-text-primary);
	}

	.requirements-list {
		list-style: none;
		padding: 0;
		margin: 0;
		display: grid;
		gap: 0.25rem;
	}

	.requirement-item {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.25rem 0;
		transition: color 0.2s ease;
	}

	.requirement-item.met {
		color: var(--theme-accent-success, #28a745);
	}

	.requirement-item.unmet {
		color: var(--theme-text-muted);
	}

	.requirement-icon {
		font-size: 0.9rem;
		width: 16px;
		text-align: center;
		flex-shrink: 0;
	}

	.requirement-item.met .requirement-icon {
		color: var(--theme-accent-success, #28a745);
	}

	.requirement-item.unmet .requirement-icon {
		color: var(--theme-text-muted);
	}

	.requirement-text {
		font-size: 0.85rem;
		line-height: 1.3;
	}

	.feedback-section {
		margin-top: 0.75rem;
	}

	.feedback-list {
		list-style: none;
		padding: 0;
		margin: 0;
		display: grid;
		gap: 0.25rem;
	}

	.feedback-item {
		display: flex;
		align-items: flex-start;
		gap: 0.5rem;
		padding: 0.25rem 0;
		font-size: 0.85rem;
		line-height: 1.4;
		color: var(--theme-text-secondary);
	}

	.feedback-icon {
		flex-shrink: 0;
		margin-top: 0.1rem;
	}

	/* Screen reader only content */
	.sr-only {
		position: absolute;
		width: 1px;
		height: 1px;
		padding: 0;
		margin: -1px;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border: 0;
	}

	/* High contrast mode adjustments */
	:global(.high-contrast) .strength-bar {
		border: 2px solid var(--theme-border);
	}

	:global(.high-contrast) .requirement-item.met {
		color: var(--theme-accent-primary);
		font-weight: bold;
	}

	:global(.high-contrast) .requirement-item.unmet {
		color: var(--theme-text-primary);
	}

	/* Large text mode adjustments */
	:global(.large-text) .password-strength-indicator {
		font-size: 1.1rem;
	}

	:global(.large-text) .strength-bar {
		height: 10px;
	}

	:global(.large-text) .strength-label {
		font-size: 1rem;
	}

	/* Simplified interface adjustments */
	:global(.simplified-interface) .feedback-section {
		display: none;
	}

	:global(.simplified-interface) .requirements-section {
		margin-bottom: 0.5rem;
	}

	/* Focus management for keyboard navigation */
	.requirement-item:focus-within {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
		border-radius: 4px;
	}

	/* Animation for strength changes */
	@keyframes strengthChange {
		0% { transform: scale(1); }
		50% { transform: scale(1.05); }
		100% { transform: scale(1); }
	}

	.strength-fill {
		animation: strengthChange 0.3s ease when strength changes;
	}

	/* Responsive design */
	@media (max-width: 480px) {
		.strength-bar-container {
			flex-direction: column;
			align-items: stretch;
			gap: 0.5rem;
		}

		.strength-label {
			text-align: left;
			min-width: auto;
		}

		.requirements-list {
			font-size: 0.8rem;
		}

		.feedback-item {
			font-size: 0.8rem;
		}
	}
</style>
