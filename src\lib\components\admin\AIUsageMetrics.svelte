<script lang="ts">
	import { onMount } from 'svelte';
	import LoadingSpinner from '../LoadingSpinner.svelte';
	import ErrorMessage from '../ErrorMessage.svelte';
	import { api } from '$lib/utils/api';

	// State
	let metrics: any = null;
	let isLoading = false;
	let error = '';
	let timeRange = '24h';

	// Time range options
	const timeRangeOptions = [
		{ value: '1h', label: 'Last Hour' },
		{ value: '24h', label: 'Last 24 Hours' },
		{ value: '7d', label: 'Last 7 Days' },
		{ value: '30d', label: 'Last 30 Days' }
	];

	/**
	 * Load usage metrics
	 */
	async function loadMetrics() {
		isLoading = true;
		error = '';

		try {
			const response = await api.get(`/api/admin/ai-usage-metrics?timeRange=${timeRange}`);

			if (response.success && response.data) {
				metrics = response.data;
			} else {
				error = response.error || 'Failed to load metrics';
			}
		} catch (err) {
			console.error('Error loading metrics:', err);
			error = 'An error occurred while loading metrics';
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Handle time range change
	 */
	function handleTimeRangeChange() {
		loadMetrics();
	}

	/**
	 * Format number with commas
	 */
	function formatNumber(num: number): string {
		return num.toLocaleString();
	}

	/**
	 * Format percentage
	 */
	function formatPercentage(num: number): string {
		return `${num.toFixed(1)}%`;
	}

	/**
	 * Get status color class
	 */
	function getStatusColor(value: number, thresholds: { good: number; warning: number }): string {
		if (value >= thresholds.good) return 'status-success';
		if (value >= thresholds.warning) return 'status-warning';
		return 'status-danger';
	}

	// Load metrics on mount and when time range changes
	onMount(() => {
		loadMetrics();
		
		// Auto-refresh every 30 seconds
		const interval = setInterval(loadMetrics, 30000);
		return () => clearInterval(interval);
	});
</script>

<div class="ai-usage-metrics">
	<div class="metrics-header">
		<h3>AI Usage Metrics</h3>
		
		<div class="time-range-selector">
			<label for="time-range">Time Range:</label>
			<select
				id="time-range"
				bind:value={timeRange}
				onchange={handleTimeRangeChange}
			>
				{#each timeRangeOptions as option}
					<option value={option.value}>{option.label}</option>
				{/each}
			</select>
		</div>
	</div>

	{#if error}
		<ErrorMessage
			title="Error"
			message={error}
			type="error"
			dismissible={true}
			onDismiss={() => error = ''}
		/>
	{/if}

	{#if isLoading}
		<LoadingSpinner message="Loading metrics..." />
	{:else if metrics}
		<div class="metrics-grid">
			<!-- Generation Statistics -->
			<div class="metric-card">
				<div class="metric-header">
					<h4>Generation Statistics</h4>
					<span class="metric-icon">📊</span>
				</div>
				<div class="metric-content">
					<div class="metric-item">
						<span class="metric-label">Total Generations</span>
						<span class="metric-value">{formatNumber(metrics.totalGenerations)}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Successful</span>
						<span class="metric-value success">{formatNumber(metrics.successfulGenerations)}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Failed</span>
						<span class="metric-value danger">{formatNumber(metrics.failedGenerations)}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Success Rate</span>
						<span class="metric-value {getStatusColor(metrics.successRate, { good: 90, warning: 75 })}">
							{formatPercentage(metrics.successRate)}
						</span>
					</div>
				</div>
			</div>

			<!-- Content Quality -->
			<div class="metric-card">
				<div class="metric-header">
					<h4>Content Quality</h4>
					<span class="metric-icon">⭐</span>
				</div>
				<div class="metric-content">
					<div class="metric-item">
						<span class="metric-label">Avg Authenticity Score</span>
						<span class="metric-value {getStatusColor(metrics.avgAuthenticityScore, { good: 80, warning: 60 })}">
							{metrics.avgAuthenticityScore.toFixed(1)}%
						</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">High Quality (80%+)</span>
						<span class="metric-value success">{formatNumber(metrics.highQualityCount)}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Needs Review</span>
						<span class="metric-value warning">{formatNumber(metrics.needsReviewCount)}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Rejected</span>
						<span class="metric-value danger">{formatNumber(metrics.rejectedCount)}</span>
					</div>
				</div>
			</div>

			<!-- Content Safety -->
			<div class="metric-card">
				<div class="metric-header">
					<h4>Content Safety</h4>
					<span class="metric-icon">🛡️</span>
				</div>
				<div class="metric-content">
					<div class="metric-item">
						<span class="metric-label">Safe Content</span>
						<span class="metric-value success">{formatNumber(metrics.safeContentCount)}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Flagged Content</span>
						<span class="metric-value warning">{formatNumber(metrics.flaggedContentCount)}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Blocked Content</span>
						<span class="metric-value danger">{formatNumber(metrics.blockedContentCount)}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Safety Rate</span>
						<span class="metric-value {getStatusColor(metrics.safetyRate, { good: 95, warning: 90 })}">
							{formatPercentage(metrics.safetyRate)}
						</span>
					</div>
				</div>
			</div>

			<!-- Performance Metrics -->
			<div class="metric-card">
				<div class="metric-header">
					<h4>Performance</h4>
					<span class="metric-icon">⚡</span>
				</div>
				<div class="metric-content">
					<div class="metric-item">
						<span class="metric-label">Avg Generation Time</span>
						<span class="metric-value">{metrics.avgGenerationTime.toFixed(0)}ms</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Fastest Generation</span>
						<span class="metric-value success">{metrics.fastestGeneration}ms</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Slowest Generation</span>
						<span class="metric-value warning">{metrics.slowestGeneration}ms</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Rate Limit Hits</span>
						<span class="metric-value {metrics.rateLimitHits > 0 ? 'warning' : 'success'}">
							{formatNumber(metrics.rateLimitHits)}
						</span>
					</div>
				</div>
			</div>

			<!-- Content Types -->
			<div class="metric-card">
				<div class="metric-header">
					<h4>Content Types</h4>
					<span class="metric-icon">📝</span>
				</div>
				<div class="metric-content">
					{#each metrics.contentTypeBreakdown as contentType}
						<div class="metric-item">
							<span class="metric-label">{contentType.type}</span>
							<span class="metric-value">{formatNumber(contentType.count)}</span>
						</div>
					{/each}
				</div>
			</div>

			<!-- Top Users -->
			<div class="metric-card">
				<div class="metric-header">
					<h4>Top Users</h4>
					<span class="metric-icon">👥</span>
				</div>
				<div class="metric-content">
					{#each metrics.topUsers as user}
						<div class="metric-item">
							<span class="metric-label">{user.displayName}</span>
							<span class="metric-value">{formatNumber(user.generationCount)}</span>
						</div>
					{/each}
				</div>
			</div>
		</div>

		<!-- Recent Activity -->
		{#if metrics.recentActivity && metrics.recentActivity.length > 0}
			<div class="recent-activity">
				<h4>Recent Activity</h4>
				<div class="activity-list">
					{#each metrics.recentActivity as activity}
						<div class="activity-item">
							<div class="activity-info">
								<span class="activity-type">{activity.contentType}</span>
								<span class="activity-user">{activity.userName}</span>
								<span class="activity-time">{new Date(activity.createdAt).toLocaleTimeString()}</span>
							</div>
							<div class="activity-status">
								<span class="status-badge {activity.success ? 'success' : 'danger'}">
									{activity.success ? '✓' : '✗'}
								</span>
								{#if activity.authenticityScore}
									<span class="authenticity-score">{activity.authenticityScore}%</span>
								{/if}
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/if}
	{/if}
</div>

<style>
	.ai-usage-metrics {
		background: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		padding: 1.5rem;
	}

	.metrics-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1.5rem;
	}

	.metrics-header h3 {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1.25rem;
	}

	.time-range-selector {
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.time-range-selector label {
		font-weight: 500;
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.time-range-selector select {
		padding: 0.5rem;
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		background: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 1.5rem;
		margin-bottom: 2rem;
	}

	.metric-card {
		background: var(--theme-bg-primary);
		border: 1px solid var(--theme-border);
		border-radius: 6px;
		padding: 1.25rem;
	}

	.metric-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1rem;
	}

	.metric-header h4 {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
	}

	.metric-icon {
		font-size: 1.25rem;
	}

	.metric-content {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.metric-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.metric-label {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	.metric-value {
		font-weight: 600;
		font-size: 1rem;
		color: var(--theme-text-primary);
	}

	.metric-value.success,
	.status-success {
		color: var(--theme-accent-success);
	}

	.metric-value.warning,
	.status-warning {
		color: var(--theme-accent-warning);
	}

	.metric-value.danger,
	.status-danger {
		color: var(--theme-accent-danger);
	}

	.recent-activity {
		border-top: 1px solid var(--theme-border);
		padding-top: 1.5rem;
	}

	.recent-activity h4 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
	}

	.activity-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
		max-height: 300px;
		overflow-y: auto;
	}

	.activity-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0.75rem;
		background: var(--theme-bg-tertiary);
		border-radius: 4px;
	}

	.activity-info {
		display: flex;
		gap: 1rem;
		align-items: center;
	}

	.activity-type {
		padding: 0.25rem 0.5rem;
		background: var(--theme-accent-primary);
		color: white;
		border-radius: 4px;
		font-size: 0.8rem;
		text-transform: uppercase;
		font-weight: 600;
	}

	.activity-user {
		font-weight: 500;
		color: var(--theme-text-primary);
	}

	.activity-time {
		color: var(--theme-text-muted);
		font-size: 0.8rem;
	}

	.activity-status {
		display: flex;
		gap: 0.5rem;
		align-items: center;
	}

	.status-badge {
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-weight: 600;
		font-size: 0.8rem;
	}

	.status-badge.success {
		background: var(--theme-accent-success);
		color: white;
	}

	.status-badge.danger {
		background: var(--theme-accent-danger);
		color: white;
	}

	.authenticity-score {
		padding: 0.25rem 0.5rem;
		background: var(--theme-bg-secondary);
		border-radius: 4px;
		font-size: 0.8rem;
		font-weight: 600;
		color: var(--theme-text-secondary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.metrics-header {
			flex-direction: column;
			gap: 1rem;
			align-items: stretch;
		}

		.metrics-grid {
			grid-template-columns: 1fr;
		}

		.activity-info {
			flex-direction: column;
			align-items: flex-start;
			gap: 0.5rem;
		}

		.activity-item {
			flex-direction: column;
			align-items: stretch;
		}

		.activity-status {
			justify-content: flex-end;
		}
	}

	/* High Contrast Mode */
	@media (prefers-contrast: high) {
		.metric-card,
		.activity-item {
			border: 2px solid currentColor;
		}

		.status-badge,
		.activity-type {
			border: 1px solid currentColor;
		}
	}
</style>
