<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import LoadingSpinner from '../LoadingSpinner.svelte';
	import ErrorMessage from '../ErrorMessage.svelte';
	import { api } from '$lib/utils/api';

	const dispatch = createEventDispatcher();

	// Props
	export let reviewId: number | null = null;
	export let showModal = false;

	// State
	let isLoading = false;
	let error = '';
	let reviewData: any = null;
	let reviewNotes = '';
	let editedContent: any = null;

	// Review status options
	const statusOptions = [
		{ value: 'pending', label: 'Pending Review', color: 'warning' },
		{ value: 'approved', label: 'Approved', color: 'success' },
		{ value: 'rejected', label: 'Rejected', color: 'danger' },
		{ value: 'needs_revision', label: 'Needs Revision', color: 'info' }
	];

	/**
	 * Load review data
	 */
	async function loadReview() {
		if (!reviewId) return;

		isLoading = true;
		error = '';

		try {
			const response = await api.get(`/api/admin/ai-content-reviews/${reviewId}`);
			
			if (response.success && response.data) {
				reviewData = response.data;
				reviewNotes = reviewData.reviewNotes || '';
				editedContent = { ...reviewData.generatedContent };
			} else {
				error = response.error || 'Failed to load review';
			}
		} catch (err) {
			console.error('Error loading review:', err);
			error = 'An error occurred while loading the review';
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Update review status
	 */
	async function updateReviewStatus(status: string) {
		if (!reviewId) return;

		isLoading = true;
		error = '';

		try {
			const response = await api.patch(`/api/admin/ai-content-reviews/${reviewId}`, {
				reviewStatus: status,
				reviewNotes,
				generatedContent: editedContent
			});

			if (response.success) {
				reviewData.reviewStatus = status;
				reviewData.reviewNotes = reviewNotes;
				reviewData.generatedContent = editedContent;
				
				dispatch('updated', {
					reviewId,
					status,
					reviewData
				});

				if (status === 'approved') {
					dispatch('approved', {
						reviewId,
						content: editedContent,
						targetUser: reviewData.targetUser
					});
				}
			} else {
				error = response.error || 'Failed to update review';
			}
		} catch (err) {
			console.error('Error updating review:', err);
			error = 'An error occurred while updating the review';
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Close modal
	 */
	function closeModal() {
		showModal = false;
		dispatch('close');
	}

	/**
	 * Get status color class
	 */
	function getStatusColor(status: string) {
		const option = statusOptions.find(opt => opt.value === status);
		return option ? option.color : 'secondary';
	}

	// Load review when reviewId changes
	$: if (reviewId && showModal) {
		loadReview();
	}
</script>

{#if showModal}
	<div class="modal-overlay" onclick={closeModal}>
		<div class="modal-content" onclick={(e) => e.stopPropagation()}>
			<div class="modal-header">
				<h3>AI Content Review</h3>
				<button class="close-btn" onclick={closeModal} aria-label="Close modal">×</button>
			</div>

			<div class="modal-body">
				{#if isLoading}
					<LoadingSpinner message="Loading review..." />
				{:else if error}
					<ErrorMessage
						title="Error"
						message={error}
						type="error"
						dismissible={true}
						onDismiss={() => error = ''}
					/>
				{:else if reviewData}
					<div class="review-content">
						<!-- Review Header -->
						<div class="review-header">
							<div class="review-meta">
								<span class="content-type">{reviewData.contentType}</span>
								<span class="status-badge status-{getStatusColor(reviewData.reviewStatus)}">
									{statusOptions.find(opt => opt.value === reviewData.reviewStatus)?.label || reviewData.reviewStatus}
								</span>
								<span class="authenticity-score">
									Score: {reviewData.authenticityScore}%
								</span>
							</div>
							
							<div class="target-user">
								<strong>Target User:</strong>
								{reviewData.targetUser.displayName} (@{reviewData.targetUser.username})
								{#if reviewData.targetUser.isSimulated}🤖{/if}
							</div>
						</div>

						<!-- Original Prompt -->
						<div class="section">
							<h4>Original Prompt</h4>
							<div class="prompt-text">{reviewData.originalPrompt}</div>
						</div>

						<!-- AI Configuration -->
						<div class="section">
							<h4>AI Configuration</h4>
							<div class="ai-config">
								<span><strong>Tone:</strong> {reviewData.aiConfig.tone}</span>
								<span><strong>Length:</strong> {reviewData.aiConfig.length}</span>
								{#if reviewData.aiConfig.focusAreas && reviewData.aiConfig.focusAreas.length > 0}
									<span><strong>Focus Areas:</strong> {reviewData.aiConfig.focusAreas.join(', ')}</span>
								{/if}
							</div>
						</div>

						<!-- Generated Content (Editable) -->
						<div class="section">
							<h4>Generated Content</h4>
							
							{#if editedContent.title}
								<div class="form-group">
									<label for="edit-title">Title</label>
									<input
										id="edit-title"
										type="text"
										bind:value={editedContent.title}
										disabled={isLoading}
									/>
								</div>
							{/if}

							<div class="form-group">
								<label for="edit-content">Content</label>
								<textarea
									id="edit-content"
									bind:value={editedContent.content}
									rows="8"
									disabled={isLoading}
								></textarea>
							</div>

							{#if editedContent.description}
								<div class="form-group">
									<label for="edit-description">Description</label>
									<textarea
										id="edit-description"
										bind:value={editedContent.description}
										rows="3"
										disabled={isLoading}
									></textarea>
								</div>
							{/if}
						</div>

						<!-- Review Notes -->
						<div class="section">
							<h4>Review Notes</h4>
							<textarea
								bind:value={reviewNotes}
								placeholder="Add notes about this review..."
								rows="4"
								disabled={isLoading}
							></textarea>
						</div>

						<!-- Review Actions -->
						<div class="review-actions">
							<button
								class="btn success"
								onclick={() => updateReviewStatus('approved')}
								disabled={isLoading}
							>
								✓ Approve
							</button>
							
							<button
								class="btn info"
								onclick={() => updateReviewStatus('needs_revision')}
								disabled={isLoading}
							>
								✏️ Needs Revision
							</button>
							
							<button
								class="btn danger"
								onclick={() => updateReviewStatus('rejected')}
								disabled={isLoading}
							>
								✗ Reject
							</button>
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
{/if}

<style>
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 1rem;
	}

	.modal-content {
		background: var(--theme-bg-primary);
		border-radius: 8px;
		max-width: 800px;
		width: 100%;
		max-height: 90vh;
		overflow: hidden;
		box-shadow: 0 10px 30px var(--theme-shadow);
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1.5rem;
		border-bottom: 1px solid var(--theme-border);
	}

	.modal-header h3 {
		margin: 0;
		color: var(--theme-text-primary);
	}

	.close-btn {
		background: none;
		border: none;
		font-size: 1.5rem;
		cursor: pointer;
		color: var(--theme-text-secondary);
		padding: 0.25rem;
		border-radius: 4px;
		transition: background-color 0.2s ease;
	}

	.close-btn:hover {
		background: var(--theme-bg-tertiary);
	}

	.modal-body {
		padding: 1.5rem;
		max-height: calc(90vh - 100px);
		overflow-y: auto;
	}

	.review-content {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.review-header {
		padding: 1rem;
		background: var(--theme-bg-secondary);
		border-radius: 6px;
	}

	.review-meta {
		display: flex;
		gap: 1rem;
		margin-bottom: 0.5rem;
		flex-wrap: wrap;
	}

	.content-type {
		padding: 0.25rem 0.5rem;
		background: var(--theme-accent-primary);
		color: white;
		border-radius: 4px;
		font-size: 0.8rem;
		text-transform: uppercase;
		font-weight: 600;
	}

	.status-badge {
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.status-success { background: var(--theme-accent-success); color: white; }
	.status-warning { background: var(--theme-accent-warning); color: white; }
	.status-danger { background: var(--theme-accent-danger); color: white; }
	.status-info { background: var(--theme-accent-info); color: white; }

	.authenticity-score {
		padding: 0.25rem 0.5rem;
		background: var(--theme-bg-tertiary);
		border-radius: 4px;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.target-user {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	.section {
		border: 1px solid var(--theme-border);
		border-radius: 6px;
		padding: 1rem;
	}

	.section h4 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
	}

	.prompt-text {
		padding: 0.75rem;
		background: var(--theme-bg-secondary);
		border-radius: 4px;
		font-style: italic;
		color: var(--theme-text-secondary);
	}

	.ai-config {
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;
	}

	.ai-config span {
		font-size: 0.9rem;
		color: var(--theme-text-secondary);
	}

	.form-group {
		margin-bottom: 1rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 500;
		color: var(--theme-text-primary);
	}

	.form-group input,
	.form-group textarea {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		background: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 0.9rem;
		resize: vertical;
	}

	.form-group input:focus,
	.form-group textarea:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
		box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
	}

	.review-actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		padding-top: 1rem;
		border-top: 1px solid var(--theme-border);
	}

	.btn {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.btn.success {
		background: var(--theme-accent-success);
		color: white;
	}

	.btn.info {
		background: var(--theme-accent-info);
		color: white;
	}

	.btn.danger {
		background: var(--theme-accent-danger);
		color: white;
	}

	.btn:hover:not(:disabled) {
		transform: translateY(-1px);
		box-shadow: 0 2px 8px var(--theme-shadow-hover);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.modal-content {
			margin: 0.5rem;
			max-height: 95vh;
		}

		.modal-header,
		.modal-body {
			padding: 1rem;
		}

		.review-meta {
			flex-direction: column;
			gap: 0.5rem;
		}

		.ai-config {
			flex-direction: column;
			gap: 0.5rem;
		}

		.review-actions {
			flex-direction: column;
		}
	}
</style>
