import { theme, getSystemTheme, watchSystemTheme } from './theme';
import { get } from 'svelte/store';

describe('Theme Store', () => {
  beforeEach(() => {
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn()
      },
      writable: true
    });
    
    // Reset DOM
    document.documentElement.removeAttribute('data-theme');
  });
  
  test('should initialize with default theme', () => {
    theme.init();
    expect(get(theme)).toBe('light');
  });
  
  test('should toggle theme correctly', () => {
    theme.setTheme('light');
    theme.toggle();
    expect(get(theme)).toBe('dark');
    expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
  });
});