import { theme, getSystemTheme, watchSystemTheme, type ThemeState } from './theme';
import { get } from 'svelte/store';
import { describe, test, expect, beforeEach, vi } from 'vitest';

describe('Theme Store', () => {
  let mockLocalStorage: { [key: string]: string };

  beforeEach(() => {
    // Reset mock localStorage
    mockLocalStorage = {};

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn((key: string) => mockLocalStorage[key] || null),
        setItem: vi.fn((key: string, value: string) => {
          mockLocalStorage[key] = value;
        }),
        removeItem: vi.fn((key: string) => {
          delete mockLocalStorage[key];
        })
      },
      writable: true
    });

    // Mock matchMedia
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    // Reset DOM
    document.documentElement.removeAttribute('data-theme');
    document.documentElement.removeAttribute('data-theme-preference');
  });

  describe('Initialization', () => {
    test('should initialize with auto preference and system theme by default', () => {
      theme.init();
      const state = get(theme);
      expect(state.preference).toBe('auto');
      expect(state.systemTheme).toBe('dark'); // Based on our mock
      expect(state.current).toBe('dark');
    });

    test('should restore preference from localStorage', () => {
      mockLocalStorage['fwfc-theme-preference'] = 'light';
      theme.init();
      const state = get(theme);
      expect(state.preference).toBe('light');
      expect(state.current).toBe('light');
    });

    test('should handle invalid localStorage values gracefully', () => {
      mockLocalStorage['fwfc-theme-preference'] = 'invalid';
      theme.init();
      const state = get(theme);
      expect(state.preference).toBe('auto');
    });
  });

  describe('Theme Preference Management', () => {
    test('should set preference and persist to localStorage', () => {
      theme.setPreference('dark');
      const state = get(theme);
      expect(state.preference).toBe('dark');
      expect(state.current).toBe('dark');
      expect(mockLocalStorage['fwfc-theme-preference']).toBe('dark');
    });

    test('should use system theme when preference is auto', () => {
      theme.setPreference('auto');
      const state = get(theme);
      expect(state.preference).toBe('auto');
      expect(state.current).toBe(state.systemTheme);
    });
  });

  describe('Theme Toggle', () => {
    test('should cycle through light -> dark -> auto', () => {
      // Start with light
      theme.setPreference('light');

      // Toggle to dark
      theme.toggle();
      let state = get(theme);
      expect(state.preference).toBe('dark');
      expect(state.current).toBe('dark');

      // Toggle to auto
      theme.toggle();
      state = get(theme);
      expect(state.preference).toBe('auto');
      expect(state.current).toBe(state.systemTheme);

      // Toggle back to light
      theme.toggle();
      state = get(theme);
      expect(state.preference).toBe('light');
      expect(state.current).toBe('light');
    });
  });

  describe('System Theme Detection', () => {
    test('should detect system theme correctly', () => {
      const systemTheme = getSystemTheme();
      expect(systemTheme).toBe('dark'); // Based on our mock
    });

    test('should update system theme and current theme when in auto mode', () => {
      theme.setPreference('auto');
      theme.updateSystemTheme('light');

      const state = get(theme);
      expect(state.systemTheme).toBe('light');
      expect(state.current).toBe('light');
    });

    test('should not change current theme when not in auto mode', () => {
      theme.setPreference('dark');
      theme.updateSystemTheme('light');

      const state = get(theme);
      expect(state.systemTheme).toBe('light');
      expect(state.current).toBe('dark'); // Should remain dark
    });
  });

  describe('DOM Integration', () => {
    test('should apply data-theme attribute correctly', () => {
      theme.setPreference('dark');
      expect(document.documentElement.getAttribute('data-theme')).toBe('dark');

      theme.setPreference('light');
      expect(document.documentElement.hasAttribute('data-theme')).toBe(false);
    });

    test('should add transition class during theme changes', () => {
      theme.setPreference('dark');
      expect(document.documentElement.classList.contains('theme-transitioning')).toBe(true);

      // Should be removed after timeout
      setTimeout(() => {
        expect(document.documentElement.classList.contains('theme-transitioning')).toBe(false);
      }, 350);
    });
  });

  describe('System Theme Watcher', () => {
    test('should set up system theme watcher correctly', () => {
      const callback = vi.fn();
      const cleanup = watchSystemTheme(callback);

      expect(typeof cleanup).toBe('function');
      expect(window.matchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
    });

    test('should handle errors in system theme detection gracefully', () => {
      // Mock matchMedia to throw an error
      Object.defineProperty(window, 'matchMedia', {
        value: () => {
          throw new Error('matchMedia not supported');
        }
      });

      const systemTheme = getSystemTheme();
      expect(systemTheme).toBe('light'); // Should fallback to light
    });
  });

  describe('Backward Compatibility', () => {
    test('should provide getCurrentTheme method', () => {
      theme.setPreference('dark');
      const currentTheme = theme.getCurrentTheme();
      expect(currentTheme).toBe('dark');
    });
  });
});