import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';
import { generateContentWithReview } from '$lib/server/services/ai';
import { checkRateLimit, recordRequest } from '$lib/server/services/rateLimiting';

// POST /api/admin/generate-content - Generate content using AI
export const POST: RequestHandler = async ({ request, locals, getClientAddress }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    // Check rate limits
    const clientIP = getClientAddress();
    const userId = locals.user.id;

    // Check per-user rate limit
    const userRateLimit = checkRateLimit('ai_generation_per_user', userId.toString(), userId);
    if (!userRateLimit.allowed) {
      return json({
        success: false,
        error: 'Rate limit exceeded. Please try again later.',
        resetTime: userRateLimit.resetTime
      }, { status: 429 });
    }

    // Check per-IP rate limit
    const ipRateLimit = checkRateLimit('ai_generation_per_ip', clientIP, userId);
    if (!ipRateLimit.allowed) {
      return json({
        success: false,
        error: 'Rate limit exceeded for this IP address. Please try again later.',
        resetTime: ipRateLimit.resetTime
      }, { status: 429 });
    }

    // Check global rate limit
    const globalRateLimit = checkRateLimit('ai_generation_global', 'global', userId);
    if (!globalRateLimit.allowed) {
      return json({
        success: false,
        error: 'System is currently busy. Please try again later.',
        resetTime: globalRateLimit.resetTime
      }, { status: 429 });
    }

    const body = await request.json();
    const { contentType, asUserId, prompt, tone, length, focusAreas, includePersonality, requireReview } = body;

    // Validate required fields
    if (!contentType || !asUserId || !prompt) {
      return json({
        success: false,
        error: 'Content type, user ID, and prompt are required'
      }, { status: 400 });
    }

    // Get target user for personalization
    const targetUser = await db.select()
      .from(users)
      .where(eq(users.id, asUserId))
      .limit(1);

    if (targetUser.length === 0) {
      return json({
        success: false,
        error: 'Target user not found'
      }, { status: 404 });
    }

    // Generate content based on type with review workflow
    const result = await generateContentWithReview({
      contentType,
      prompt,
      tone: tone || 'casual',
      length: length || 'medium',
      userContext: targetUser[0],
      focusAreas: focusAreas || [],
      includePersonality: includePersonality !== false
    }, locals.user.id, requireReview !== false);

    // Record successful requests for rate limiting
    recordRequest('ai_generation_per_user', userId.toString(), true, userId);
    recordRequest('ai_generation_per_ip', clientIP, true, userId);
    recordRequest('ai_generation_global', 'global', true, userId);

    return json({
      success: true,
      data: {
        content: result.content,
        authenticityScore: result.content.authenticityScore || 0,
        reviewId: result.reviewId,
        requiresReview: !!result.reviewId,
        moderationResult: result.content.moderationResult
      }
    });
  } catch (error) {
    // Record failed requests for rate limiting
    const clientIP = getClientAddress();
    const userId = locals.user?.id;

    if (userId) {
      recordRequest('ai_generation_per_user', userId.toString(), false, userId);
      recordRequest('ai_generation_per_ip', clientIP, false, userId);
      recordRequest('ai_generation_global', 'global', false, userId);
    }

    logger.error('Error generating content:', error);
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate content'
    }, { status: 500 });
  }
};