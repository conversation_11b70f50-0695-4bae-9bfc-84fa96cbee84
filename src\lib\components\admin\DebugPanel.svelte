<script>
  import { onMount } from 'svelte';
  import logger from '$lib/client/logger';
  
  // State
  let logs = [];
  let showPanel = false;
  let filter = 'all';
  
  // Toggle panel visibility
  function togglePanel() {
    showPanel = !showPanel;
  }
  
  // Filter logs
  $: filteredLogs = filter === 'all' 
    ? logs 
    : logs.filter(log => log.level.toLowerCase() === filter.toLowerCase());
  
  // Override console methods to capture logs
  onMount(() => {
    // Store original console methods
    const originalConsole = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error,
      debug: console.debug
    };
    
    // Override console methods
    console.log = (...args) => {
      logs = [...logs, { level: 'LOG', message: args.join(' '), timestamp: new Date() }];
      originalConsole.log(...args);
    };
    
    console.info = (...args) => {
      logs = [...logs, { level: 'INFO', message: args.join(' '), timestamp: new Date() }];
      originalConsole.info(...args);
    };
    
    console.warn = (...args) => {
      logs = [...logs, { level: 'WARN', message: args.join(' '), timestamp: new Date() }];
      originalConsole.warn(...args);
    };
    
    console.error = (...args) => {
      logs = [...logs, { level: 'ERROR', message: args.join(' '), timestamp: new Date() }];
      originalConsole.error(...args);
    };
    
    console.debug = (...args) => {
      logs = [...logs, { level: 'DEBUG', message: args.join(' '), timestamp: new Date() }];
      originalConsole.debug(...args);
    };
    
    // Restore original console methods on cleanup
    return () => {
      console.log = originalConsole.log;
      console.info = originalConsole.info;
      console.warn = originalConsole.warn;
      console.error = originalConsole.error;
      console.debug = originalConsole.debug;
    };
  });
  
  // Clear logs
  function clearLogs() {
    logs = [];
  }
  
  // Download logs as JSON
  function downloadLogs() {
    const data = JSON.stringify(logs, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `logs-${new Date().toISOString()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
</script>

<!-- Debug panel toggle button -->
<button class="debug-toggle" on:click={togglePanel}>
  {showPanel ? 'Hide' : 'Show'} Debug Panel
</button>

<!-- Debug panel -->
{#if showPanel}
  <div class="debug-panel">
    <div class="debug-header">
      <h3>Debug Console</h3>
      <div class="debug-controls">
        <select bind:value={filter}>
          <option value="all">All Logs</option>
          <option value="log">Log</option>
          <option value="info">Info</option>
          <option value="warn">Warnings</option>
          <option value="error">Errors</option>
          <option value="debug">Debug</option>
        </select>
        <button on:click={clearLogs}>Clear</button>
        <button on:click={downloadLogs}>Download</button>
        <button on:click={togglePanel}>Close</button>
      </div>
    </div>
    
    <div class="debug-content">
      {#if filteredLogs.length === 0}
        <div class="empty-logs">No logs to display</div>
      {:else}
        {#each filteredLogs as log}
          <div class="log-entry" class:error={log.level === 'ERROR'} class:warn={log.level === 'WARN'}>
            <span class="log-time">{log.timestamp.toLocaleTimeString()}</span>
            <span class="log-level">[{log.level}]</span>
            <span class="log-message">{log.message}</span>
          </div>
        {/each}
      {/if}
    </div>
  </div>
{/if}

<style>
  .debug-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    background-color: #333;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0.7;
  }
  
  .debug-toggle:hover {
    opacity: 1;
  }
  
  .debug-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 300px;
    background-color: #1e1e1e;
    color: #f0f0f0;
    z-index: 9998;
    display: flex;
    flex-direction: column;
    border-top: 2px solid #444;
  }
  
  .debug-header {
    padding: 8px 12px;
    background-color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .debug-header h3 {
    margin: 0;
  }
  
  .debug-controls {
    display: flex;
    gap: 8px;
  }
  
  .debug-controls button, .debug-controls select {
    background-color: #444;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
  }
  
  .debug-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    font-family: monospace;
    font-size: 12px;
  }
  
  .log-entry {
    padding: 4px 0;
    border-bottom: 1px solid #333;
    white-space: pre-wrap;
    word-break: break-all;
  }
  
  .log-time {
    color: #888;
    margin-right: 8px;
  }
  
  .log-level {
    font-weight: bold;
    margin-right: 8px;
  }
  
  .error {
    color: #ff6b6b;
  }
  
  .warn {
    color: #ffd166;
  }
  
  .empty-logs {
    text-align: center;
    color: #888;
    padding: 20px;
  }
</style>
