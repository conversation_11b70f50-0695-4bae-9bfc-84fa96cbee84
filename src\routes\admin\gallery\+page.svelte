<script>
	import { onMount } from 'svelte';
	import { api } from '$lib/utils/api';
	import logger from '$lib/client/logger';

	// Create a logger with context
	const log = logger.withContext('gallery-admin');

	// State variables
	/** @type {Array<{id: number, title: string, description?: string, imageUrl: string, thumbnailUrl: string, authorId?: number, createdAt: string, updatedAt: string, published: boolean}>} */
	let galleryItems = [];
	let loading = true;
	let syncing = false;
	/** @type {string|null} */
	let error = null;
	/** @type {string|null} */
	let syncMessage = null;

	// Fetch gallery items from the API
	async function fetchGalleryItems() {
		loading = true;
		error = null;

		log.info('Fetching gallery items');
		const { data, error: apiError } = await api.get('/api/gallery?limit=100&all=true');

		if (apiError) {
			log.error('Failed to fetch gallery items', apiError);
			error = apiError;
		} else {
			log.debug('Gallery items fetched successfully', { count: data?.length });
			galleryItems = data || [];
		}

		loading = false;
	}

	/**
	 * Delete a gallery item
	 * @param {number} id - The ID of the gallery item to delete
	 */
	async function deleteGalleryItem(id) {
		if (!confirm('Are you sure you want to delete this gallery item?')) {
			return;
		}

		log.info(`Deleting gallery item ${id}`);
		const { success, error: apiError } = await api.delete(`/api/gallery/${id}`);

		if (success) {
			log.info(`Gallery item ${id} deleted successfully`);
			// Remove the item from the list
			galleryItems = galleryItems.filter(item => item.id !== id);
		} else {
			log.error(`Failed to delete gallery item ${id}`, apiError);
			alert(apiError || 'Failed to delete gallery item');
		}
	}

	/**
	 * Toggle the published status of a gallery item
	 * @param {{id: number, title: string, description?: string, imageUrl: string, thumbnailUrl: string, published: boolean}} item - The gallery item to update
	 */
	async function togglePublished(item) {
		const newStatus = !item.published;
		log.info(`Toggling gallery item ${item.id} published status to ${newStatus}`);

		const { success, data, error: apiError } = await api.put(`/api/gallery/${item.id}`, {
			...item,
			published: newStatus
		});

		if (success && data) {
			log.info(`Gallery item ${item.id} updated successfully`);
			// Update the item in the list
			galleryItems = galleryItems.map(i =>
				i.id === item.id ? data : i
			);
		} else {
			log.error(`Failed to update gallery item ${item.id}`, apiError);
			alert(apiError || 'Failed to update gallery item');
		}
	}

	/**
	 * Sync uploaded files with the database
	 */
	async function syncUploads() {
		syncing = true;
		syncMessage = null;
		error = null;

		log.info('Syncing uploaded files with database');
		const { success, data, error: apiError } = await api.post('/api/admin/sync-uploads');

		if (success && data) {
			log.info('Sync completed successfully', data);
			syncMessage = `Sync completed: ${data.created} items created, ${data.skipped} skipped, ${data.processed} total processed`;

			if (data.errors && data.errors.length > 0) {
				syncMessage += `. Errors: ${data.errors.join(', ')}`;
			}

			// Refresh the gallery items
			await fetchGalleryItems();
		} else {
			log.error('Failed to sync uploads', apiError);
			error = apiError || 'Failed to sync uploads';
		}

		syncing = false;
	}

	// Load gallery items on mount
	onMount(() => {
		fetchGalleryItems();
	});
</script>

<svelte:head>
	<title>Gallery Management - Admin</title>
</svelte:head>

<div class="gallery-admin-container">
	<div class="header">
		<h1>Gallery Management</h1>
		<div class="header-actions">
			<button class="btn secondary" on:click={syncUploads} disabled={syncing}>
				{syncing ? 'Syncing...' : 'Sync Uploads'}
			</button>
			<a href="/admin/media" class="btn secondary">Media Library</a>
			<a href="/admin/gallery/upload" class="btn primary">Upload New Images</a>
		</div>
	</div>

	{#if error}
		<div class="error-message">
			<p>{error}</p>
			<button class="btn secondary" on:click={fetchGalleryItems}>Try Again</button>
		</div>
	{/if}

	{#if syncMessage}
		<div class="success-message">
			<p>{syncMessage}</p>
			<button class="btn secondary" on:click={() => syncMessage = null}>Dismiss</button>
		</div>
	{/if}

	{#if loading}
		<div class="loading">
			<p>Loading gallery items...</p>
		</div>
	{:else if galleryItems.length === 0}
		<div class="empty-state">
			<p>No gallery items found. Upload some images to get started!</p>
			<a href="/admin/gallery/upload" class="btn primary">Upload Images</a>
		</div>
	{:else}
		<div class="gallery-grid">
			{#each galleryItems as item}
				<div class="gallery-item" class:unpublished={!item.published}>
					<div class="gallery-image">
						<img src={item.thumbnailUrl} alt={item.title} />
					</div>
					<div class="gallery-info">
						<h3>{item.title}</h3>
						{#if item.description}
							<p class="description">{item.description}</p>
						{/if}
						<p class="date">Created: {new Date(item.createdAt).toLocaleDateString()}</p>
						<div class="status">
							Status:
							<span class={item.published ? 'published' : 'draft'}>
								{item.published ? 'Published' : 'Draft'}
							</span>
						</div>
					</div>
					<div class="gallery-actions">
						<button
							class="btn icon-btn"
							title={item.published ? 'Unpublish' : 'Publish'}
							on:click={() => togglePublished(item)}
						>
							{item.published ? '👁️' : '👁️‍🗨️'}
						</button>
						<a
							href={`/admin/gallery/edit/${item.id}`}
							class="btn icon-btn"
							title="Edit"
						>
							✏️
						</a>
						<button
							class="btn icon-btn delete"
							title="Delete"
							on:click={() => deleteGalleryItem(item.id)}
						>
							🗑️
						</button>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>

<style>
	.gallery-admin-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.header-actions {
		display: flex;
		gap: 1rem;
		align-items: center;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border: 1px solid var(--theme-accent-primary);
	}

	.primary:hover {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
	}

	.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border: 1px solid var(--theme-border);
	}

	.secondary:hover {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
	}

	.icon-btn {
		padding: 0.5rem;
		font-size: 1.2rem;
		background: none;
		border: 1px solid var(--theme-border);
		color: var(--theme-text-primary);
	}

	.delete {
		color: var(--theme-accent-danger);
	}

	.error-message {
		background-color: var(--theme-accent-danger);
		color: white;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border: 1px solid var(--theme-accent-danger);
	}

	.success-message {
		background-color: var(--theme-accent-success);
		color: white;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border: 1px solid var(--theme-accent-success);
	}

	.loading, .empty-state {
		text-align: center;
		padding: 3rem;
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		color: var(--theme-text-primary);
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
	}

	.gallery-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
		gap: 1.5rem;
	}

	.gallery-item {
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 2px 4px var(--theme-shadow);
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		position: relative;
		color: var(--theme-text-primary);
	}

	.unpublished {
		background-color: var(--theme-bg-secondary);
		border: 1px dashed var(--theme-border);
		opacity: 0.8;
	}

	.gallery-image {
		height: 200px;
		overflow: hidden;
	}

	.gallery-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.gallery-info {
		padding: 1rem;
	}

	.gallery-info h3 {
		margin: 0 0 0.5rem 0;
	}

	.description {
		margin: 0.5rem 0;
		color: var(--theme-text-secondary);
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.date {
		font-size: 0.9rem;
		color: var(--theme-text-secondary);
		margin: 0.5rem 0;
	}

	.status {
		font-size: 0.9rem;
		margin-top: 0.5rem;
	}

	.published {
		color: var(--theme-accent-success);
		font-weight: bold;
	}

	.draft {
		color: var(--theme-accent-warning);
		font-weight: bold;
	}

	.gallery-actions {
		display: flex;
		justify-content: flex-end;
		padding: 0.5rem;
		background-color: var(--theme-bg-secondary);
		border-top: 1px solid var(--theme-border);
	}
</style>
