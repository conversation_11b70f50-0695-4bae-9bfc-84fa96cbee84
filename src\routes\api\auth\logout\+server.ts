import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// POST /api/auth/logout - Log out a user
export const POST: RequestHandler = async ({ cookies }) => {
  try {
    // Clear the session cookie
    cookies.delete('session_id', { path: '/' });
    
    return json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Error logging out:', error);
    return json({
      success: false,
      error: 'Failed to log out'
    }, { status: 500 });
  }
};
