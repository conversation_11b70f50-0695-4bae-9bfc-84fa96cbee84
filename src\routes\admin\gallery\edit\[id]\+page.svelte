<script>
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	
	// Get the gallery item ID from the URL
	export let data;
	const { id } = data;
	
	// State variables
	let galleryItem = null;
	let loading = true;
	let saving = false;
	let error = null;
	let successMessage = null;
	
	// Form data
	let title = '';
	let description = '';
	let category = 'tv';
	let published = false;
	let imageUrl = '';
	let thumbnailUrl = '';
	
	// Available categories
	const categories = [
		{ id: 'tv', name: 'TV Shows' },
		{ id: 'movies', name: 'Movies' },
		{ id: 'music', name: 'Music' },
		{ id: 'events', name: 'Events' },
		{ id: 'photoshoots', name: 'Photoshoots' },
		{ id: 'directing', name: 'Directing' }
	];
	
	// Fetch the gallery item from the API
	async function fetchGalleryItem() {
		try {
			loading = true;
			error = null;
			
			const response = await fetch(`/api/gallery/${id}`);
			
			if (!response.ok) {
				throw new Error('Failed to fetch gallery item');
			}
			
			const data = await response.json();
			galleryItem = data.data;
			
			// Populate form fields
			title = galleryItem.title || '';
			description = galleryItem.description || '';
			category = galleryItem.category || 'tv';
			published = galleryItem.published || false;
			imageUrl = galleryItem.imageUrl || '';
			thumbnailUrl = galleryItem.thumbnailUrl || '';
		} catch (err) {
			console.error('Error fetching gallery item:', err);
			error = err.message || 'Failed to load gallery item';
		} finally {
			loading = false;
		}
	}
	
	// Update the gallery item
	async function updateGalleryItem() {
		try {
			saving = true;
			error = null;
			successMessage = null;
			
			const response = await fetch(`/api/gallery/${id}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					title,
					description,
					category,
					published,
					imageUrl,
					thumbnailUrl
				})
			});
			
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to update gallery item');
			}
			
			const data = await response.json();
			galleryItem = data.data;
			
			successMessage = 'Gallery item updated successfully!';
			
			// Hide success message after 3 seconds
			setTimeout(() => {
				successMessage = null;
			}, 3000);
		} catch (err) {
			console.error('Error updating gallery item:', err);
			error = err.message || 'Failed to update gallery item';
		} finally {
			saving = false;
		}
	}
	
	// Handle form submission
	function handleSubmit(event) {
		event.preventDefault();
		updateGalleryItem();
	}
	
	// Load gallery item on mount
	onMount(() => {
		fetchGalleryItem();
	});
</script>

<svelte:head>
	<title>Edit Gallery Item - Admin</title>
</svelte:head>

<div class="edit-container">
	<div class="header">
		<h1>Edit Gallery Item</h1>
		<a href="/admin/gallery" class="btn secondary">Back to Gallery</a>
	</div>
	
	{#if error}
		<div class="error-message">
			<p>{error}</p>
		</div>
	{/if}
	
	{#if successMessage}
		<div class="success-message">
			<p>{successMessage}</p>
		</div>
	{/if}
	
	{#if loading}
		<div class="loading">
			<p>Loading gallery item...</p>
		</div>
	{:else if galleryItem}
		<div class="edit-form-container">
			<div class="image-preview">
				<img src={imageUrl} alt={title} />
			</div>
			
			<form on:submit={handleSubmit} class="edit-form">
				<div class="form-group">
					<label for="title">Title *</label>
					<input 
						type="text" 
						id="title" 
						bind:value={title} 
						required 
						disabled={saving}
					/>
				</div>
				
				<div class="form-group">
					<label for="description">Description</label>
					<textarea 
						id="description" 
						bind:value={description} 
						rows="4" 
						disabled={saving}
					></textarea>
				</div>
				
				<div class="form-group">
					<label for="category">Category</label>
					<select id="category" bind:value={category} disabled={saving}>
						{#each categories as cat}
							<option value={cat.id}>{cat.name}</option>
						{/each}
					</select>
				</div>
				
				<div class="form-group checkbox">
					<label>
						<input type="checkbox" bind:checked={published} disabled={saving} />
						Published
					</label>
				</div>
				
				<div class="form-group">
					<label for="imageUrl">Image URL</label>
					<input 
						type="text" 
						id="imageUrl" 
						bind:value={imageUrl} 
						disabled={saving}
					/>
				</div>
				
				<div class="form-group">
					<label for="thumbnailUrl">Thumbnail URL</label>
					<input 
						type="text" 
						id="thumbnailUrl" 
						bind:value={thumbnailUrl} 
						disabled={saving}
					/>
				</div>
				
				<div class="form-actions">
					<button type="submit" class="btn primary" disabled={saving}>
						{#if saving}
							Saving...
						{:else}
							Save Changes
						{/if}
					</button>
					<button 
						type="button" 
						class="btn danger" 
						disabled={saving}
						on:click={() => {
							if (confirm('Are you sure you want to delete this gallery item?')) {
								fetch(`/api/gallery/${id}`, { method: 'DELETE' })
									.then(() => goto('/admin/gallery'))
									.catch(err => {
										console.error('Error deleting gallery item:', err);
										error = 'Failed to delete gallery item';
									});
							}
						}}
					>
						Delete Item
					</button>
				</div>
			</form>
		</div>
	{:else}
		<div class="not-found">
			<p>Gallery item not found.</p>
			<a href="/admin/gallery" class="btn primary">Back to Gallery</a>
		</div>
	{/if}
</div>

<style>
	.edit-container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 2rem;
	}
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}
	
	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}
	
	.primary {
		background-color: #4caf50;
		color: white;
	}
	
	.secondary {
		background-color: #f0f0f0;
		color: #333;
	}
	
	.danger {
		background-color: #f44336;
		color: white;
	}
	
	.error-message {
		background-color: #ffebee;
		color: #c62828;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
	}
	
	.success-message {
		background-color: #e8f5e9;
		color: #2e7d32;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
	}
	
	.loading, .not-found {
		text-align: center;
		padding: 3rem;
		background-color: #f5f5f5;
		border-radius: 4px;
	}
	
	.not-found {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
	}
	
	.edit-form-container {
		display: grid;
		grid-template-columns: 1fr 2fr;
		gap: 2rem;
	}
	
	.image-preview {
		background-color: #f5f5f5;
		border-radius: 8px;
		overflow: hidden;
		height: fit-content;
	}
	
	.image-preview img {
		width: 100%;
		display: block;
	}
	
	.edit-form {
		background-color: #f9f9f9;
		padding: 1.5rem;
		border-radius: 8px;
	}
	
	.form-group {
		margin-bottom: 1.5rem;
	}
	
	label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: bold;
	}
	
	input[type="text"],
	textarea,
	select {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
	}
	
	.checkbox {
		display: flex;
		align-items: center;
	}
	
	.checkbox label {
		display: flex;
		align-items: center;
		font-weight: normal;
	}
	
	.checkbox input {
		margin-right: 0.5rem;
	}
	
	.form-actions {
		display: flex;
		gap: 1rem;
	}
	
	@media (max-width: 768px) {
		.edit-form-container {
			grid-template-columns: 1fr;
		}
	}
</style>
