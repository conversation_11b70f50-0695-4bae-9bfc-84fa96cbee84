import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { db } from '$lib/server/db';
import { users, scheduledContent, news, comments } from '$lib/server/db/schema';
import { eq, lte } from 'drizzle-orm';

describe('Content Scheduling System', () => {
  let adminUserId: number;
  let simulatedUserId: number;
  let scheduledItemId: number;

  beforeEach(async () => {
    // Create test admin user
    const adminResult = await db.insert(users).values({
      username: 'testadmin',
      displayName: 'Test Admin',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
      role: 'admin',
      status: 'active',
      isSimulated: false
    }).returning();
    adminUserId = adminResult[0].id;

    // Create test simulated user
    const simulatedResult = await db.insert(users).values({
      username: 'simulateduser',
      displayName: 'Simulated User',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
      role: 'user',
      status: 'active',
      isSimulated: true
    }).returning();
    simulatedUserId = simulatedResult[0].id;
  });

  afterEach(async () => {
    // Clean up test data
    if (scheduledItemId) {
      await db.delete(scheduledContent).where(eq(scheduledContent.id, scheduledItemId));
    }
    await db.delete(scheduledContent).where(eq(scheduledContent.createdByUserId, adminUserId));
    await db.delete(comments).where(eq(comments.userId, simulatedUserId));
    await db.delete(news).where(eq(news.authorId, simulatedUserId));
    await db.delete(users).where(eq(users.id, simulatedUserId));
    await db.delete(users).where(eq(users.id, adminUserId));
  });

  describe('Scheduled Content Creation', () => {
    it('should create scheduled news article', async () => {
      const futureDate = new Date(Date.now() + 86400000).toISOString(); // 1 day from now
      const contentData = {
        title: 'Scheduled News Article',
        content: 'This article will be published later.',
        imageUrl: 'https://example.com/scheduled.jpg',
        published: true
      };

      const result = await db.insert(scheduledContent).values({
        contentType: 'news',
        contentData: JSON.stringify(contentData),
        asUserId: simulatedUserId,
        scheduledFor: futureDate,
        createdByUserId: adminUserId,
        status: 'pending'
      }).returning();

      scheduledItemId = result[0].id;

      expect(result[0].contentType).toBe('news');
      expect(result[0].asUserId).toBe(simulatedUserId);
      expect(result[0].status).toBe('pending');
      expect(result[0].scheduledFor).toBe(futureDate);

      const parsedData = JSON.parse(result[0].contentData);
      expect(parsedData.title).toBe(contentData.title);
    });

    it('should create scheduled comment', async () => {
      const futureDate = new Date(Date.now() + 3600000).toISOString(); // 1 hour from now
      const contentData = {
        content: 'This is a scheduled comment.',
        itemType: 'news',
        itemId: 1,
        approved: true
      };

      const result = await db.insert(scheduledContent).values({
        contentType: 'comment',
        contentData: JSON.stringify(contentData),
        asUserId: simulatedUserId,
        scheduledFor: futureDate,
        createdByUserId: adminUserId,
        status: 'pending'
      }).returning();

      scheduledItemId = result[0].id;

      expect(result[0].contentType).toBe('comment');
      const parsedData = JSON.parse(result[0].contentData);
      expect(parsedData.content).toBe(contentData.content);
      expect(parsedData.itemType).toBe('news');
    });
  });

  describe('Scheduled Content Status Management', () => {
    beforeEach(async () => {
      const futureDate = new Date(Date.now() + 86400000).toISOString();
      const result = await db.insert(scheduledContent).values({
        contentType: 'news',
        contentData: JSON.stringify({ title: 'Test', content: 'Test content' }),
        asUserId: simulatedUserId,
        scheduledFor: futureDate,
        createdByUserId: adminUserId,
        status: 'pending'
      }).returning();
      scheduledItemId = result[0].id;
    });

    it('should update status to published', async () => {
      const publishedAt = new Date().toISOString();

      await db.update(scheduledContent)
        .set({
          status: 'published',
          publishedAt,
          updatedAt: new Date().toISOString()
        })
        .where(eq(scheduledContent.id, scheduledItemId));

      const updated = await db.select()
        .from(scheduledContent)
        .where(eq(scheduledContent.id, scheduledItemId))
        .limit(1);

      expect(updated[0].status).toBe('published');
      expect(updated[0].publishedAt).toBe(publishedAt);
    });

    it('should update status to failed with error message', async () => {
      const errorMessage = 'Failed to publish: Target user not found';

      await db.update(scheduledContent)
        .set({
          status: 'failed',
          errorMessage,
          updatedAt: new Date().toISOString()
        })
        .where(eq(scheduledContent.id, scheduledItemId));

      const updated = await db.select()
        .from(scheduledContent)
        .where(eq(scheduledContent.id, scheduledItemId))
        .limit(1);

      expect(updated[0].status).toBe('failed');
      expect(updated[0].errorMessage).toBe(errorMessage);
    });

    it('should update status to cancelled', async () => {
      await db.update(scheduledContent)
        .set({
          status: 'cancelled',
          updatedAt: new Date().toISOString()
        })
        .where(eq(scheduledContent.id, scheduledItemId));

      const updated = await db.select()
        .from(scheduledContent)
        .where(eq(scheduledContent.id, scheduledItemId))
        .limit(1);

      expect(updated[0].status).toBe('cancelled');
    });
  });

  describe('Scheduled Content Processing', () => {
    it('should identify content ready for publishing', async () => {
      const pastDate = new Date(Date.now() - 3600000).toISOString(); // 1 hour ago
      const futureDate = new Date(Date.now() + 3600000).toISOString(); // 1 hour from now

      // Create past scheduled item (ready for publishing)
      const pastResult = await db.insert(scheduledContent).values({
        contentType: 'news',
        contentData: JSON.stringify({ title: 'Past Item', content: 'Ready to publish' }),
        asUserId: simulatedUserId,
        scheduledFor: pastDate,
        createdByUserId: adminUserId,
        status: 'pending'
      }).returning();

      // Create future scheduled item (not ready)
      const futureResult = await db.insert(scheduledContent).values({
        contentType: 'news',
        contentData: JSON.stringify({ title: 'Future Item', content: 'Not ready yet' }),
        asUserId: simulatedUserId,
        scheduledFor: futureDate,
        createdByUserId: adminUserId,
        status: 'pending'
      }).returning();

      // Query for items ready to publish
      const now = new Date().toISOString();
      const readyItems = await db.select()
        .from(scheduledContent)
        .where(
          eq(scheduledContent.status, 'pending') &&
          lte(scheduledContent.scheduledFor, now)
        );

      expect(readyItems.length).toBe(1);
      expect(readyItems[0].id).toBe(pastResult[0].id);

      // Clean up
      await db.delete(scheduledContent).where(eq(scheduledContent.id, pastResult[0].id));
      await db.delete(scheduledContent).where(eq(scheduledContent.id, futureResult[0].id));
    });

    it('should handle content data parsing', async () => {
      const contentData = {
        title: 'Complex Content',
        content: 'Content with special characters: "quotes", \'apostrophes\', & symbols',
        imageUrl: 'https://example.com/image.jpg',
        published: true
      };

      const result = await db.insert(scheduledContent).values({
        contentType: 'news',
        contentData: JSON.stringify(contentData),
        asUserId: simulatedUserId,
        scheduledFor: new Date().toISOString(),
        createdByUserId: adminUserId,
        status: 'pending'
      }).returning();

      scheduledItemId = result[0].id;

      // Parse the stored content data
      const parsedData = JSON.parse(result[0].contentData);
      expect(parsedData).toEqual(contentData);
      expect(parsedData.content).toContain('special characters');
    });
  });

  describe('Bulk Operations', () => {
    let scheduledIds: number[] = [];

    beforeEach(async () => {
      // Create multiple scheduled items
      const items = [
        { title: 'Item 1', content: 'Content 1' },
        { title: 'Item 2', content: 'Content 2' },
        { title: 'Item 3', content: 'Content 3' }
      ];

      for (const item of items) {
        const result = await db.insert(scheduledContent).values({
          contentType: 'news',
          contentData: JSON.stringify(item),
          asUserId: simulatedUserId,
          scheduledFor: new Date(Date.now() + 86400000).toISOString(),
          createdByUserId: adminUserId,
          status: 'pending'
        }).returning();
        scheduledIds.push(result[0].id);
      }
    });

    afterEach(async () => {
      // Clean up bulk test items
      for (const id of scheduledIds) {
        await db.delete(scheduledContent).where(eq(scheduledContent.id, id));
      }
      scheduledIds = [];
    });

    it('should cancel multiple scheduled items', async () => {
      // Cancel all items
      for (const id of scheduledIds) {
        await db.update(scheduledContent)
          .set({
            status: 'cancelled',
            updatedAt: new Date().toISOString()
          })
          .where(eq(scheduledContent.id, id));
      }

      // Verify all items are cancelled
      const cancelledItems = await db.select()
        .from(scheduledContent)
        .where(eq(scheduledContent.status, 'cancelled'));

      const cancelledIds = cancelledItems.map(item => item.id);
      for (const id of scheduledIds) {
        expect(cancelledIds).toContain(id);
      }
    });

    it('should retry failed scheduled items', async () => {
      // Mark items as failed
      for (const id of scheduledIds) {
        await db.update(scheduledContent)
          .set({
            status: 'failed',
            errorMessage: 'Test error',
            updatedAt: new Date().toISOString()
          })
          .where(eq(scheduledContent.id, id));
      }

      // Retry failed items
      for (const id of scheduledIds) {
        await db.update(scheduledContent)
          .set({
            status: 'pending',
            errorMessage: null,
            updatedAt: new Date().toISOString()
          })
          .where(eq(scheduledContent.id, id));
      }

      // Verify all items are pending again
      const retriedItems = await db.select()
        .from(scheduledContent)
        .where(eq(scheduledContent.status, 'pending'));

      const retriedIds = retriedItems.map(item => item.id);
      for (const id of scheduledIds) {
        expect(retriedIds).toContain(id);
      }
    });
  });

  describe('Data Integrity', () => {
    it('should maintain referential integrity with users', async () => {
      const result = await db.insert(scheduledContent).values({
        contentType: 'news',
        contentData: JSON.stringify({ title: 'Test', content: 'Test' }),
        asUserId: simulatedUserId,
        scheduledFor: new Date().toISOString(),
        createdByUserId: adminUserId,
        status: 'pending'
      }).returning();

      scheduledItemId = result[0].id;

      // Verify the user references exist
      const asUser = await db.select()
        .from(users)
        .where(eq(users.id, result[0].asUserId))
        .limit(1);

      const createdByUser = await db.select()
        .from(users)
        .where(eq(users.id, result[0].createdByUserId))
        .limit(1);

      expect(asUser.length).toBe(1);
      expect(createdByUser.length).toBe(1);
      expect(asUser[0].id).toBe(simulatedUserId);
      expect(createdByUser[0].id).toBe(adminUserId);
    });
  });
});
