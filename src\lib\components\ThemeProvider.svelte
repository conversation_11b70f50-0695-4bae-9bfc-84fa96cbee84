<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { theme, watchSystemTheme, type ThemeState } from '$lib/stores/theme';

  let cleanup: () => void;
  let isInitialized = false;
  let themeState: ThemeState;

  // Subscribe to theme state changes
  const unsubscribe = theme.subscribe((state) => {
    themeState = state;
  });

  onMount(() => {
    // Initialize theme system
    theme.init();
    isInitialized = true;

    // Watch for system theme changes
    cleanup = watchSystemTheme((newSystemTheme) => {
      theme.updateSystemTheme(newSystemTheme);
    });

    // Set initial theme attribute to prevent FOUC
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme-initialized', 'true');
    }
  });

  onDestroy(() => {
    if (cleanup) cleanup();
    unsubscribe();
  });

  // Reactive statement to apply theme changes
  $: if (isInitialized && themeState) {
    if (typeof document !== 'undefined') {
      // Apply theme class to html element for CSS targeting
      if (themeState.current === 'dark') {
        document.documentElement.setAttribute('data-theme', 'dark');
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.removeAttribute('data-theme');
        document.documentElement.classList.remove('dark');
      }

      // Set preference attribute for debugging/styling
      document.documentElement.setAttribute('data-theme-preference', themeState.preference);

      // Dispatch custom event for other components that might need to react
      document.dispatchEvent(new CustomEvent('themechange', {
        detail: {
          current: themeState.current,
          preference: themeState.preference,
          systemTheme: themeState.systemTheme
        }
      }));
    }
  }
</script>

<slot />