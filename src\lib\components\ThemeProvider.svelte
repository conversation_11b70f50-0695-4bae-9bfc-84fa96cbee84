<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { theme, watchSystemTheme } from '$lib/stores/theme';
  
  let cleanup: () => void;
  
  onMount(() => {
    theme.init();
    
    // Watch for system theme changes
    cleanup = watchSystemTheme((newTheme) => {
      // Only update if user hasn't explicitly set a preference
      if (!localStorage.getItem('fwfc-theme')) {
        theme.setTheme(newTheme);
      }
    });
  });
  
  onDestroy(() => {
    if (cleanup) cleanup();
  });
</script>

<slot />