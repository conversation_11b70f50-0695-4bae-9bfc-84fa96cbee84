/* Theme tokens for consistent design system */
:root {
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-2xl: 2rem;
  --font-size-3xl: 2.5rem;

  /* Font weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Borders */
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-full: 9999px;

  /* Border widths */
  --border-width-thin: 1px;
  --border-width-medium: 2px;
  --border-width-thick: 4px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms ease;
  --transition-slow: 500ms ease;
  --transition-theme: background-color 300ms ease, color 300ms ease, border-color 300ms ease, box-shadow 300ms ease;

  /* Z-index scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* ===== LIGHT THEME COLORS (Default) ===== */
  /* Background colors - WCAG AA compliant */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-tertiary: #f1f5f9;
  --color-bg-quaternary: #e2e8f0;
  --color-bg-overlay: rgba(255, 255, 255, 0.95);

  /* Text colors - WCAG AA compliant (4.5:1 contrast ratio) */
  --color-text-primary: #0f172a;      /* 19.07:1 contrast on white */
  --color-text-secondary: #475569;    /* 7.25:1 contrast on white */
  --color-text-tertiary: #64748b;     /* 5.74:1 contrast on white */
  --color-text-muted: #94a3b8;        /* 3.54:1 contrast - for large text only */
  --color-text-inverse: #ffffff;

  /* Border colors */
  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-tertiary: #94a3b8;
  --color-border-focus: #3b82f6;
  --color-border-error: #ef4444;

  /* Surface colors for cards, modals, etc. */
  --color-surface-primary: #ffffff;
  --color-surface-secondary: #f8fafc;
  --color-surface-tertiary: #f1f5f9;
  --color-surface-elevated: #ffffff;

  /* Interactive colors */
  --color-interactive-primary: #3b82f6;      /* Blue - 4.5:1 contrast */
  --color-interactive-primary-hover: #2563eb;
  --color-interactive-primary-active: #1d4ed8;
  --color-interactive-secondary: #6366f1;    /* Indigo */
  --color-interactive-secondary-hover: #4f46e5;

  /* Status colors - WCAG AA compliant */
  --color-success: #059669;          /* Green - 4.5:1 contrast */
  --color-success-bg: #ecfdf5;
  --color-success-border: #a7f3d0;
  --color-warning: #d97706;          /* Orange - 4.5:1 contrast */
  --color-warning-bg: #fffbeb;
  --color-warning-border: #fed7aa;
  --color-error: #dc2626;            /* Red - 4.5:1 contrast */
  --color-error-bg: #fef2f2;
  --color-error-border: #fecaca;
  --color-info: #0284c7;             /* Sky blue - 4.5:1 contrast */
  --color-info-bg: #f0f9ff;
  --color-info-border: #bae6fd;

  /* Shadow colors */
  --color-shadow-primary: rgba(15, 23, 42, 0.1);
  --color-shadow-secondary: rgba(15, 23, 42, 0.05);
  --color-shadow-hover: rgba(15, 23, 42, 0.15);
  --color-shadow-focus: rgba(59, 130, 246, 0.25);
}

/* ===== DARK THEME COLORS ===== */
[data-theme="dark"] {
  /* Background colors - Dark theme */
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-bg-quaternary: #475569;
  --color-bg-overlay: rgba(15, 23, 42, 0.95);

  /* Text colors - WCAG AA compliant on dark backgrounds */
  --color-text-primary: #f8fafc;      /* 18.07:1 contrast on dark bg */
  --color-text-secondary: #cbd5e1;    /* 12.63:1 contrast on dark bg */
  --color-text-tertiary: #94a3b8;     /* 7.25:1 contrast on dark bg */
  --color-text-muted: #64748b;        /* 4.78:1 contrast on dark bg */
  --color-text-inverse: #0f172a;

  /* Border colors - Dark theme */
  --color-border-primary: #334155;
  --color-border-secondary: #475569;
  --color-border-tertiary: #64748b;
  --color-border-focus: #60a5fa;
  --color-border-error: #f87171;

  /* Surface colors - Dark theme */
  --color-surface-primary: #1e293b;
  --color-surface-secondary: #334155;
  --color-surface-tertiary: #475569;
  --color-surface-elevated: #334155;

  /* Interactive colors - Dark theme adjusted */
  --color-interactive-primary: #60a5fa;      /* Lighter blue for dark bg */
  --color-interactive-primary-hover: #3b82f6;
  --color-interactive-primary-active: #2563eb;
  --color-interactive-secondary: #818cf8;    /* Lighter indigo */
  --color-interactive-secondary-hover: #6366f1;

  /* Status colors - Dark theme WCAG AA compliant */
  --color-success: #34d399;          /* Lighter green - 6.8:1 contrast */
  --color-success-bg: #064e3b;
  --color-success-border: #065f46;
  --color-warning: #fbbf24;          /* Lighter orange - 8.2:1 contrast */
  --color-warning-bg: #451a03;
  --color-warning-border: #92400e;
  --color-error: #f87171;            /* Lighter red - 6.8:1 contrast */
  --color-error-bg: #450a0a;
  --color-error-border: #991b1b;
  --color-info: #38bdf8;             /* Lighter sky blue - 6.8:1 contrast */
  --color-info-bg: #0c4a6e;
  --color-info-border: #0369a1;

  /* Shadow colors - Dark theme */
  --color-shadow-primary: rgba(0, 0, 0, 0.3);
  --color-shadow-secondary: rgba(0, 0, 0, 0.2);
  --color-shadow-hover: rgba(0, 0, 0, 0.4);
  --color-shadow-focus: rgba(96, 165, 250, 0.25);
}

/* ===== SEMANTIC COLOR MAPPINGS ===== */
:root {
  /* Component-specific semantic colors */
  --color-button-primary-bg: var(--color-interactive-primary);
  --color-button-primary-text: var(--color-text-inverse);
  --color-button-primary-border: var(--color-interactive-primary);
  --color-button-primary-hover-bg: var(--color-interactive-primary-hover);
  --color-button-primary-hover-border: var(--color-interactive-primary-hover);

  --color-button-secondary-bg: transparent;
  --color-button-secondary-text: var(--color-interactive-primary);
  --color-button-secondary-border: var(--color-interactive-primary);
  --color-button-secondary-hover-bg: var(--color-interactive-primary);
  --color-button-secondary-hover-text: var(--color-text-inverse);

  --color-input-bg: var(--color-surface-primary);
  --color-input-text: var(--color-text-primary);
  --color-input-border: var(--color-border-primary);
  --color-input-border-focus: var(--color-border-focus);
  --color-input-placeholder: var(--color-text-muted);

  --color-card-bg: var(--color-surface-primary);
  --color-card-border: var(--color-border-primary);
  --color-card-shadow: var(--color-shadow-primary);
  --color-card-hover-shadow: var(--color-shadow-hover);

  --color-nav-bg: var(--color-surface-primary);
  --color-nav-text: var(--color-text-primary);
  --color-nav-text-hover: var(--color-interactive-primary);
  --color-nav-border: var(--color-border-primary);

  --color-modal-bg: var(--color-surface-elevated);
  --color-modal-backdrop: rgba(15, 23, 42, 0.5);
  --color-modal-border: var(--color-border-primary);
  --color-modal-shadow: var(--color-shadow-xl);

  --color-tooltip-bg: var(--color-bg-quaternary);
  --color-tooltip-text: var(--color-text-primary);
  --color-tooltip-border: var(--color-border-secondary);

  --color-dropdown-bg: var(--color-surface-elevated);
  --color-dropdown-border: var(--color-border-primary);
  --color-dropdown-shadow: var(--color-shadow-lg);
  --color-dropdown-item-hover: var(--color-bg-secondary);
}

/* ===== SMOOTH THEME TRANSITIONS ===== */
.theme-transitioning,
.theme-transitioning * {
  transition: var(--transition-theme) !important;
}

/* Prevent flash of unstyled content during theme transitions */
html:not([data-theme]) {
  visibility: hidden;
}

html[data-theme] {
  visibility: visible;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .theme-transitioning,
  .theme-transitioning * {
    transition: none !important;
  }

  :root {
    --transition-fast: 0ms;
    --transition-normal: 0ms;
    --transition-slow: 0ms;
    --transition-theme: none;
  }
}