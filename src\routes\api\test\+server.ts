import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import type { <PERSON>quest<PERSON>and<PERSON> } from './$types';

// GET /api/test - Test the database connection
export const GET: RequestHandler = async () => {
  try {
    // Count the number of users in the database
    const result = await db.select({ count: users.id }).from(users);
    
    return json({
      success: true,
      message: 'Database connection successful',
      data: {
        userCount: result.length > 0 ? result[0].count : 0
      }
    });
  } catch (error) {
    console.error('Error testing database connection:', error);
    return json({
      success: false,
      error: 'Database connection failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
};
