<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import PasswordResetModal from '$lib/components/admin/PasswordResetModal.svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	// User data and state
	let users: any[] = [];
	let selectedUsers: Set<number> = new Set();
	let isLoading = true;
	let error = '';
	let successMessage = '';

	// Debug state
	let showDebugPanel = false;
	let debugLogs: string[] = [];
	let debugInfo: any = {
		currentUser: null,
		userRole: '',
		authStatus: '',
		buttonVisibility: {},
		componentStates: {},
		apiCalls: []
	};

	// Debug logging function
	function debugLog(message: string, data?: any) {
		const timestamp = new Date().toLocaleTimeString();
		const logEntry = `[${timestamp}] ${message}`;
		debugLogs = [logEntry, ...debugLogs.slice(0, 49)]; // Keep last 50 logs
		console.log(`🔍 DEBUG: ${message}`, data || '');

		// Update debug info
		debugInfo = { ...debugInfo };
	}

	// Pagination
	let currentPage = 1;
	let totalPages = 1;
	let totalUsers = 0;
	let usersPerPage = 20;

	// Filters and search
	let searchQuery = '';
	let roleFilter = '';
	let statusFilter = '';
	let typeFilter = '';
	let sortBy = 'createdAt';
	let sortOrder = 'desc';
	let dateFrom = '';
	let dateTo = '';

	// Modal states
	let showUserModal = false;
	let showBulkModal = false;
	let showPasswordResetModal = false;
	let selectedUser: any = null;
	let bulkAction = '';

	// User roles for filtering and editing
	const userRoles = [
		{ value: '', label: 'All Roles' },
		{ value: 'admin', label: 'Administrator' },
		{ value: 'moderator', label: 'Moderator' },
		{ value: 'user', label: 'Regular User' }
	];

	// User status options
	const userStatuses = [
		{ value: '', label: 'All Statuses' },
		{ value: 'active', label: 'Active' },
		{ value: 'inactive', label: 'Inactive' },
		{ value: 'suspended', label: 'Suspended' }
	];

	// User type filter
	const userTypes = [
		{ value: '', label: 'All Types' },
		{ value: 'real', label: 'Real Users' },
		{ value: 'simulated', label: 'Simulated Users' }
	];

	// Sort options
	const sortOptions = [
		{ value: 'createdAt', label: 'Registration Date' },
		{ value: 'username', label: 'Username' },
		{ value: 'displayName', label: 'Display Name' },
		{ value: 'email', label: 'Email' },
		{ value: 'role', label: 'Role' },
		{ value: 'status', label: 'Status' },
		{ value: 'lastActiveAt', label: 'Last Active' },
		{ value: 'updatedAt', label: 'Last Updated' }
	];

	// Load users data
	async function loadUsers() {
		isLoading = true;
		error = ''; // Clear error immediately without timeout

		debugLog('=== LOADING USERS ===');
		debugAuthentication(); // Re-check auth on each load

		try {
			const params = new URLSearchParams({
				page: currentPage.toString(),
				limit: usersPerPage.toString(),
				search: searchQuery,
				role: roleFilter,
				status: statusFilter,
				type: typeFilter,
				sortBy,
				sortOrder,
				...(dateFrom && { dateFrom }),
				...(dateTo && { dateTo })
			});

			const apiUrl = `/api/admin/users?${params}`;
			debugLog('API Request URL:', apiUrl);

			const response = await fetch(apiUrl);
			const result = await response.json();

			debugLog('API Response Status:', response.status);
			debugLog('API Response Data:', result);

			// Store API call info
			debugInfo.apiCalls = [
				{
					timestamp: new Date().toISOString(),
					url: apiUrl,
					status: response.status,
					success: response.ok && result.success,
					error: result.error || null,
					userCount: result.data?.users?.length || 0
				},
				...debugInfo.apiCalls.slice(0, 9) // Keep last 10 API calls
			];

			if (response.ok && result.success) {
				users = result.data.users;
				totalPages = result.data.pagination.totalPages;
				totalUsers = result.data.pagination.total;

				debugLog(`Successfully loaded ${users.length} users`);
				debugLog('Users data:', users);

				// Debug button visibility for each user
				users.forEach(user => {
					debugButtonVisibility(user);
				});
			} else {
				setErrorMessage(result.error || 'Failed to load users');
				debugLog('API Error:', error);
			}
		} catch (err) {
			console.error('Error loading users:', err);
			setErrorMessage('An error occurred while loading users');
			debugLog('Network Error:', err);
		} finally {
			isLoading = false;
			debugInfo = { ...debugInfo };
		}
	}

	// Handle search with debouncing
	let searchTimeout: NodeJS.Timeout;
	function handleSearch() {
		clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			currentPage = 1;
			loadUsers();
		}, 300);
	}

	// Handle filter changes
	function handleFilterChange() {
		currentPage = 1;
		loadUsers();
	}

	// Handle pagination
	function goToPage(page: number) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
			loadUsers();
		}
	}

	// Handle user selection
	function toggleUserSelection(userId: number) {
		if (selectedUsers.has(userId)) {
			selectedUsers.delete(userId);
		} else {
			selectedUsers.add(userId);
		}
		selectedUsers = selectedUsers; // Trigger reactivity
	}

	function selectAllUsers() {
		if (selectedUsers.size === users.length) {
			selectedUsers.clear();
		} else {
			selectedUsers = new Set(users.map(user => user.id));
		}
		selectedUsers = selectedUsers; // Trigger reactivity
	}

	// View user details
	async function viewUser(userId: number) {
		try {
			const response = await fetch(`/api/admin/users/${userId}`);
			const result = await response.json();

			if (response.ok && result.success) {
				selectedUser = result.data;
				showUserModal = true;
			} else {
				setErrorMessage(result.error || 'Failed to load user details');
			}
		} catch (err) {
			console.error('Error loading user details:', err);
			setErrorMessage('An error occurred while loading user details');
		}
	}

	// Create user
	async function createUser(userData: any) {
		try {
			const response = await fetch('/api/admin/users', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(userData)
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccessMessage(result.message || 'User created successfully');
				showUserModal = false;
				selectedUser = null;
				loadUsers(); // Refresh the list
			} else {
				setErrorMessage(result.error || 'Failed to create user');
			}
		} catch (err) {
			console.error('Error creating user:', err);
			setErrorMessage('An error occurred while creating user');
		}
	}

	// Update user
	async function updateUser(userId: number, updateData: any) {
		try {
			const response = await fetch(`/api/admin/users/${userId}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(updateData)
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccessMessage(result.message);
				showUserModal = false;
				selectedUser = null;
				loadUsers(); // Refresh the list
			} else {
				setErrorMessage(result.error || 'Failed to update user');
			}
		} catch (err) {
			console.error('Error updating user:', err);
			setErrorMessage('An error occurred while updating user');
		}
	}

	// Delete user
	async function deleteUser(userId: number) {
		if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
			return;
		}

		try {
			const response = await fetch(`/api/admin/users/${userId}`, {
				method: 'DELETE'
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccessMessage(result.message);
				showUserModal = false;
				selectedUser = null;
				loadUsers(); // Refresh the list
			} else {
				setErrorMessage(result.error || 'Failed to delete user');
			}
		} catch (err) {
			console.error('Error deleting user:', err);
			setErrorMessage('An error occurred while deleting user');
		}
	}

	// Handle bulk operations
	async function performBulkAction() {
		if (selectedUsers.size === 0) {
			setErrorMessage('Please select at least one user');
			return;
		}

		if (!bulkAction) {
			setErrorMessage('Please select an action');
			return;
		}

		const userIds = Array.from(selectedUsers);
		let confirmMessage = '';

		switch (bulkAction) {
			case 'delete':
				confirmMessage = `Are you sure you want to delete ${userIds.length} users? This action cannot be undone.`;
				break;
			case 'updateRole':
				confirmMessage = `Are you sure you want to change the role for ${userIds.length} users?`;
				break;
			default:
				confirmMessage = `Are you sure you want to perform this action on ${userIds.length} users?`;
		}

		if (!confirm(confirmMessage)) {
			return;
		}

		try {
			const requestData: any = {
				action: bulkAction,
				userIds
			};

			// Add additional data based on action
			if (bulkAction === 'updateRole') {
				const newRole = prompt('Enter new role (admin, moderator, user):');
				if (!newRole || !['admin', 'moderator', 'user'].includes(newRole)) {
					setErrorMessage('Invalid role specified');
					return;
				}
				requestData.data = { role: newRole };
			}

			const response = await fetch('/api/admin/users/bulk', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(requestData)
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccessMessage(result.message);
				selectedUsers.clear();
				selectedUsers = selectedUsers; // Trigger reactivity
				showBulkModal = false;
				bulkAction = '';
				loadUsers(); // Refresh the list
			} else {
				setErrorMessage(result.error || 'Failed to perform bulk action');
			}
		} catch (err) {
			console.error('Error performing bulk action:', err);
			setErrorMessage('An error occurred while performing bulk action');
		}
	}

	// Export users
	async function exportUsers(format: 'json' | 'csv' = 'json') {
		try {
			const params = new URLSearchParams({
				format,
				includePreferences: 'true'
			});

			const response = await fetch(`/api/admin/users/bulk/export?${params}`);

			if (format === 'csv') {
				const blob = await response.blob();
				const url = window.URL.createObjectURL(blob);
				const a = document.createElement('a');
				a.href = url;
				a.download = `fwfc-users-${new Date().toISOString().split('T')[0]}.csv`;
				document.body.appendChild(a);
				a.click();
				window.URL.revokeObjectURL(url);
				document.body.removeChild(a);
				setSuccessMessage('Users exported successfully');
			} else {
				const result = await response.json();
				if (response.ok && result.success) {
					const dataStr = JSON.stringify(result.data, null, 2);
					const blob = new Blob([dataStr], { type: 'application/json' });
					const url = window.URL.createObjectURL(blob);
					const a = document.createElement('a');
					a.href = url;
					a.download = `fwfc-users-${new Date().toISOString().split('T')[0]}.json`;
					document.body.appendChild(a);
					a.click();
					window.URL.revokeObjectURL(url);
					document.body.removeChild(a);
					setSuccessMessage('Users exported successfully');
				} else {
					setErrorMessage(result.error || 'Failed to export users');
				}
			}
		} catch (err) {
			console.error('Error exporting users:', err);
			setErrorMessage('An error occurred while exporting users');
		}
	}

	// Format date for display
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// Get role badge class
	function getRoleBadgeClass(role: string): string {
		switch (role) {
			case 'admin': return 'role-admin';
			case 'moderator': return 'role-moderator';
			default: return 'role-user';
		}
	}

	// Get status badge class
	function getStatusBadgeClass(status: string): string {
		switch (status) {
			case 'active': return 'status-active';
			case 'inactive': return 'status-inactive';
			case 'suspended': return 'status-suspended';
			default: return 'status-active';
		}
	}

	// Check if user is being created (no ID)
	function isCreatingUser(): boolean {
		return !selectedUser?.id;
	}

	// Check if a string is valid JSON
	function isValidJson(jsonString: string | null): boolean {
		if (!jsonString || jsonString.trim() === '') return true; // Empty is considered valid

		try {
			JSON.parse(jsonString);
			return true;
		} catch {
			return false;
		}
	}

	// Validate and format personality JSON
	function validatePersonalityJson(personalityString: string | null): string | null {
		if (!personalityString || personalityString.trim() === '') {
			return null;
		}

		try {
			// Try to parse the JSON to validate it
			const parsed = JSON.parse(personalityString);
			// Return the properly formatted JSON string
			return JSON.stringify(parsed);
		} catch (error) {
			// If it's not valid JSON, try to create a simple structure
			console.warn('Invalid personality JSON, attempting to create valid structure:', error);

			// If it looks like a simple list of traits, convert it
			if (personalityString.includes(',') && !personalityString.includes('{')) {
				const traits = personalityString.split(',').map(trait => trait.trim().replace(/['"]/g, ''));
				return JSON.stringify({
					traits: traits,
					writingStyle: 'casual',
					activityLevel: 'medium'
				});
			}

			// Otherwise, wrap it in a basic structure
			return JSON.stringify({
				traits: [personalityString.trim()],
				writingStyle: 'casual',
				activityLevel: 'medium'
			});
		}
	}

	// Handle save user (create or update)
	async function saveUser() {
		if (!selectedUser) return;

		// Validate and format personality data
		let validatedPersonality = null;
		if (selectedUser.isSimulated && selectedUser.simulatedPersonality) {
			validatedPersonality = validatePersonalityJson(selectedUser.simulatedPersonality);
			if (!validatedPersonality) {
				setErrorMessage('Invalid personality JSON format. Please check the syntax.');
				return;
			}
		}

		const userData = {
			username: selectedUser.username,
			displayName: selectedUser.displayName,
			email: selectedUser.email,
			role: selectedUser.role,
			status: selectedUser.status,
			isSimulated: selectedUser.isSimulated,
			bio: selectedUser.bio,
			avatarUrl: selectedUser.avatarUrl,
			location: selectedUser.location,
			website: selectedUser.website,
			birthDate: selectedUser.birthDate,
			interests: selectedUser.interests || [],
			simulatedPersonality: validatedPersonality
		};

		if (isCreatingUser()) {
			await createUser(userData);
		} else {
			await updateUser(selectedUser.id, userData);
		}
	}

	// Debug authentication and role
	function debugAuthentication() {
		debugLog('=== AUTHENTICATION DEBUG ===');
		debugLog('Current data object:', data);
		debugLog('Current user object:', data?.user);
		debugLog('User role:', data?.user?.role);
		debugLog('User ID:', data?.user?.id);
		debugLog('User username:', data?.user?.username);

		// Update debug info
		debugInfo.currentUser = data?.user || null;
		debugInfo.userRole = data?.user?.role || 'NONE';
		debugInfo.authStatus = data?.user ? 'AUTHENTICATED' : 'NOT_AUTHENTICATED';

		// Test role conditions
		const isAdmin = data?.user?.role === 'admin';
		const isModerator = data?.user?.role === 'moderator';
		const isUser = data?.user?.role === 'user';

		debugLog(`Role checks - Admin: ${isAdmin}, Moderator: ${isModerator}, User: ${isUser}`);
		debugInfo = { ...debugInfo };
	}

	// Debug button visibility for each user
	function debugButtonVisibility(user: any) {
		const currentUserRole = data?.user?.role;
		const targetUserRole = user?.role;
		const currentUserId = data?.user?.id;
		const targetUserId = user?.id;

		// Admin condition
		const adminCondition = currentUserRole === 'admin';

		// Moderator condition
		const moderatorCondition = currentUserRole === 'moderator' && targetUserRole !== 'admin';

		// Combined condition
		const showResetButton = adminCondition || moderatorCondition;

		const debugData = {
			targetUser: `${user?.username} (ID: ${targetUserId}, Role: ${targetUserRole})`,
			currentUser: `${data?.user?.username} (ID: ${currentUserId}, Role: ${currentUserRole})`,
			adminCondition,
			moderatorCondition,
			showResetButton,
			reasoning: showResetButton
				? (adminCondition ? 'Admin can reset any password' : 'Moderator can reset non-admin passwords')
				: (currentUserRole === 'moderator' ? 'Moderator cannot reset admin passwords' : 'Insufficient permissions')
		};

		debugLog(`Button visibility for ${user?.username}:`, debugData);

		// Store in debug info
		debugInfo.buttonVisibility[user?.id] = debugData;
		debugInfo = { ...debugInfo };

		return showResetButton;
	}

	// Debug modal state changes
	function debugModalState(action: string, user?: any) {
		debugLog(`=== MODAL STATE CHANGE: ${action} ===`);
		debugLog('showPasswordResetModal:', showPasswordResetModal);
		debugLog('selectedUser:', selectedUser);
		debugLog('action user:', user);

		debugInfo.componentStates = {
			...debugInfo.componentStates,
			showPasswordResetModal,
			selectedUser: selectedUser ? `${selectedUser.username} (${selectedUser.id})` : null,
			lastAction: action,
			timestamp: new Date().toISOString()
		};
		debugInfo = { ...debugInfo };
	}

	// Initialize
	onMount(() => {
		debugLog('=== COMPONENT MOUNTED ===');
		debugAuthentication();
		loadUsers();
	});

	// Clear messages after delays - using proper effect management
	let successMessageTimeout: NodeJS.Timeout | null = null;
	let errorMessageTimeout: NodeJS.Timeout | null = null;

	// Function to set success message with auto-clear
	function setSuccessMessage(message: string) {
		successMessage = message;
		if (successMessageTimeout) {
			clearTimeout(successMessageTimeout);
		}
		successMessageTimeout = setTimeout(() => {
			successMessage = '';
			successMessageTimeout = null;
		}, 5000);
	}

	// Function to set error message with auto-clear
	function setErrorMessage(message: string) {
		error = message;
		if (errorMessageTimeout) {
			clearTimeout(errorMessageTimeout);
		}
		errorMessageTimeout = setTimeout(() => {
			error = '';
			errorMessageTimeout = null;
		}, 8000);
	}

	// Reactive debugging - track important state changes
	$: {
		if (data?.user) {
			debugInfo.currentUser = data.user;
			debugInfo.userRole = data.user.role || 'NONE';
			debugInfo.authStatus = 'AUTHENTICATED';
		} else {
			debugInfo.currentUser = null;
			debugInfo.userRole = 'NONE';
			debugInfo.authStatus = 'NOT_AUTHENTICATED';
		}
		debugInfo = { ...debugInfo };
	}

	$: if (showPasswordResetModal !== undefined) {
		debugLog(`showPasswordResetModal changed to: ${showPasswordResetModal}`);
	}

	$: if (selectedUser) {
		debugLog(`selectedUser changed to: ${selectedUser.username} (${selectedUser.id})`);
	}
</script>

<svelte:head>
	<title>User Management - FWFC Admin</title>
	<meta name="description" content="Manage user accounts for the Finn Wolfhard Fan Club" />
</svelte:head>

<div class="users-management">
	<header class="page-header">
		<div class="header-content">
			<h1>User Management</h1>
			<p class="header-subtitle">
				Manage user accounts, roles, and permissions for the FWFC community
			</p>
			<!-- Debug Info Display -->
			<div class="debug-status">
				<strong>🔍 Debug Info:</strong>
				Role: <span class="role-indicator role-{debugInfo.userRole.toLowerCase()}">{debugInfo.userRole}</span> |
				Auth: <span class="auth-indicator auth-{debugInfo.authStatus.toLowerCase().replace('_', '-')}">{debugInfo.authStatus}</span> |
				Users: {users.length} |
				<button class="btn small {showDebugPanel ? 'primary' : 'secondary'}" onclick={() => showDebugPanel = !showDebugPanel}>
					{showDebugPanel ? 'Hide' : 'Show'} Debug Panel
				</button>
			</div>
		</div>
		<div class="header-actions">
			<button
				class="btn primary"
				onclick={() => {
					selectedUser = {
						username: '',
						displayName: '',
						email: '',
						role: 'user',
						status: 'active',
						isSimulated: false,
						bio: '',
						avatarUrl: '',
						location: '',
						website: '',
						birthDate: '',
						interests: [],
						simulatedPersonality: null
					};
					showUserModal = true;
				}}
				disabled={isLoading}
				aria-label="Create new user"
			>
				➕ Create User
			</button>
			<button
				class="btn secondary"
				onclick={() => exportUsers('csv')}
				disabled={isLoading}
				aria-label="Export users to CSV"
			>
				📊 Export CSV
			</button>
			<button
				class="btn secondary"
				onclick={() => exportUsers('json')}
				disabled={isLoading}
				aria-label="Export users to JSON"
			>
				📄 Export JSON
			</button>
		</div>
	</header>

	<!-- Debug Panel -->
	{#if showDebugPanel}
		<div class="debug-panel">
			<h3>🔧 Debug Panel</h3>

			<div class="debug-section">
				<h4>Authentication & Role Info</h4>
				<div class="debug-grid">
					<div><strong>Current User:</strong> {debugInfo.currentUser?.username || 'NONE'}</div>
					<div><strong>User ID:</strong> {debugInfo.currentUser?.id || 'NONE'}</div>
					<div><strong>Role:</strong> {debugInfo.userRole}</div>
					<div><strong>Auth Status:</strong> {debugInfo.authStatus}</div>
				</div>
			</div>

			<div class="debug-section">
				<h4>Component States</h4>
				<div class="debug-grid">
					<div><strong>showPasswordResetModal:</strong> {showPasswordResetModal}</div>
					<div><strong>selectedUser:</strong> {selectedUser?.username || 'NONE'}</div>
					<div><strong>isLoading:</strong> {isLoading}</div>
					<div><strong>users.length:</strong> {users.length}</div>
				</div>
			</div>

			<div class="debug-section">
				<h4>Button Visibility Matrix</h4>
				<div class="visibility-matrix">
					{#each users.slice(0, 5) as user}
						<div class="user-debug-row">
							<span class="user-info">{user.username} ({user.role})</span>
							<span class="visibility-status">
								{#if debugInfo.buttonVisibility[user.id]}
									{debugInfo.buttonVisibility[user.id].showResetButton ? '✅ VISIBLE' : '❌ HIDDEN'}
									- {debugInfo.buttonVisibility[user.id].reasoning}
								{:else}
									⏳ Calculating...
								{/if}
							</span>
						</div>
					{/each}
				</div>
			</div>

			<div class="debug-section">
				<h4>Recent Debug Logs (Last 10)</h4>
				<div class="debug-logs">
					{#each debugLogs.slice(0, 10) as log}
						<div class="log-entry">{log}</div>
					{/each}
				</div>
			</div>

			<div class="debug-section">
				<h4>API Calls</h4>
				<div class="api-calls">
					{#each debugInfo.apiCalls.slice(0, 3) as call}
						<div class="api-call">
							<strong>{call.timestamp}:</strong> {call.url}
							<span class="status-{call.success ? 'success' : 'error'}">
								({call.status}) {call.success ? '✅' : '❌'}
							</span>
						</div>
					{/each}
				</div>
			</div>

			<div class="debug-actions">
				<button class="btn small secondary" onclick={() => debugAuthentication()}>
					🔄 Refresh Auth Debug
				</button>
				<button class="btn small secondary" onclick={() => debugLogs = []}>
					🗑️ Clear Logs
				</button>
				<button class="btn small secondary" onclick={() => console.log('Full Debug Info:', debugInfo)}>
					📋 Log Full Debug Info
				</button>
			</div>
		</div>
	{/if}

	<!-- Success/Error Messages -->
	{#if successMessage}
		<div class="message success" role="alert" aria-live="polite">
			✅ {successMessage}
		</div>
	{/if}

	{#if error}
		<ErrorMessage
			title="Error"
			message={error}
			type="error"
			dismissible={true}
			onDismiss={() => {
				error = '';
				if (errorMessageTimeout) {
					clearTimeout(errorMessageTimeout);
					errorMessageTimeout = null;
				}
			}}
		/>
	{/if}

	<!-- Filters and Search -->
	<div class="filters-section">
		<div class="search-group">
			<label for="search" class="sr-only">Search users</label>
			<input
				type="text"
				id="search"
				bind:value={searchQuery}
				oninput={handleSearch}
				placeholder="Search by username, display name, or email..."
				class="search-input"
				disabled={isLoading}
			/>
		</div>

		<div class="filter-group">
			<label for="role-filter">Role:</label>
			<select
				id="role-filter"
				bind:value={roleFilter}
				onchange={handleFilterChange}
				disabled={isLoading}
			>
				{#each userRoles as role}
					<option value={role.value}>{role.label}</option>
				{/each}
			</select>
		</div>

		<div class="filter-group">
			<label for="status-filter">Status:</label>
			<select
				id="status-filter"
				bind:value={statusFilter}
				onchange={handleFilterChange}
				disabled={isLoading}
			>
				{#each userStatuses as status}
					<option value={status.value}>{status.label}</option>
				{/each}
			</select>
		</div>

		<div class="filter-group">
			<label for="type-filter">Type:</label>
			<select
				id="type-filter"
				bind:value={typeFilter}
				onchange={handleFilterChange}
				disabled={isLoading}
			>
				{#each userTypes as type}
					<option value={type.value}>{type.label}</option>
				{/each}
			</select>
		</div>

		<div class="filter-group">
			<label for="sort-by">Sort by:</label>
			<select
				id="sort-by"
				bind:value={sortBy}
				onchange={handleFilterChange}
				disabled={isLoading}
			>
				{#each sortOptions as option}
					<option value={option.value}>{option.label}</option>
				{/each}
			</select>
		</div>

		<div class="filter-group">
			<label for="sort-order">Order:</label>
			<select
				id="sort-order"
				bind:value={sortOrder}
				onchange={handleFilterChange}
				disabled={isLoading}
			>
				<option value="desc">Descending</option>
				<option value="asc">Ascending</option>
			</select>
		</div>

		<div class="filter-group date-group">
			<label for="date-from">From:</label>
			<input
				type="date"
				id="date-from"
				bind:value={dateFrom}
				onchange={handleFilterChange}
				disabled={isLoading}
			/>
		</div>

		<div class="filter-group date-group">
			<label for="date-to">To:</label>
			<input
				type="date"
				id="date-to"
				bind:value={dateTo}
				onchange={handleFilterChange}
				disabled={isLoading}
			/>
		</div>
	</div>

	<!-- Bulk Actions -->
	{#if selectedUsers.size > 0}
		<div class="bulk-actions">
			<div class="bulk-info">
				<span class="selected-count">{selectedUsers.size} users selected</span>
			</div>
			<div class="bulk-controls">
				<select bind:value={bulkAction} class="bulk-action-select">
					<option value="">Select action...</option>
					<option value="updateRole">Change Role</option>
					{#if data.user.role === 'admin'}
						<option value="delete">Delete Users</option>
					{/if}
				</select>
				<button
					class="btn danger"
					onclick={performBulkAction}
					disabled={!bulkAction || isLoading}
				>
					Apply Action
				</button>
				<button
					class="btn secondary"
					onclick={() => { selectedUsers.clear(); selectedUsers = selectedUsers; }}
					disabled={isLoading}
				>
					Clear Selection
				</button>
			</div>
		</div>
	{/if}

	<!-- Users Table -->
	<div class="table-container">
		{#if isLoading}
			<div class="loading-container">
				<LoadingSpinner size="large" message="Loading users..." />
			</div>
		{:else if users.length === 0}
			<div class="empty-state">
				<h3>No users found</h3>
				<p>No users match your current search criteria.</p>
			</div>
		{:else}
			<table class="users-table" aria-label="Users list">
				<thead>
					<tr>
						<th scope="col">
							<input
								type="checkbox"
								checked={selectedUsers.size === users.length && users.length > 0}
								onchange={selectAllUsers}
								aria-label="Select all users"
							/>
						</th>
						<th scope="col">Username</th>
						<th scope="col">Display Name</th>
						<th scope="col">Email</th>
						<th scope="col">Role</th>
						<th scope="col">Status</th>
						<th scope="col">Type</th>
						<th scope="col">Last Active</th>
						<th scope="col">Registered</th>
						<th scope="col">
							Actions
							<span class="debug-header-info" title="Your role: {data?.user?.role || 'NONE'} | Reset permissions: {data?.user?.role === 'admin' ? 'ALL USERS' : data?.user?.role === 'moderator' ? 'NON-ADMIN USERS' : 'NONE'}">
								🔍
							</span>
						</th>
					</tr>
				</thead>
				<tbody>
					{#each users as user (user.id)}
						<tr class="user-row" class:selected={selectedUsers.has(user.id)}>
							<td>
								<input
									type="checkbox"
									checked={selectedUsers.has(user.id)}
									onchange={() => toggleUserSelection(user.id)}
									aria-label="Select user {user.username}"
								/>
							</td>
							<td class="username-cell">
								<button
									class="username-link"
									onclick={() => viewUser(user.id)}
									aria-label="View details for {user.username}"
								>
									{user.username}
									{#if user.isSimulated}
										<span class="simulated-indicator" title="Simulated User">🤖</span>
									{/if}
								</button>
							</td>
							<td>{user.displayName}</td>
							<td class="email-cell">{user.email}</td>
							<td>
								<span class="role-badge {getRoleBadgeClass(user.role)}">
									{user.role}
								</span>
							</td>
							<td>
								<span class="status-badge status-{user.status}">
									{user.status}
								</span>
							</td>
							<td class="type-cell">
								{user.isSimulated ? 'Simulated' : 'Real'}
							</td>
							<td class="date-cell">
								{user.lastActiveAt ? formatDate(user.lastActiveAt) : 'Never'}
							</td>
							<td class="date-cell">{formatDate(user.createdAt)}</td>
							<td class="actions-cell">
								<div class="action-buttons">
									<button
										class="btn small primary"
										onclick={() => viewUser(user.id)}
										aria-label="View {user.username}"
									>
										View
									</button>
									{#if data.user.role === 'admin' || (data.user.role === 'moderator' && user.role !== 'admin')}
										<button
											class="btn small secondary"
											onclick={() => {
												debugLog(`=== RESET BUTTON CLICKED ===`);
												debugLog('Target user:', user);
												debugLog('Current user role:', data.user.role);
												debugLog('Target user role:', user.role);

												selectedUser = user;
												showPasswordResetModal = true;

												debugModalState('RESET_BUTTON_CLICKED', user);
											}}
											aria-label="Reset password for {user.username}"
										>
											Reset
										</button>
									{:else}
										<!-- Debug: Show why button is hidden -->
										<span class="debug-info" title="Reset button hidden - Current role: {data.user.role}, Target role: {user.role}">
											🚫
										</span>
									{/if}
								</div>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		{/if}
	</div>

	<!-- Pagination -->
	{#if totalPages > 1}
		<div class="pagination">
			<div class="pagination-info">
				Showing {((currentPage - 1) * usersPerPage) + 1} to {Math.min(currentPage * usersPerPage, totalUsers)} of {totalUsers} users
			</div>
			<div class="pagination-controls">
				<button
					class="btn small secondary"
					onclick={() => goToPage(1)}
					disabled={currentPage === 1 || isLoading}
					aria-label="Go to first page"
				>
					First
				</button>
				<button
					class="btn small secondary"
					onclick={() => goToPage(currentPage - 1)}
					disabled={currentPage === 1 || isLoading}
					aria-label="Go to previous page"
				>
					Previous
				</button>

				{#each Array.from({length: Math.min(5, totalPages)}, (_, i) => {
					const start = Math.max(1, currentPage - 2);
					const end = Math.min(totalPages, start + 4);
					return start + i;
				}).filter(page => page <= totalPages) as page}
					<button
						class="btn small {page === currentPage ? 'primary' : 'secondary'}"
						onclick={() => goToPage(page)}
						disabled={isLoading}
						aria-label="Go to page {page}"
						aria-current={page === currentPage ? 'page' : undefined}
					>
						{page}
					</button>
				{/each}

				<button
					class="btn small secondary"
					onclick={() => goToPage(currentPage + 1)}
					disabled={currentPage === totalPages || isLoading}
					aria-label="Go to next page"
				>
					Next
				</button>
				<button
					class="btn small secondary"
					onclick={() => goToPage(totalPages)}
					disabled={currentPage === totalPages || isLoading}
					aria-label="Go to last page"
				>
					Last
				</button>
			</div>
		</div>
	{/if}
</div>

<!-- User Details Modal -->
{#if showUserModal && selectedUser}
	<div
		class="modal-overlay"
		onclick={() => { showUserModal = false; selectedUser = null; }}
		onkeydown={(e) => e.key === 'Escape' && (showUserModal = false, selectedUser = null)}
		role="button"
		tabindex="0"
		aria-label="Close modal"
	>
		<div
			class="modal-content"
			onclick={(e) => e.stopPropagation()}
			onkeydown={(e) => e.stopPropagation()}
			role="dialog"
			aria-modal="true"
			aria-labelledby="user-modal-title"
			tabindex="-1"
		>
			<div class="modal-header">
				<h2 id="user-modal-title">
					{isCreatingUser() ? 'Create New User' : `User Details: ${selectedUser.username}`}
				</h2>
				<button
					class="close-button"
					onclick={() => { showUserModal = false; selectedUser = null; }}
					aria-label="Close user details"
				>
					×
				</button>
			</div>
			<div class="modal-body">
				<div class="user-details">
					<!-- Basic Information -->
					<div class="form-section">
						<h3>Basic Information</h3>
						<div class="form-row">
							<div class="detail-group">
								<label for="modal-username">Username: *</label>
								<input
									type="text"
									id="modal-username"
									bind:value={selectedUser.username}
									readonly={!isCreatingUser()}
									class:readonly-input={!isCreatingUser()}
									required
								/>
							</div>
							<div class="detail-group">
								<label for="modal-display-name">Display Name: *</label>
								<input
									type="text"
									id="modal-display-name"
									bind:value={selectedUser.displayName}
									required
								/>
							</div>
						</div>
						<div class="form-row">
							<div class="detail-group">
								<label for="modal-email">Email: *</label>
								<input
									type="email"
									id="modal-email"
									bind:value={selectedUser.email}
									required
								/>
							</div>
							{#if data.user.role === 'admin'}
								<div class="detail-group">
									<label for="modal-role">Role:</label>
									<select id="modal-role" bind:value={selectedUser.role}>
										<option value="user">Regular User</option>
										<option value="moderator">Moderator</option>
										<option value="admin">Administrator</option>
									</select>
								</div>
							{:else}
								<div class="detail-group">
									<span class="detail-label">Role:</span>
									<span class="role-badge {getRoleBadgeClass(selectedUser.role)}">
										{selectedUser.role}
									</span>
								</div>
							{/if}
						</div>
						<div class="form-row">
							<div class="detail-group">
								<label for="modal-status">Status:</label>
								<select id="modal-status" bind:value={selectedUser.status}>
									<option value="active">Active</option>
									<option value="inactive">Inactive</option>
									<option value="suspended">Suspended</option>
								</select>
							</div>
							<div class="detail-group">
								<label for="modal-is-simulated">
									<input
										type="checkbox"
										id="modal-is-simulated"
										bind:checked={selectedUser.isSimulated}
									/>
									Simulated User
								</label>
							</div>
						</div>
					</div>

					<!-- Profile Information -->
					<div class="form-section">
						<h3>Profile Information</h3>
						<div class="form-row">
							<div class="detail-group">
								<label for="modal-avatar-url">Avatar URL:</label>
								<input
									type="url"
									id="modal-avatar-url"
									bind:value={selectedUser.avatarUrl}
									placeholder="https://example.com/avatar.jpg"
								/>
							</div>
							<div class="detail-group">
								<label for="modal-location">Location:</label>
								<input
									type="text"
									id="modal-location"
									bind:value={selectedUser.location}
									placeholder="City, Country"
								/>
							</div>
						</div>
						<div class="form-row">
							<div class="detail-group">
								<label for="modal-website">Website:</label>
								<input
									type="url"
									id="modal-website"
									bind:value={selectedUser.website}
									placeholder="https://example.com"
								/>
							</div>
							<div class="detail-group">
								<label for="modal-birth-date">Birth Date:</label>
								<input
									type="date"
									id="modal-birth-date"
									bind:value={selectedUser.birthDate}
								/>
							</div>
						</div>
						<div class="detail-group">
							<label for="modal-bio">Bio:</label>
							<textarea
								id="modal-bio"
								bind:value={selectedUser.bio}
								rows="3"
								placeholder="Tell us about yourself..."
							></textarea>
						</div>
					</div>

					<!-- Simulated User Settings -->
					{#if selectedUser.isSimulated}
						<div class="form-section simulated-section">
							<h3>🤖 Simulated User Settings</h3>
							<div class="detail-group">
								<label for="modal-personality">Personality Traits (JSON):</label>
								<textarea
									id="modal-personality"
									bind:value={selectedUser.simulatedPersonality}
									rows="4"
									placeholder={`{"traits": ["friendly", "enthusiastic"], "interests": ["music", "movies"], "writingStyle": "casual", "activityLevel": "medium"}`}
									class:invalid={selectedUser.simulatedPersonality && !isValidJson(selectedUser.simulatedPersonality)}
								></textarea>
								{#if selectedUser.simulatedPersonality && !isValidJson(selectedUser.simulatedPersonality)}
									<small class="error-text">
										⚠️ Invalid JSON format. Will be auto-corrected on save.
									</small>
								{:else}
									<small class="help-text">
										Configure the personality traits for this simulated user. This affects how they interact and post content.
									</small>
								{/if}
							</div>
						</div>
					{/if}

					<!-- System Information -->
					{#if !isCreatingUser()}
						<div class="form-section">
							<h3>System Information</h3>
							<div class="form-row">
								<div class="detail-group">
									<span class="detail-label">User ID:</span>
									<span>{selectedUser.id}</span>
								</div>
								<div class="detail-group">
									<span class="detail-label">Registered:</span>
									<span>{formatDate(selectedUser.createdAt)}</span>
								</div>
							</div>
							<div class="form-row">
								<div class="detail-group">
									<span class="detail-label">Last Updated:</span>
									<span>{formatDate(selectedUser.updatedAt)}</span>
								</div>
								<div class="detail-group">
									<span class="detail-label">Last Active:</span>
									<span>{selectedUser.lastActiveAt ? formatDate(selectedUser.lastActiveAt) : 'Never'}</span>
								</div>
							</div>
						</div>
					{/if}
				</div>
			</div>
			<div class="modal-footer">
				<button
					class="btn primary"
					onclick={saveUser}
				>
					{isCreatingUser() ? 'Create User' : 'Save Changes'}
				</button>
				{#if !isCreatingUser() && data.user.role === 'admin' && selectedUser.id !== data.user.id}
					<button
						class="btn danger"
						onclick={() => deleteUser(selectedUser.id)}
					>
						Delete User
					</button>
				{/if}
				<button
					class="btn secondary"
					onclick={() => { showUserModal = false; selectedUser = null; }}
				>
					Cancel
				</button>
			</div>
		</div>
	</div>
{/if}

<!-- Password Reset Modal -->
<PasswordResetModal
	bind:isOpen={showPasswordResetModal}
	user={selectedUser}
	on:success={() => {
		debugLog('=== PASSWORD RESET SUCCESS ===');
		debugLog('Reset successful for user:', selectedUser?.username);
		setSuccessMessage(`Password reset successful for ${selectedUser?.username}`);
		showPasswordResetModal = false;
		selectedUser = null;
		debugModalState('RESET_SUCCESS');
	}}
	on:close={() => {
		debugLog('=== PASSWORD RESET MODAL CLOSED ===');
		showPasswordResetModal = false;
		selectedUser = null;
		debugModalState('MODAL_CLOSED');
	}}
/>

<style>
	.users-management {
		padding: 2rem;
		max-width: 1400px;
		margin: 0 auto;
		color: var(--theme-text-primary);
	}

	/* Page Header */
	.page-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 2px solid var(--theme-border);
	}

	.header-content h1 {
		margin: 0 0 0.5rem 0;
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--theme-text-primary);
	}

	.header-subtitle {
		margin: 0;
		font-size: 1.1rem;
		color: var(--theme-text-secondary);
		line-height: 1.5;
	}

	.header-actions {
		display: flex;
		gap: 1rem;
		flex-wrap: wrap;
	}

	/* Messages */
	.message {
		padding: 1rem;
		border-radius: 8px;
		margin-bottom: 1.5rem;
		font-weight: 600;
	}

	.message.success {
		background-color: var(--theme-accent-success, #d4edda);
		color: var(--theme-accent-success-text, #155724);
		border: 1px solid var(--theme-accent-success-border, #c3e6cb);
	}

	/* Filters Section */
	.filters-section {
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;
		align-items: end;
		margin-bottom: 2rem;
		padding: 1.5rem;
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 8px;
	}

	.search-group {
		flex: 2;
		min-width: 300px;
	}

	.search-input {
		width: 100%;
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		font-size: 1rem;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		transition: border-color 0.2s ease;
	}

	.search-input:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
		box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
	}

	.filter-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
		min-width: 120px;
	}

	.filter-group label {
		font-weight: 600;
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.filter-group select,
	.filter-group input {
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.date-group {
		min-width: 140px;
	}

	/* Bulk Actions */
	.bulk-actions {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem 1.5rem;
		background-color: var(--theme-accent-primary-light, #e8f5e8);
		border: 1px solid var(--theme-accent-primary);
		border-radius: 8px;
		margin-bottom: 1.5rem;
	}

	.bulk-info {
		font-weight: 600;
		color: var(--theme-accent-primary);
	}

	.bulk-controls {
		display: flex;
		gap: 1rem;
		align-items: center;
	}

	.bulk-action-select {
		padding: 0.5rem;
		border: 2px solid var(--theme-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
	}

	/* Table Container */
	.table-container {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 2px 4px var(--theme-shadow);
	}

	.loading-container {
		padding: 4rem;
		text-align: center;
	}

	.empty-state {
		padding: 4rem;
		text-align: center;
		color: var(--theme-text-secondary);
	}

	.empty-state h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
	}

	/* Users Table */
	.users-table {
		width: 100%;
		border-collapse: collapse;
		font-size: 0.9rem;
	}

	.users-table th,
	.users-table td {
		padding: 1rem;
		text-align: left;
		border-bottom: 1px solid var(--theme-border);
	}

	.users-table th {
		background-color: var(--theme-bg-secondary);
		font-weight: 600;
		color: var(--theme-text-primary);
		position: sticky;
		top: 0;
		z-index: 1;
	}

	.user-row {
		transition: background-color 0.2s ease;
	}

	.user-row:hover {
		background-color: var(--theme-bg-tertiary);
	}

	.user-row.selected {
		background-color: var(--theme-accent-primary-light, #e8f5e8);
	}

	.username-cell {
		font-weight: 600;
	}

	.username-link {
		background: none;
		border: none;
		color: var(--theme-accent-primary);
		cursor: pointer;
		font-weight: 600;
		text-decoration: none;
		padding: 0;
		font-size: inherit;
	}

	.username-link:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	.email-cell {
		max-width: 200px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.date-cell {
		font-size: 0.85rem;
		color: var(--theme-text-secondary);
		white-space: nowrap;
	}

	.actions-cell {
		width: 120px;
	}

	.action-buttons {
		display: flex;
		gap: 0.5rem;
	}

	/* Role Badges */
	.role-badge {
		display: inline-block;
		padding: 0.25rem 0.75rem;
		border-radius: 12px;
		font-size: 0.8rem;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.role-admin {
		background-color: var(--theme-accent-danger, #dc3545);
		color: white;
	}

	.role-moderator {
		background-color: var(--theme-accent-warning, #ffc107);
		color: #212529;
	}

	.role-user {
		background-color: var(--theme-accent-info, #17a2b8);
		color: white;
	}

	/* Status Badges */
	.status-badge {
		display: inline-block;
		padding: 0.25rem 0.75rem;
		border-radius: 12px;
		font-size: 0.8rem;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.status-active {
		background-color: var(--theme-accent-success, #28a745);
		color: white;
	}

	.status-inactive {
		background-color: var(--theme-accent-warning, #ffc107);
		color: #212529;
	}

	.status-suspended {
		background-color: var(--theme-accent-danger, #dc3545);
		color: white;
	}

	/* Simulated user indicator */
	.simulated-indicator {
		margin-left: 0.5rem;
		font-size: 0.8rem;
		opacity: 0.7;
	}

	.type-cell {
		font-size: 0.85rem;
		color: var(--theme-text-secondary);
	}

	/* Buttons */
	.btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		border: 2px solid var(--theme-border);
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}

	.btn.small {
		padding: 0.5rem 1rem;
		font-size: 0.8rem;
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border-color: var(--theme-accent-primary);
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
		transform: translateY(-1px);
	}

	.btn.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border-color: var(--theme-border);
	}

	.btn.secondary:hover:not(:disabled) {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-1px);
	}

	.btn.danger {
		background-color: var(--theme-accent-danger, #dc3545);
		color: white;
		border-color: var(--theme-accent-danger, #dc3545);
	}

	.btn.danger:hover:not(:disabled) {
		background-color: #c82333;
		border-color: #c82333;
		transform: translateY(-1px);
	}

	/* Pagination */
	.pagination {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1.5rem;
		background-color: var(--theme-card-bg);
		border-top: 1px solid var(--theme-border);
	}

	.pagination-info {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	.pagination-controls {
		display: flex;
		gap: 0.5rem;
		align-items: center;
	}

	/* Modal Styles */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 1rem;
	}

	.modal-content {
		background: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 12px;
		width: 100%;
		max-width: 600px;
		max-height: 90vh;
		overflow: hidden;
		color: var(--theme-text-primary);
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1.5rem;
		border-bottom: 1px solid var(--theme-border);
		background-color: var(--theme-bg-secondary);
	}

	.modal-header h2 {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
	}

	.close-button {
		background: none;
		border: none;
		font-size: 2rem;
		cursor: pointer;
		color: var(--theme-text-secondary);
		padding: 0;
		width: 2rem;
		height: 2rem;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: color 0.2s ease;
		border-radius: 4px;
	}

	.close-button:hover {
		color: var(--theme-text-primary);
		background-color: var(--theme-bg-tertiary);
	}

	.modal-body {
		padding: 1.5rem;
		max-height: 60vh;
		overflow-y: auto;
	}

	.modal-footer {
		display: flex;
		justify-content: flex-end;
		gap: 1rem;
		padding: 1.5rem;
		border-top: 1px solid var(--theme-border);
		background-color: var(--theme-bg-secondary);
	}

	/* User Details Form */
	.user-details {
		display: grid;
		gap: 2rem;
	}

	.form-section {
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		padding: 1.5rem;
		background-color: var(--theme-bg-secondary);
	}

	.form-section h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
		border-bottom: 1px solid var(--theme-border);
		padding-bottom: 0.5rem;
	}

	.form-row {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 1rem;
		margin-bottom: 1rem;
	}

	.form-row:last-child {
		margin-bottom: 0;
	}

	.simulated-section {
		border-color: var(--theme-accent-warning, #ffc107);
		background-color: rgba(255, 193, 7, 0.1);
	}

	.help-text {
		font-size: 0.8rem;
		color: var(--theme-text-secondary);
		margin-top: 0.25rem;
		display: block;
	}

	.error-text {
		font-size: 0.8rem;
		color: var(--theme-accent-danger, #dc3545);
		margin-top: 0.25rem;
		display: block;
		font-weight: 600;
	}

	textarea.invalid {
		border-color: var(--theme-accent-danger, #dc3545) !important;
		background-color: var(--theme-accent-danger-light, rgba(220, 53, 69, 0.1));
	}

	.detail-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.detail-group label,
	.detail-label {
		font-weight: 600;
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.detail-group input,
	.detail-group select,
	.detail-group textarea {
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 1rem;
		font-family: inherit;
	}

	.detail-group textarea {
		resize: vertical;
		min-height: 80px;
	}

	.detail-group input:focus,
	.detail-group select:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
		box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
	}

	.readonly-input {
		background-color: var(--theme-bg-tertiary) !important;
		cursor: not-allowed;
		opacity: 0.7;
	}

	/* Screen Reader Only */
	.sr-only {
		position: absolute;
		width: 1px;
		height: 1px;
		padding: 0;
		margin: -1px;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border: 0;
	}

	/* Responsive Design */
	@media (max-width: 1200px) {
		.users-management {
			padding: 1rem;
		}

		.page-header {
			flex-direction: column;
			align-items: stretch;
			gap: 1rem;
		}

		.header-actions {
			justify-content: flex-start;
		}
	}

	@media (max-width: 768px) {
		.filters-section {
			flex-direction: column;
			align-items: stretch;
		}

		.search-group {
			min-width: auto;
		}

		.filter-group {
			min-width: auto;
		}

		.bulk-actions {
			flex-direction: column;
			align-items: stretch;
			gap: 1rem;
		}

		.bulk-controls {
			justify-content: space-between;
		}

		.users-table {
			font-size: 0.8rem;
		}

		.users-table th,
		.users-table td {
			padding: 0.5rem;
		}

		.email-cell {
			max-width: 150px;
		}

		.pagination {
			flex-direction: column;
			gap: 1rem;
		}

		.pagination-controls {
			flex-wrap: wrap;
			justify-content: center;
		}

		.modal-content {
			margin: 0.5rem;
		}

		.modal-header,
		.modal-body,
		.modal-footer {
			padding: 1rem;
		}

		.modal-footer {
			flex-direction: column;
		}
	}

	/* High Contrast Mode */
	:global(.high-contrast) .users-table th {
		border-bottom: 3px solid var(--theme-border);
	}

	:global(.high-contrast) .user-row.selected {
		border: 3px solid var(--theme-accent-primary);
	}

	:global(.high-contrast) .role-badge {
		border: 2px solid currentColor;
	}

	:global(.high-contrast) .btn {
		border-width: 3px;
	}

	/* Large Text Mode */
	:global(.large-text) .users-table {
		font-size: 1.1rem;
	}

	:global(.large-text) .btn {
		padding: 1rem 2rem;
		font-size: 1.1rem;
	}

	:global(.large-text) .role-badge {
		padding: 0.5rem 1rem;
		font-size: 0.9rem;
	}

	/* Focus Management */
	.users-table th:focus,
	.users-table td:focus,
	.btn:focus,
	.search-input:focus,
	.filter-group select:focus,
	.filter-group input:focus,
	.username-link:focus,
	.close-button:focus {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
	}

	/* Loading and Animation States */
	.user-row {
		animation: fadeIn 0.3s ease-in-out;
	}

	@keyframes fadeIn {
		from { opacity: 0; transform: translateY(10px); }
		to { opacity: 1; transform: translateY(0); }
	}

	.btn:active:not(:disabled) {
		transform: translateY(0) scale(0.98);
	}

	/* Accessibility Enhancements */
	.btn:active:not(:disabled) {
		transform: translateY(0) scale(0.98);
	}

	/* Debug Panel Styles */
	.debug-status {
		margin-top: 0.5rem;
		padding: 0.5rem;
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		font-size: 0.9rem;
		font-family: monospace;
	}

	.role-indicator {
		padding: 0.2rem 0.5rem;
		border-radius: 3px;
		font-weight: bold;
		text-transform: uppercase;
	}

	.role-admin {
		background-color: #dc3545;
		color: white;
	}

	.role-moderator {
		background-color: #ffc107;
		color: #212529;
	}

	.role-user {
		background-color: #17a2b8;
		color: white;
	}

	.role-none {
		background-color: #6c757d;
		color: white;
	}

	.auth-indicator {
		padding: 0.2rem 0.5rem;
		border-radius: 3px;
		font-weight: bold;
	}

	.auth-authenticated {
		background-color: #28a745;
		color: white;
	}

	.auth-not-authenticated {
		background-color: #dc3545;
		color: white;
	}

	.debug-panel {
		background-color: var(--theme-card-bg);
		border: 2px solid var(--theme-accent-primary);
		border-radius: 8px;
		padding: 1.5rem;
		margin-bottom: 2rem;
		font-family: monospace;
		font-size: 0.9rem;
	}

	.debug-panel h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-accent-primary);
		font-size: 1.2rem;
	}

	.debug-panel h4 {
		margin: 1rem 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 1rem;
		border-bottom: 1px solid var(--theme-border);
		padding-bottom: 0.25rem;
	}

	.debug-section {
		margin-bottom: 1.5rem;
	}

	.debug-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 0.5rem;
		margin-bottom: 1rem;
	}

	.debug-grid div {
		padding: 0.5rem;
		background-color: var(--theme-bg-secondary);
		border-radius: 4px;
	}

	.visibility-matrix {
		max-height: 200px;
		overflow-y: auto;
		border: 1px solid var(--theme-border);
		border-radius: 4px;
	}

	.user-debug-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0.5rem;
		border-bottom: 1px solid var(--theme-border);
	}

	.user-debug-row:last-child {
		border-bottom: none;
	}

	.user-info {
		font-weight: bold;
		color: var(--theme-text-primary);
	}

	.visibility-status {
		font-size: 0.8rem;
		color: var(--theme-text-secondary);
	}

	.debug-logs {
		max-height: 150px;
		overflow-y: auto;
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		padding: 0.5rem;
	}

	.log-entry {
		padding: 0.25rem 0;
		border-bottom: 1px solid var(--theme-border);
		font-size: 0.8rem;
		color: var(--theme-text-secondary);
	}

	.log-entry:last-child {
		border-bottom: none;
	}

	.api-calls {
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		padding: 0.5rem;
	}

	.api-call {
		padding: 0.25rem 0;
		border-bottom: 1px solid var(--theme-border);
		font-size: 0.8rem;
	}

	.api-call:last-child {
		border-bottom: none;
	}

	.status-success {
		color: #28a745;
		font-weight: bold;
	}

	.status-error {
		color: #dc3545;
		font-weight: bold;
	}

	.debug-actions {
		display: flex;
		gap: 0.5rem;
		flex-wrap: wrap;
		margin-top: 1rem;
		padding-top: 1rem;
		border-top: 1px solid var(--theme-border);
	}

	.debug-info {
		font-size: 1.2rem;
		opacity: 0.5;
		cursor: help;
	}

	.debug-header-info {
		font-size: 0.8rem;
		opacity: 0.7;
		cursor: help;
		margin-left: 0.5rem;
	}

	/* Debug Panel Mobile Responsiveness */
	@media (max-width: 768px) {
		.debug-panel {
			padding: 1rem;
			font-size: 0.8rem;
		}

		.debug-grid {
			grid-template-columns: 1fr;
		}

		.user-debug-row {
			flex-direction: column;
			align-items: flex-start;
			gap: 0.25rem;
		}

		.debug-actions {
			flex-direction: column;
		}

		.form-row {
			grid-template-columns: 1fr;
		}

		.form-section {
			padding: 1rem;
		}

		.form-section h3 {
			font-size: 1rem;
		}
	}
</style>
