<script>
	import { onMount } from 'svelte';

	// State variables
	let adminServerRunning = false;

	// Check if the AdminJS server is running
	async function checkAdminServer() {
		try {
			const response = await fetch('http://localhost:3001/admin', { method: 'HEAD' });
			adminServerRunning = response.ok;
		} catch (error) {
			adminServerRunning = false;
		}
	}

	// Load on mount
	onMount(() => {
		checkAdminServer();
	});
</script>

<svelte:head>
	<title>Admin Dashboard - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Admin dashboard for the Finn Wolfhard Fan Club website" />
</svelte:head>

<div class="admin-dashboard">
	<h1>Admin Dashboard</h1>

	<div class="admin-sections">
		<div class="admin-section">
			<h2>Content Management</h2>
			<div class="admin-links">
				<a href="/admin/gallery" class="admin-link">
					<span class="icon">🖼️</span>
					<span class="text">Gallery Management</span>
				</a>
				<a href="/admin/media" class="admin-link">
					<span class="icon">🏞️</span>
					<span class="text">Media Library</span>
				</a>
				<a href="/admin/news" class="admin-link">
					<span class="icon">📰</span>
					<span class="text">News Management</span>
				</a>
				<a href="/admin/motd" class="admin-link">
					<span class="icon">💬</span>
					<span class="text">Message of the Day</span>
				</a>
			</div>
		</div>

		<div class="admin-section">
			<h2>User Management</h2>
			<div class="admin-links">
				<a href="/admin/users" class="admin-link">
					<span class="icon">👥</span>
					<span class="text">User Accounts</span>
				</a>
				<a href="/admin/comments" class="admin-link">
					<span class="icon">💬</span>
					<span class="text">Comments Moderation</span>
				</a>
				<a href="/admin/messages" class="admin-link">
					<span class="icon">✉️</span>
					<span class="text">Fan Messages</span>
				</a>
			</div>
		</div>

		<div class="admin-section">
			<h2>System</h2>
			<div class="admin-links">
				<a href="/admin/settings" class="admin-link">
					<span class="icon">⚙️</span>
					<span class="text">Site Settings</span>
				</a>
				{#if adminServerRunning}
					<a href="http://localhost:3001/admin" class="admin-link" target="_blank">
						<span class="icon">🔧</span>
						<span class="text">AdminJS Interface</span>
						<span class="status online">Online</span>
					</a>
				{:else}
					<div class="admin-link disabled">
						<span class="icon">🔧</span>
						<span class="text">AdminJS Interface</span>
						<span class="status offline">Offline</span>
					</div>
					<p class="help-text">Start the AdminJS server with <code>npm run admin</code></p>
				{/if}
			</div>
		</div>
	</div>
</div>

<style>
	.admin-dashboard {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
	}

	h1 {
		margin-bottom: 2rem;
		text-align: center;
	}

	.admin-sections {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
		gap: 2rem;
	}

	.admin-section {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 8px;
		padding: 1.5rem;
		box-shadow: 0 2px 4px var(--theme-shadow);
		color: var(--theme-text-primary);
	}

	.admin-section h2 {
		margin-top: 0;
		margin-bottom: 1rem;
		padding-bottom: 0.5rem;
		border-bottom: 1px solid var(--theme-border);
		color: var(--theme-text-primary);
	}

	.admin-links {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.admin-link {
		display: flex;
		align-items: center;
		padding: 1rem;
		background-color: var(--theme-bg-primary);
		border-radius: 4px;
		text-decoration: none;
		color: var(--theme-text-primary);
		transition: all 0.2s ease;
		border: 1px solid var(--theme-border);
	}

	.admin-link:hover {
		background-color: var(--theme-bg-secondary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-2px);
		box-shadow: 0 4px 8px var(--theme-shadow-hover);
	}

	.admin-link .icon {
		font-size: 1.5rem;
		margin-right: 1rem;
		width: 24px;
		text-align: center;
	}

	.admin-link .text {
		flex: 1;
	}

	.admin-link .status {
		font-size: 0.8rem;
		padding: 0.25rem 0.5rem;
		border-radius: 12px;
	}

	.status.online {
		background-color: var(--theme-accent-success);
		color: white;
	}

	.status.offline {
		background-color: var(--theme-accent-danger);
		color: white;
	}

	.disabled {
		opacity: 0.7;
		cursor: not-allowed;
	}

	.help-text {
		font-size: 0.9rem;
		color: var(--theme-text-secondary);
		margin-top: 0.5rem;
		margin-left: 1rem;
	}

	code {
		background-color: var(--theme-bg-secondary);
		color: var(--theme-text-primary);
		border: 1px solid var(--theme-border);
		padding: 0.2rem 0.4rem;
		border-radius: 3px;
		font-family: monospace;
	}
</style>
