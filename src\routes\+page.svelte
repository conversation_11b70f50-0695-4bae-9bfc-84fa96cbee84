<script>
	import { onMount } from 'svelte';

	// State variables
	let heroImage = null;
	let featuredNews = [];
	let featuredGallery = [];
	let loading = true;

	// Fetch data from APIs
	onMount(async () => {
		try {
			// Fetch active hero image
			const heroResponse = await fetch('/api/hero-images');
			if (heroResponse.ok) {
				const heroData = await heroResponse.json();
				if (heroData.success && heroData.data.length > 0) {
					heroImage = heroData.data[0]; // Get the first active hero image
				}
			}

			// Fetch featured news (latest published news)
			const newsResponse = await fetch('/api/news?limit=3&published=true');
			if (newsResponse.ok) {
				const newsData = await newsResponse.json();
				if (newsData.success) {
					featuredNews = newsData.data || [];
				}
			}

			// Fetch featured gallery (latest published gallery items)
			const galleryResponse = await fetch('/api/gallery?limit=4');
			if (galleryResponse.ok) {
				const galleryData = await galleryResponse.json();
				if (galleryData.success) {
					featuredGallery = galleryData.data || [];
				}
			}
		} catch (error) {
			console.error('Error fetching homepage data:', error);
		} finally {
			loading = false;
		}
	});

	// Fallback hero data
	$: heroTitle = heroImage?.title || 'Welcome to the Finn Wolfhard Fan Club';
	$: heroSubtitle = heroImage?.subtitle || 'The official fan community for fans of actor and musician Finn Wolfhard';
	$: heroBackground = heroImage?.imageUrl || '/images/hero-bg.jpg';
</script>

<svelte:head>
	<title>Home - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Official fan club for actor and musician Finn Wolfhard" />
</svelte:head>

<section class="hero" style="background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('{heroBackground}')">
	<div class="hero-content">
		<h1>{heroTitle}</h1>
		<p>{heroSubtitle}</p>
		<div class="hero-buttons">
			<a href="/join" class="btn primary">Join the Club</a>
			<a href="/about" class="btn secondary">Learn More</a>
		</div>
	</div>
</section>

<section class="featured-news">
	<h2>Latest News</h2>
	{#if loading}
		<div class="loading">Loading news...</div>
	{:else if featuredNews.length === 0}
		<div class="empty-state">
			<p>No news articles available at the moment.</p>
			<p>Check back soon for updates!</p>
		</div>
	{:else}
		<div class="news-grid">
			{#each featuredNews as news}
				<div class="news-card">
					<div class="news-image">
						<img
							src={news.imageUrl || '/images/placeholder-news.jpg'}
							alt={news.title}
							on:error={(e) => { e.target.src = '/images/placeholder.jpg'; }}
						/>
					</div>
					<div class="news-content">
						<h3>{news.title}</h3>
						<p class="date">{new Date(news.createdAt).toLocaleDateString()}</p>
						<p>{news.content.substring(0, 150)}...</p>
						<a href={`/news/${news.id}`} class="read-more">Read More</a>
					</div>
				</div>
			{/each}
		</div>
	{/if}
	<div class="view-all">
		<a href="/news" class="btn secondary">View All News</a>
	</div>
</section>

<section class="featured-gallery">
	<h2>Photo Gallery</h2>
	{#if loading}
		<div class="loading">Loading gallery...</div>
	{:else if featuredGallery.length === 0}
		<div class="empty-state">
			<p>No gallery images available at the moment.</p>
			<p>Check back soon for new photos!</p>
		</div>
	{:else}
		<div class="gallery-grid">
			{#each featuredGallery as image}
				<div class="gallery-item">
					<a href={`/gallery/${image.id}`}>
						<img src={image.thumbnailUrl || image.imageUrl} alt={image.title} />
						<div class="gallery-caption">{image.title}</div>
					</a>
				</div>
			{/each}
		</div>
	{/if}
	<div class="view-all">
		<a href="/gallery" class="btn secondary">View Full Gallery</a>
	</div>
</section>

<section class="community">
	<h2>Join Our Community</h2>
	<div class="community-content">
		<div class="community-text">
			<p>Connect with other Finn Wolfhard fans, discuss his latest projects, and stay updated on all things Finn!</p>
			<ul>
				<li>Exclusive content and updates</li>
				<li>Participate in discussions on our message board</li>
				<li>Share your fan art and stories</li>
				<li>Connect with fans from around the world</li>
			</ul>
			<a href="/join" class="btn primary">Join Now</a>
		</div>
		<div class="community-image">
			<img src="/images/community.jpg" alt="Fan Community" />
		</div>
	</div>
</section>

<style>
	.hero {
		background-size: cover;
		background-position: center;
		color: white;
		text-align: center;
		padding: 4rem 1rem;
		margin-bottom: 2rem;
		border-radius: 8px;
		min-height: 400px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.hero-content {
		max-width: 800px;
		margin: 0 auto;
	}

	.hero h1 {
		font-size: 2.5rem;
		margin-bottom: 1rem;
	}

	.hero p {
		font-size: 1.2rem;
		margin-bottom: 2rem;
	}

	.hero-buttons {
		display: flex;
		justify-content: center;
		gap: 1rem;
		flex-wrap: wrap;
	}

	.btn {
		display: inline-block;
		padding: 0.8rem 1.5rem;
		border-radius: 4px;
		text-decoration: none;
		font-weight: bold;
		transition: all 0.3s ease;
	}

	.btn.primary {
		background-color: var(--color-button-primary-bg);
		color: var(--color-button-primary-text);
		border: var(--border-width-thin) solid var(--color-button-primary-border);
	}

	.btn.primary:hover {
		background-color: var(--color-button-primary-hover-bg);
		border-color: var(--color-button-primary-hover-border);
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	.btn.secondary {
		background-color: var(--color-button-secondary-bg);
		color: var(--color-button-secondary-text);
		border: var(--border-width-medium) solid var(--color-button-secondary-border);
	}

	.btn.secondary:hover {
		background-color: var(--color-button-secondary-hover-bg);
		color: var(--color-button-secondary-hover-text);
		border-color: var(--color-button-secondary-hover-border);
	}

	section {
		margin-bottom: 3rem;
	}

	h2 {
		text-align: center;
		margin-bottom: 1.5rem;
		font-size: 2rem;
	}

	.news-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
		gap: 1.5rem;
		margin-bottom: 1.5rem;
	}

	.news-card {
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease;
	}

	.news-card:hover {
		transform: translateY(-5px);
	}

	.news-image img {
		width: 100%;
		height: 200px;
		object-fit: cover;
	}

	.news-content {
		padding: 1rem;
	}

	.news-content h3 {
		margin-top: 0;
		margin-bottom: 0.5rem;
	}

	.date {
		color: var(--color-text-secondary);
		font-size: var(--font-size-sm);
		margin-bottom: var(--space-sm);
	}

	.read-more {
		display: inline-block;
		margin-top: var(--space-sm);
		color: var(--color-interactive-primary);
		font-weight: var(--font-weight-semibold);
		text-decoration: none;
		transition: var(--transition-theme);
	}

	.read-more:hover {
		text-decoration: underline;
	}

	.gallery-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
		gap: 1rem;
		margin-bottom: 1.5rem;
	}

	.gallery-item {
		border-radius: 8px;
		overflow: hidden;
		position: relative;
		aspect-ratio: 1;
	}

	.gallery-item img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: transform 0.3s ease;
	}

	.gallery-item:hover img {
		transform: scale(1.05);
	}

	.gallery-caption {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: var(--color-bg-overlay);
		color: var(--color-text-primary);
		padding: var(--space-sm);
		font-size: var(--font-size-sm);
		opacity: 0;
		transition: var(--transition-theme);
	}

	.gallery-item:hover .gallery-caption {
		opacity: 1;
	}

	.view-all {
		text-align: center;
	}

	.loading, .empty-state {
		text-align: center;
		padding: var(--space-xl);
		background-color: var(--color-surface-secondary);
		border: var(--border-width-thin) solid var(--color-border-primary);
		border-radius: var(--border-radius-lg);
		margin-bottom: var(--space-lg);
		color: var(--color-text-primary);
	}

	.empty-state p {
		margin: var(--space-sm) 0;
		color: var(--color-text-secondary);
	}

	.community-content {
		display: flex;
		flex-direction: column;
		gap: 2rem;
		align-items: center;
	}

	.community-text {
		flex: 1;
	}

	.community-text ul {
		margin-bottom: 1.5rem;
	}

	.community-text li {
		margin-bottom: 0.5rem;
	}

	.community-image {
		flex: 1;
		border-radius: 8px;
		overflow: hidden;
	}

	.community-image img {
		width: 100%;
		height: auto;
		display: block;
	}

	@media (min-width: 768px) {
		.community-content {
			flex-direction: row;
		}
	}
</style>
