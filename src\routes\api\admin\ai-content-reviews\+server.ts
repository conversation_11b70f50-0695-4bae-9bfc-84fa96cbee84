import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { aiContentReviews, users } from '$lib/server/db/schema';
import { eq, desc, and, or, like } from 'drizzle-orm';
import type { RequestHand<PERSON> } from './$types';
import logger from '$lib/server/services/logger';

// GET /api/admin/ai-content-reviews - List AI content reviews with filtering
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    // Parse query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const status = url.searchParams.get('status');
    const contentType = url.searchParams.get('contentType');
    const search = url.searchParams.get('search');
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = url.searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];
    
    if (status && status !== 'all') {
      conditions.push(eq(aiContentReviews.reviewStatus, status as any));
    }
    
    if (contentType && contentType !== 'all') {
      conditions.push(eq(aiContentReviews.contentType, contentType as any));
    }
    
    if (search) {
      conditions.push(
        or(
          like(aiContentReviews.originalPrompt, `%${search}%`),
          like(aiContentReviews.reviewNotes, `%${search}%`)
        )
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get reviews with user information
    const reviews = await db
      .select({
        id: aiContentReviews.id,
        contentType: aiContentReviews.contentType,
        originalPrompt: aiContentReviews.originalPrompt,
        generatedContent: aiContentReviews.generatedContent,
        authenticityScore: aiContentReviews.authenticityScore,
        aiConfig: aiContentReviews.aiConfig,
        reviewStatus: aiContentReviews.reviewStatus,
        reviewNotes: aiContentReviews.reviewNotes,
        reviewedAt: aiContentReviews.reviewedAt,
        createdAt: aiContentReviews.createdAt,
        updatedAt: aiContentReviews.updatedAt,
        targetUser: {
          id: users.id,
          displayName: users.displayName,
          username: users.username,
          avatarUrl: users.avatarUrl
        },
        createdBy: {
          id: users.id,
          displayName: users.displayName,
          username: users.username
        }
      })
      .from(aiContentReviews)
      .leftJoin(users, eq(aiContentReviews.targetUserId, users.id))
      .where(whereClause)
      .orderBy(sortOrder === 'desc' ? desc(aiContentReviews[sortBy as keyof typeof aiContentReviews]) : aiContentReviews[sortBy as keyof typeof aiContentReviews])
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await db
      .select({ count: aiContentReviews.id })
      .from(aiContentReviews)
      .where(whereClause);
    
    const total = totalResult.length;

    return json({
      success: true,
      data: {
        reviews: reviews.map(review => ({
          ...review,
          generatedContent: JSON.parse(review.generatedContent),
          aiConfig: JSON.parse(review.aiConfig)
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching AI content reviews:', error);
    return json({
      success: false,
      error: 'Failed to fetch AI content reviews'
    }, { status: 500 });
  }
};

// POST /api/admin/ai-content-reviews - Create new AI content review
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      contentType,
      originalPrompt,
      generatedContent,
      authenticityScore,
      aiConfig,
      targetUserId
    } = body;

    // Validate required fields
    if (!contentType || !originalPrompt || !generatedContent || !targetUserId) {
      return json({
        success: false,
        error: 'Content type, prompt, generated content, and target user ID are required'
      }, { status: 400 });
    }

    // Create new review
    const result = await db.insert(aiContentReviews).values({
      contentType: contentType as 'news' | 'gallery' | 'comment' | 'message' | 'reply',
      originalPrompt,
      generatedContent: JSON.stringify(generatedContent),
      authenticityScore: authenticityScore || 0,
      aiConfig: JSON.stringify(aiConfig || {}),
      targetUserId,
      createdById: locals.user.id,
      reviewStatus: 'pending'
    }).returning();

    logger.info('AI content review created', {
      reviewId: result[0].id,
      contentType,
      createdBy: locals.user.username,
      authenticityScore
    });

    return json({
      success: true,
      data: result[0]
    }, { status: 201 });
  } catch (error) {
    logger.error('Error creating AI content review:', error);
    return json({
      success: false,
      error: 'Failed to create AI content review'
    }, { status: 500 });
  }
};
