import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq, inArray } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// POST /api/admin/users/bulk - Perform bulk operations on users
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action, userIds, data } = body;

    // Validate input
    if (!action || !Array.isArray(userIds) || userIds.length === 0) {
      return json({
        success: false,
        error: 'Action and user IDs are required'
      }, { status: 400 });
    }

    // Convert userIds to numbers and validate
    const validUserIds = userIds.map(id => parseInt(id)).filter(id => !isNaN(id));
    if (validUserIds.length === 0) {
      return json({
        success: false,
        error: 'No valid user IDs provided'
      }, { status: 400 });
    }

    // Prevent admin from performing bulk operations on themselves
    if (validUserIds.includes(locals.user.id)) {
      return json({
        success: false,
        error: 'Cannot perform bulk operations on your own account'
      }, { status: 403 });
    }

    let results = [];
    let errors = [];

    switch (action) {
      case 'updateRole':
        if (!data?.role || !['admin', 'moderator', 'user'].includes(data.role)) {
          return json({
            success: false,
            error: 'Valid role is required for role update'
          }, { status: 400 });
        }

        try {
          // Get current users to check permissions
          const currentUsers = await db.select({
            id: users.id,
            username: users.username,
            role: users.role
          }).from(users).where(inArray(users.id, validUserIds));

          // Check if trying to modify admin accounts
          const adminUsers = currentUsers.filter(user => user.role === 'admin');
          if (adminUsers.length > 0) {
            return json({
              success: false,
              error: 'Cannot modify admin accounts in bulk operations'
            }, { status: 403 });
          }

          // Update roles
          const updateResult = await db.update(users)
            .set({ 
              role: data.role,
              updatedAt: new Date().toISOString()
            })
            .where(inArray(users.id, validUserIds))
            .returning({
              id: users.id,
              username: users.username,
              role: users.role
            });

          results = updateResult;

          // Log admin action
          logger.info('Bulk role update performed by admin', {
            adminUser: locals.user.username,
            action: 'updateRole',
            newRole: data.role,
            affectedUsers: updateResult.map(u => u.username),
            userCount: updateResult.length
          });

        } catch (error) {
          errors.push(`Failed to update roles: ${error.message}`);
        }
        break;

      case 'delete':
        try {
          // Get users to be deleted for logging
          const usersToDelete = await db.select({
            id: users.id,
            username: users.username,
            role: users.role
          }).from(users).where(inArray(users.id, validUserIds));

          // Check if trying to delete admin accounts
          const adminUsers = usersToDelete.filter(user => user.role === 'admin');
          if (adminUsers.length > 0) {
            return json({
              success: false,
              error: 'Cannot delete admin accounts in bulk operations'
            }, { status: 403 });
          }

          // Delete users
          await db.delete(users).where(inArray(users.id, validUserIds));

          results = usersToDelete;

          // Log admin action
          logger.info('Bulk user deletion performed by admin', {
            adminUser: locals.user.username,
            action: 'delete',
            deletedUsers: usersToDelete.map(u => u.username),
            userCount: usersToDelete.length
          });

        } catch (error) {
          errors.push(`Failed to delete users: ${error.message}`);
        }
        break;

      case 'export':
        try {
          // Get user data for export (excluding sensitive information)
          const exportUsers = await db.select({
            id: users.id,
            username: users.username,
            displayName: users.displayName,
            email: users.email,
            role: users.role,
            createdAt: users.createdAt,
            updatedAt: users.updatedAt
          }).from(users).where(inArray(users.id, validUserIds));

          results = exportUsers;

          // Log admin action
          logger.info('Bulk user export performed by admin', {
            adminUser: locals.user.username,
            action: 'export',
            exportedUsers: exportUsers.map(u => u.username),
            userCount: exportUsers.length
          });

        } catch (error) {
          errors.push(`Failed to export users: ${error.message}`);
        }
        break;

      default:
        return json({
          success: false,
          error: 'Invalid action specified'
        }, { status: 400 });
    }

    return json({
      success: errors.length === 0,
      data: {
        action,
        results,
        processed: results.length,
        errors: errors.length > 0 ? errors : undefined
      },
      message: errors.length === 0 
        ? `Bulk ${action} completed successfully for ${results.length} users`
        : `Bulk ${action} completed with ${errors.length} errors`
    });

  } catch (error) {
    logger.error('Error performing bulk operation:', error);
    return json({
      success: false,
      error: 'Failed to perform bulk operation'
    }, { status: 500 });
  }
};

// GET /api/admin/users/bulk/export - Export all users data
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || (locals.user.role !== 'admin' && locals.user.role !== 'moderator')) {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const format = url.searchParams.get('format') || 'json';
    const includePreferences = url.searchParams.get('includePreferences') === 'true';

    // Get all users (excluding sensitive information)
    const selectFields = {
      id: users.id,
      username: users.username,
      displayName: users.displayName,
      email: users.email,
      role: users.role,
      createdAt: users.createdAt,
      updatedAt: users.updatedAt,
      ...(includePreferences && { preferences: users.preferences })
    };

    const allUsers = await db.select(selectFields).from(users);

    // Log admin action
    logger.info('Full user export performed by admin', {
      adminUser: locals.user.username,
      format,
      includePreferences,
      userCount: allUsers.length
    });

    if (format === 'csv') {
      // Convert to CSV format
      const headers = Object.keys(selectFields);
      const csvRows = [headers.join(',')];
      
      allUsers.forEach(user => {
        const row = headers.map(header => {
          const value = user[header];
          if (typeof value === 'object') {
            return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
          }
          return `"${String(value).replace(/"/g, '""')}"`;
        });
        csvRows.push(row.join(','));
      });

      return new Response(csvRows.join('\n'), {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="fwfc-users-${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    }

    return json({
      success: true,
      data: {
        users: allUsers,
        exportDate: new Date().toISOString(),
        totalUsers: allUsers.length,
        format,
        includePreferences
      }
    });

  } catch (error) {
    logger.error('Error exporting users:', error);
    return json({
      success: false,
      error: 'Failed to export users'
    }, { status: 500 });
  }
};
