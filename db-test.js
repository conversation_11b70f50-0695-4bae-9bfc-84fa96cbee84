import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';

// Database file path
const dbPath = 'local.db';

// Create database directory if it doesn't exist
const dbDir = path.dirname(dbPath);
if (dbDir !== '.' && !fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Connect to the database
console.log('Connecting to database...');
const db = new Database(dbPath);

// Create users table
console.log('Creating users table...');
db.exec(`
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'user',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    preferences TEXT DEFAULT '{"highContrast": false, "largeText": false, "simplifiedInterface": false}'
  )
`);

// Create news table
console.log('Creating news table...');
db.exec(`
  CREATE TABLE IF NOT EXISTS news (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    image_url TEXT,
    author_id INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    published INTEGER NOT NULL DEFAULT 0,
    FOREIGN KEY (author_id) REFERENCES users(id)
  )
`);

// Create gallery table
console.log('Creating gallery table...');
db.exec(`
  CREATE TABLE IF NOT EXISTS gallery (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    thumbnail_url TEXT NOT NULL,
    author_id INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    published INTEGER NOT NULL DEFAULT 0,
    FOREIGN KEY (author_id) REFERENCES users(id)
  )
`);

// Create messages table
console.log('Creating messages table...');
db.exec(`
  CREATE TABLE IF NOT EXISTS messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    content TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    approved INTEGER NOT NULL DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
  )
`);

// Create replies table
console.log('Creating replies table...');
db.exec(`
  CREATE TABLE IF NOT EXISTS replies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    message_id INTEGER NOT NULL,
    user_id INTEGER,
    content TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    approved INTEGER NOT NULL DEFAULT 0,
    FOREIGN KEY (message_id) REFERENCES messages(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
  )
`);

// Create comments table
console.log('Creating comments table...');
db.exec(`
  CREATE TABLE IF NOT EXISTS comments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    content TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    approved INTEGER NOT NULL DEFAULT 0,
    item_type TEXT NOT NULL,
    item_id INTEGER NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
  )
`);

// Create site_settings table
console.log('Creating site_settings table...');
db.exec(`
  CREATE TABLE IF NOT EXISTS site_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category TEXT NOT NULL,
    settings TEXT NOT NULL
  )
`);

// Create message_of_the_day table
console.log('Creating message_of_the_day table...');
db.exec(`
  CREATE TABLE IF NOT EXISTS message_of_the_day (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content TEXT NOT NULL,
    active INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
  )
`);

// Insert default admin user
console.log('Inserting default admin user...');
db.exec(`
  INSERT OR IGNORE INTO users (username, display_name, email, password_hash, role)
  VALUES ('admin', 'Administrator', '<EMAIL>', '$2a$10$JdJF.3Q9HeAQJmNgdqA3ZO7YRoJI5xYdA.ZuXGpXZbR.Uy/vz/Kru', 'admin')
`);

// Insert default site settings
console.log('Inserting default site settings...');
db.exec(`
  INSERT OR IGNORE INTO site_settings (category, settings)
  VALUES
    ('general', '{"siteName": "Finn Wolfhard Fan Club", "description": "Official fan club for Finn Wolfhard", "contactEmail": "<EMAIL>"}'),
    ('appearance', '{"primaryColor": "#4a90e2", "secondaryColor": "#27ae60", "textColor": "#333333", "backgroundColor": "#ffffff", "darkMode": false}'),
    ('accessibility', '{"defaultHighContrast": false, "defaultLargeText": false, "defaultSimplifiedInterface": false}'),
    ('social', '{"twitter": "https://twitter.com/FinnWolfhard", "instagram": "https://www.instagram.com/finnwolfhardofficial/", "facebook": "https://www.facebook.com/FinnWolfhardOfficial"}')
`);

// Insert some initial message of the day entries
console.log('Inserting message of the day entries...');
db.exec(`
  INSERT OR IGNORE INTO message_of_the_day (content, active)
  VALUES
    ('Finn Wolfhard was born on December 23, 2002, in Vancouver, Canada.', 1),
    ('Finn Wolfhard plays Mike Wheeler in the Netflix series Stranger Things.', 1),
    ('Finn is also the lead vocalist and guitarist for the rock band The Aubreys.', 1),
    ('Finn made his directorial debut with the short film "Night Shifts" in 2020.', 1),
    ('Finn starred as Richie Tozier in the horror films "It" (2017) and "It Chapter Two" (2019).', 1)
`);

// Verify tables were created
console.log('Verifying tables...');
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
console.log('Tables in database:', tables.map(t => t.name).join(', '));

// Close the database connection
console.log('Closing database connection...');
db.close();

console.log('Database setup completed successfully!');
