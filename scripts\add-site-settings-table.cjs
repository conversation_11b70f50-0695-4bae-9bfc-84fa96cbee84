const Database = require('better-sqlite3');
const path = require('path');

// Connect to the database
const dbPath = path.join(__dirname, '..', 'local.db');
const db = new Database(dbPath);

console.log('Adding site_settings table...');

try {
  // Drop existing table if it exists and recreate
  db.exec(`DROP TABLE IF EXISTS site_settings`);

  // Create site_settings table
  db.exec(`
    CREATE TABLE site_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      setting_key TEXT NOT NULL UNIQUE,
      setting_value TEXT,
      setting_type TEXT NOT NULL DEFAULT 'text',
      category TEXT NOT NULL DEFAULT 'general',
      display_name TEXT NOT NULL,
      description TEXT,
      is_public INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  console.log('Site settings table created successfully!');
  
  // Insert default site settings
  const existingSettings = db.prepare('SELECT COUNT(*) as count FROM site_settings').get();
  
  if (existingSettings.count === 0) {
    console.log('Creating default site settings...');
    
    const stmt = db.prepare(`
      INSERT INTO site_settings (setting_key, setting_value, setting_type, category, display_name, description, is_public)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    // Site Branding Settings
    stmt.run('site_name', 'Finn Wolfhard Fan Club', 'text', 'branding', 'Site Name', 'The main title of the website', 1);
    stmt.run('site_tagline', 'The official fan community for fans of actor and musician Finn Wolfhard', 'text', 'branding', 'Site Tagline', 'Subtitle or description of the site', 1);
    stmt.run('footer_text', '© 2025 Finn Wolfhard Fan Club | All rights reserved', 'text', 'branding', 'Footer Text', 'Copyright text displayed in the footer', 1);
    
    // Logo and Image Settings
    stmt.run('site_logo', '/images/logo.png', 'image', 'images', 'Site Logo', 'Main logo displayed in the header', 1);
    stmt.run('finn_profile_image', '/images/finn-profile.jpg', 'image', 'images', 'Finn Profile Image', 'Profile image used on the About page', 1);
    stmt.run('default_placeholder', '/images/placeholder.jpg', 'image', 'images', 'Default Placeholder', 'Fallback image for missing content', 1);
    stmt.run('error_image', '/images/placeholder.jpg', 'image', 'images', 'Error Image', 'Image shown when content fails to load', 1);
    
    // Contact Information
    stmt.run('contact_email', '<EMAIL>', 'email', 'contact', 'Contact Email', 'Main contact email address', 1);
    stmt.run('admin_email', '<EMAIL>', 'email', 'contact', 'Admin Email', 'Administrative contact email', 0);
    stmt.run('social_twitter', '', 'url', 'contact', 'Twitter URL', 'Official Twitter/X account URL', 1);
    stmt.run('social_instagram', '', 'url', 'contact', 'Instagram URL', 'Official Instagram account URL', 1);
    stmt.run('social_youtube', '', 'url', 'contact', 'YouTube URL', 'Official YouTube channel URL', 1);
    
    // Site Configuration
    stmt.run('maintenance_mode', '0', 'boolean', 'config', 'Maintenance Mode', 'Enable to show maintenance page to visitors', 0);
    stmt.run('allow_registrations', '1', 'boolean', 'config', 'Allow Registrations', 'Allow new user registrations', 0);
    stmt.run('max_upload_size', '********', 'number', 'config', 'Max Upload Size', 'Maximum file upload size in bytes (10MB default)', 0);
    stmt.run('items_per_page', '12', 'number', 'config', 'Items Per Page', 'Default number of items to show per page', 0);
    
    // SEO Settings
    stmt.run('meta_description', 'Official fan club for actor and musician Finn Wolfhard. Latest news, photos, and community discussions.', 'textarea', 'seo', 'Meta Description', 'Default meta description for SEO', 1);
    stmt.run('meta_keywords', 'Finn Wolfhard, Stranger Things, It, The Aubreys, actor, musician, fan club', 'text', 'seo', 'Meta Keywords', 'Default meta keywords for SEO', 1);
    stmt.run('google_analytics', '', 'text', 'seo', 'Google Analytics ID', 'Google Analytics tracking ID', 0);
    
    console.log('Default site settings created successfully!');
  } else {
    console.log('Site settings already exist.');
  }
  
} catch (error) {
  console.error('Error creating site settings table:', error);
} finally {
  db.close();
}
