-- Add AI content review tables
-- This migration adds the new tables for AI content generation and review

-- AI content reviews table
CREATE TABLE IF NOT EXISTS ai_content_reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content_type TEXT NOT NULL CHECK (content_type IN ('news', 'gallery', 'comment', 'message', 'reply')),
    original_prompt TEXT NOT NULL,
    generated_content TEXT NOT NULL, -- <PERSON><PERSON><PERSON> with title, content, description
    authenticity_score INTEGER NOT NULL,
    ai_config TEXT NOT NULL, -- JSO<PERSON> with tone, length, focusAreas, etc.
    target_user_id INTEGER NOT NULL,
    review_status TEXT NOT NULL DEFAULT 'pending' CHECK (review_status IN ('pending', 'approved', 'rejected', 'needs_revision')),
    reviewed_by_id INTEGER,
    review_notes TEXT,
    reviewed_at TEXT,
    created_by_id INTEGER NOT NULL,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (target_user_id) REFERENCES users(id) ON UPDATE NO ACTION ON DELETE NO ACTION,
    FOREIGN KEY (reviewed_by_id) REFERENCES users(id) ON UPDATE NO ACTION ON DELETE NO ACTION,
    FOREIGN KEY (created_by_id) REFERENCES users(id) ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- AI generation metrics table
CREATE TABLE IF NOT EXISTS ai_generation_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content_type TEXT NOT NULL CHECK (content_type IN ('news', 'gallery', 'comment', 'message', 'reply')),
    prompt_length INTEGER NOT NULL,
    generated_length INTEGER NOT NULL,
    authenticity_score INTEGER NOT NULL,
    tone TEXT NOT NULL,
    length TEXT NOT NULL,
    focus_areas TEXT DEFAULT '[]', -- JSON array
    generation_time_ms INTEGER NOT NULL,
    success INTEGER NOT NULL CHECK (success IN (0, 1)),
    error_message TEXT,
    user_id INTEGER NOT NULL,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ai_content_reviews_status ON ai_content_reviews(review_status);
CREATE INDEX IF NOT EXISTS idx_ai_content_reviews_content_type ON ai_content_reviews(content_type);
CREATE INDEX IF NOT EXISTS idx_ai_content_reviews_target_user ON ai_content_reviews(target_user_id);
CREATE INDEX IF NOT EXISTS idx_ai_content_reviews_created_at ON ai_content_reviews(created_at);

CREATE INDEX IF NOT EXISTS idx_ai_generation_metrics_content_type ON ai_generation_metrics(content_type);
CREATE INDEX IF NOT EXISTS idx_ai_generation_metrics_user ON ai_generation_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_generation_metrics_created_at ON ai_generation_metrics(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_generation_metrics_success ON ai_generation_metrics(success);
