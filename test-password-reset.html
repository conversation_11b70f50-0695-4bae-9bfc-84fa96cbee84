<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Password Reset API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 FWFC Password Reset API Test</h1>
        <p>This page helps test the password reset functionality for the FWFC admin system.</p>
        
        <div class="test-section">
            <h3>1. Test User List API</h3>
            <p>First, let's check if we can access the users list:</p>
            <button onclick="testUsersList()">Test Users List</button>
            <div id="users-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Password Reset API</h3>
            <p>Test password reset for a specific user:</p>
            <input type="number" id="userId" placeholder="User ID" value="1">
            <select id="resetType">
                <option value="link">Reset Link</option>
                <option value="temp">Temporary Password</option>
            </select>
            <button onclick="testPasswordReset()">Test Password Reset</button>
            <div id="reset-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Current Session Info</h3>
            <button onclick="checkSession()">Check Current Session</button>
            <div id="session-result" class="result"></div>
        </div>
    </div>

    <script>
        async function testUsersList() {
            const resultDiv = document.getElementById('users-result');
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('/api/admin/users?limit=5');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Success! Found ${data.data.pagination.total} users.\n\nFirst few users:\n${JSON.stringify(data.data.users.map(u => ({id: u.id, username: u.username, role: u.role})), null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Error: ${data.error || 'Unknown error'}\nStatus: ${response.status}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Network Error: ${error.message}`;
            }
        }

        async function testPasswordReset() {
            const resultDiv = document.getElementById('reset-result');
            const userId = document.getElementById('userId').value;
            const resetType = document.getElementById('resetType').value;
            
            if (!userId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Please enter a User ID';
                return;
            }
            
            resultDiv.textContent = 'Testing password reset...';
            
            try {
                const response = await fetch(`/api/admin/users/${userId}/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ type: resetType })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Password Reset Success!\n\nType: ${data.data.type}\nMessage: ${data.data.message}\n\n${resetType === 'link' ? `Reset Link: ${data.data.resetLink}\nExpires: ${data.data.expires}` : `Temporary Password: ${data.data.tempPassword}`}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Error: ${data.error || 'Unknown error'}\nStatus: ${response.status}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Network Error: ${error.message}`;
            }
        }

        async function checkSession() {
            const resultDiv = document.getElementById('session-result');
            resultDiv.textContent = 'Checking session...';
            
            try {
                // Try to access a protected endpoint to check authentication
                const response = await fetch('/api/admin/users?limit=1');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Authenticated as Admin/Moderator\n\nYou have access to the user management system.`;
                } else if (response.status === 403) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Access Denied\n\nYou need admin or moderator privileges to access user management.\n\nError: ${data.error}`;
                } else if (response.status === 401) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Not Authenticated\n\nPlease log in to access the admin system.`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Error: ${data.error || 'Unknown error'}\nStatus: ${response.status}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Network Error: ${error.message}`;
            }
        }

        // Auto-check session on page load
        window.addEventListener('load', checkSession);
    </script>
</body>
</html>
