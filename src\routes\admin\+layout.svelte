<script lang="ts">
  import { dev } from '$app/environment';
  import DebugPanel from '$lib/components/admin/DebugPanel.svelte';
  import ErrorBoundary from '$lib/components/ErrorBoundary.svelte';
  import { theme, type ThemeState } from '$lib/stores/theme';
  import ThemeToggle from '$lib/components/ThemeToggle.svelte';

  // Current theme state
  let themeState: ThemeState;
  $: themeState = $theme;
</script>

<ErrorBoundary>
  <div class="admin-layout" class:dark-admin={themeState?.current === 'dark'}>
    <header class="admin-header">
      <div class="logo">
        <a href="/admin">FWFC Admin</a>
      </div>
      <nav class="admin-nav">
        <a href="/admin/ai-dashboard">AI Dashboard</a>
        <a href="/admin/gallery">Gallery</a>
        <a href="/admin/media">Media</a>
        <a href="/admin/hero-images">Hero Images</a>
        <a href="/admin/news">News</a>
        <a href="/admin/users">Users</a>
        <a href="/admin/post-as-user">Post as User</a>
        <a href="/admin/ai-content-reviews">AI Content Reviews</a>
        <a href="/admin/scheduled-content">Scheduled Content</a>
        <a href="/admin/simulate-interactions">Simulate Interactions</a>
        <a href="/admin/audit-logs">Audit Logs</a>
        <a href="/admin/site-settings">Site Settings</a>
        <a href="/">View Site</a>
      </nav>
      <div class="admin-theme-controls">
        <ThemeToggle size="sm" variant="button" />
      </div>
    </header>

    <main class="admin-content">
      <slot />
    </main>

    <footer class="admin-footer">
      <p>Finn Wolfhard Fan Club Admin Panel &copy; {new Date().getFullYear()}</p>
    </footer>
  </div>

  {#if dev}
    <DebugPanel />
  {/if}
</ErrorBoundary>

<style>
  .admin-layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    transition: var(--transition-theme);
  }

  .admin-header {
    background-color: var(--color-surface-secondary);
    color: var(--color-text-primary);
    border-bottom: var(--border-width-thin) solid var(--color-border-primary);
    padding: var(--space-md) var(--space-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-theme);
  }

  .logo a {
    color: var(--color-interactive-primary);
    text-decoration: none;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    transition: var(--transition-theme);
  }

  .logo a:hover {
    color: var(--color-interactive-primary-hover);
  }

  .admin-nav {
    display: flex;
    gap: var(--space-lg);
    flex-wrap: wrap;
  }

  .admin-nav a {
    color: var(--color-text-primary);
    text-decoration: none;
    padding: var(--space-sm) 0;
    position: relative;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: var(--transition-theme);
  }

  .admin-nav a:hover {
    color: var(--color-interactive-primary);
  }

  .admin-theme-controls {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
  }

  .admin-nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--color-interactive-primary);
    transition: width var(--transition-normal);
  }

  .admin-nav a:hover::after {
    width: 100%;
  }

  .admin-content {
    flex: 1;
    padding: var(--space-xl);
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    transition: var(--transition-theme);
  }

  .admin-footer {
    background-color: var(--color-surface-secondary);
    color: var(--color-text-secondary);
    border-top: var(--border-width-thin) solid var(--color-border-primary);
    padding: var(--space-md) var(--space-xl);
    text-align: center;
    font-size: var(--font-size-sm);
    transition: var(--transition-theme);
  }

  /* Dark theme specific adjustments */
  .dark-admin .admin-header {
    box-shadow: var(--shadow-md);
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .admin-header {
      flex-direction: column;
      gap: var(--space-md);
      padding: var(--space-md);
    }

    .admin-nav {
      gap: var(--space-md);
      justify-content: center;
    }

    .admin-content {
      padding: var(--space-md);
    }
  }
</style>
