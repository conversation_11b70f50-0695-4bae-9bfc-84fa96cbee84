import { GEMINI_API_KEY } from '$env/static/private';
import logger from './logger';
import { db } from '../db';
import { aiContentReviews, aiGenerationMetrics } from '../db/schema';
import { moderateContent, sanitizeContent, type ModerationResult } from './contentModeration';

/**
 * Calculate authenticity score for generated content
 */
function calculateAuthenticityScore(
  content: GeneratedContent,
  userContext: any,
  params: GenerationParams
): number {
  let score = 70; // Base score

  // Check content length appropriateness
  const contentLength = (content.content || '').length;
  if (params.length === 'short' && contentLength > 500) score -= 10;
  if (params.length === 'medium' && (contentLength < 200 || contentLength > 1000)) score -= 10;
  if (params.length === 'long' && contentLength < 500) score -= 10;

  // Check for overly promotional language
  const promotionalWords = ['amazing', 'incredible', 'fantastic', 'perfect', 'best ever'];
  const promotionalCount = promotionalWords.filter(word =>
    content.content.toLowerCase().includes(word)
  ).length;
  if (promotionalCount > 2) score -= 15;

  // Check for personality consistency (if user has personality traits)
  if (userContext.isSimulated && userContext.simulatedPersonality) {
    try {
      const personality = JSON.parse(userContext.simulatedPersonality);
      if (personality.traits) {
        // Bonus for including personality-appropriate language
        score += 10;
      }
    } catch (e) {
      // Ignore parsing errors
    }
  }

  // Check for focus area relevance
  if (params.focusAreas && params.focusAreas.length > 0) {
    const focusKeywords = {
      'stranger_things': ['stranger things', 'hawkins', 'upside down', 'mike wheeler'],
      'it_movies': ['it', 'pennywise', 'losers club', 'derry'],
      'music': ['aubreys', 'music', 'band', 'guitar', 'song'],
      'acting': ['acting', 'performance', 'role', 'character'],
      'interviews': ['interview', 'press', 'questions', 'answers'],
      'fan_interactions': ['fans', 'meet', 'convention', 'autograph'],
      'behind_scenes': ['behind the scenes', 'filming', 'set', 'production'],
      'upcoming_projects': ['upcoming', 'new', 'project', 'movie', 'show']
    };

    let relevanceFound = false;
    for (const area of params.focusAreas) {
      const keywords = focusKeywords[area as keyof typeof focusKeywords] || [];
      if (keywords.some(keyword => content.content.toLowerCase().includes(keyword))) {
        relevanceFound = true;
        break;
      }
    }
    if (relevanceFound) score += 10;
    else score -= 5;
  }

  // Check for natural conversation flow
  const sentences = content.content.split(/[.!?]+/).filter(s => s.trim().length > 0);
  if (sentences.length > 0) {
    const avgSentenceLength = content.content.length / sentences.length;
    if (avgSentenceLength > 20 && avgSentenceLength < 100) score += 5; // Natural sentence length
  }

  // Ensure score is within bounds
  return Math.max(0, Math.min(100, score));
}

type GenerationParams = {
  contentType: string;
  prompt: string;
  tone: string;
  length: string;
  userContext: any;
  focusAreas?: string[];
  includePersonality?: boolean;
};

type GeneratedContent = {
  title?: string;
  content: string;
  description?: string;
  imagePrompt?: string;
  authenticityScore?: number;
  moderationResult?: ModerationResult;
  metadata?: {
    tone: string;
    length: string;
    focusAreas: string[];
    generatedAt: string;
  };
};

export async function generateWithGemini(params: GenerationParams): Promise<GeneratedContent> {
  try {
    // Validate API key is available
    if (!GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is not set');
    }

    const { contentType, prompt, tone, length, userContext, focusAreas = [], includePersonality = true } = params;
    
    // Construct system prompt based on user context
    let systemPrompt = `You are writing content as ${userContext.displayName} (@${userContext.username}) for the Finn Wolfhard Fan Club community.`;

    // Add personality context if enabled and available
    if (includePersonality && userContext.isSimulated && userContext.simulatedPersonality) {
      const personality = JSON.parse(userContext.simulatedPersonality);
      systemPrompt += ` This user has the following personality traits: ${personality.traits.join(', ')}.`;
      systemPrompt += ` Their writing style is ${personality.writingStyle}.`;
    }

    // Add focus areas if specified
    if (focusAreas.length > 0) {
      const focusAreaMap: Record<string, string> = {
        'stranger_things': 'Stranger Things',
        'it_movies': 'IT Movies',
        'music': 'Music & The Aubreys',
        'acting': 'Acting Career',
        'interviews': 'Interviews & Press',
        'fan_interactions': 'Fan Interactions',
        'behind_scenes': 'Behind the Scenes',
        'upcoming_projects': 'Upcoming Projects'
      };
      const focusLabels = focusAreas.map(area => focusAreaMap[area] || area).join(', ');
      systemPrompt += ` Focus on these areas: ${focusLabels}.`;
    }

    systemPrompt += ` Generate ${contentType} content with a ${tone} tone and ${length} length based on the following prompt: "${prompt}".`;

    // Add authenticity guidelines
    systemPrompt += ` The content should feel authentic to a genuine fan community member and avoid overly promotional or artificial language.`;
    
    // Add content type specific instructions
    switch (contentType) {
      case 'news':
        systemPrompt += ' Include a title and main content for a news article.';
        break;
      case 'gallery':
        systemPrompt += ' Include a title, brief description, and an image prompt.';
        break;
      case 'comment':
        systemPrompt += ' Write a comment that sounds natural and conversational.';
        break;
      case 'message':
        systemPrompt += ' Write a message that sounds like casual conversation.';
        break;
    }

    // Call Gemini API
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': GEMINI_API_KEY
      },
      body: JSON.stringify({
        contents: [
          {
            role: 'user',
            parts: [{ text: systemPrompt }]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024
        }
      })
    });

    const data = await response.json();
    
    // Parse the response based on content type
    let result: GeneratedContent = {
      content: '',
      metadata: {
        tone,
        length,
        focusAreas,
        generatedAt: new Date().toISOString()
      }
    };
    
    if (data.candidates && data.candidates[0]?.content?.parts[0]?.text) {
      const generatedText = data.candidates[0].content.parts[0].text;
      
      switch (contentType) {
        case 'news':
          // Extract title and content
          const titleMatch = generatedText.match(/^#\s*(.*?)(?:\n|$)/);
          result.title = titleMatch ? titleMatch[1] : '';
          result.content = titleMatch ? generatedText.replace(titleMatch[0], '') : generatedText;
          break;
          
        case 'gallery':
          // Extract title, description and image prompt
          const galleryTitleMatch = generatedText.match(/^#\s*(.*?)(?:\n|$)/);
          result.title = galleryTitleMatch ? galleryTitleMatch[1] : '';
          
          const parts = generatedText.split('IMAGE PROMPT:');
          if (parts.length > 1) {
            result.description = parts[0].replace(/^#\s*(.*?)(?:\n|$)/, '').trim();
            result.imagePrompt = parts[1].trim();
          } else {
            result.description = generatedText;
          }
          break;
          
        case 'comment':
        case 'message':
          // Just use the generated text directly
          result.content = generatedText;
          break;
      }
    }

    // Calculate authenticity score
    result.authenticityScore = calculateAuthenticityScore(result, userContext, params);

    // Moderate content for safety
    result.moderationResult = moderateContent(result.content, params.contentType);

    // If content is inappropriate, sanitize it
    if (!result.moderationResult.isAppropriate) {
      logger.warn('Generated content failed moderation', {
        contentType: params.contentType,
        flags: result.moderationResult.flags,
        blockedReasons: result.moderationResult.blockedReasons
      });

      // Sanitize the content
      result.content = sanitizeContent(result.content);

      // Re-moderate the sanitized content
      result.moderationResult = moderateContent(result.content, params.contentType);

      // If still inappropriate, throw an error
      if (!result.moderationResult.isAppropriate) {
        throw new Error('Generated content violates safety guidelines and cannot be sanitized');
      }
    }

    return result;
  } catch (error) {
    logger.error('Error calling Gemini API:', error);
    throw new Error('Failed to generate content with AI');
  }
}

/**
 * Save AI-generated content for review
 */
export async function saveContentForReview(
  params: GenerationParams,
  generatedContent: GeneratedContent,
  createdById: number
): Promise<number> {
  try {
    const result = await db.insert(aiContentReviews).values({
      contentType: params.contentType as 'news' | 'gallery' | 'comment' | 'message' | 'reply',
      originalPrompt: params.prompt,
      generatedContent: JSON.stringify(generatedContent),
      authenticityScore: generatedContent.authenticityScore || 0,
      aiConfig: JSON.stringify({
        tone: params.tone,
        length: params.length,
        focusAreas: params.focusAreas || [],
        includePersonality: params.includePersonality
      }),
      targetUserId: params.userContext.id,
      createdById,
      reviewStatus: 'pending'
    }).returning();

    return result[0].id;
  } catch (error) {
    logger.error('Error saving content for review:', error);
    throw new Error('Failed to save content for review');
  }
}

/**
 * Track AI generation metrics
 */
export async function trackGenerationMetrics(
  params: GenerationParams,
  generatedContent: GeneratedContent | null,
  generationTimeMs: number,
  success: boolean,
  errorMessage?: string,
  userId?: number
): Promise<void> {
  try {
    await db.insert(aiGenerationMetrics).values({
      contentType: params.contentType as 'news' | 'gallery' | 'comment' | 'message' | 'reply',
      promptLength: params.prompt.length,
      generatedLength: generatedContent?.content?.length || 0,
      authenticityScore: generatedContent?.authenticityScore || 0,
      tone: params.tone,
      length: params.length,
      focusAreas: params.focusAreas || [],
      generationTimeMs,
      success,
      errorMessage: errorMessage || null,
      userId: userId || params.userContext.id
    });
  } catch (error) {
    logger.error('Error tracking generation metrics:', error);
    // Don't throw here as this is just for tracking
  }
}

/**
 * Enhanced content generation with review workflow
 */
export async function generateContentWithReview(
  params: GenerationParams,
  createdById: number,
  requireReview: boolean = true
): Promise<{ content: GeneratedContent; reviewId?: number }> {
  const startTime = Date.now();
  let generatedContent: GeneratedContent | null = null;
  let success = false;
  let errorMessage: string | undefined;

  try {
    generatedContent = await generateWithGemini(params);
    success = true;

    let reviewId: number | undefined;

    // Save for review if required or if authenticity score is low
    if (requireReview || (generatedContent.authenticityScore && generatedContent.authenticityScore < 70)) {
      reviewId = await saveContentForReview(params, generatedContent, createdById);
    }

    return { content: generatedContent, reviewId };
  } catch (error) {
    errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw error;
  } finally {
    const generationTime = Date.now() - startTime;
    await trackGenerationMetrics(
      params,
      generatedContent,
      generationTime,
      success,
      errorMessage,
      createdById
    );
  }
}