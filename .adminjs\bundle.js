(function (React, designSystem) {
  'use strict';

  function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

  var React__default = /*#__PURE__*/_interopDefault(React);

  const Dashboard = () => {
    return /*#__PURE__*/React__default.default.createElement(designSystem.Box, {
      variant: "grey"
    }, /*#__PURE__*/React__default.default.createElement(designSystem.Box, {
      variant: "white",
      style: {
        padding: '20px'
      }
    }, /*#__PURE__*/React__default.default.createElement(designSystem.H2, null, "Welcome to Finn Wolfhard Fan Club Admin Panel"), /*#__PURE__*/React__default.default.createElement(designSystem.Text, null, "This is the administration panel for the Finn Wolfhard Fan Club website."), /*#__PURE__*/React__default.default.createElement(designSystem.Box, {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        marginTop: '20px'
      }
    }, /*#__PURE__*/React__default.default.createElement(designSystem.Box, {
      style: {
        flex: 1,
        padding: '20px',
        margin: '10px',
        backgroundColor: '#f0f0f0',
        borderRadius: '10px'
      }
    }, /*#__PURE__*/React__default.default.createElement(designSystem.H5, null, "User Management"), /*#__PURE__*/React__default.default.createElement(designSystem.Text, null, "Manage users, roles, and permissions")), /*#__PURE__*/React__default.default.createElement(designSystem.Box, {
      style: {
        flex: 1,
        padding: '20px',
        margin: '10px',
        backgroundColor: '#f0f0f0',
        borderRadius: '10px'
      }
    }, /*#__PURE__*/React__default.default.createElement(designSystem.H5, null, "Content Management"), /*#__PURE__*/React__default.default.createElement(designSystem.Text, null, "Manage news articles and gallery images")), /*#__PURE__*/React__default.default.createElement(designSystem.Box, {
      style: {
        flex: 1,
        padding: '20px',
        margin: '10px',
        backgroundColor: '#f0f0f0',
        borderRadius: '10px'
      }
    }, /*#__PURE__*/React__default.default.createElement(designSystem.H5, null, "Community Management"), /*#__PURE__*/React__default.default.createElement(designSystem.Text, null, "Moderate messages, replies, and comments"))), /*#__PURE__*/React__default.default.createElement(designSystem.Box, {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        marginTop: '20px'
      }
    }, /*#__PURE__*/React__default.default.createElement(designSystem.Box, {
      style: {
        flex: 1,
        padding: '20px',
        margin: '10px',
        backgroundColor: '#f0f0f0',
        borderRadius: '10px'
      }
    }, /*#__PURE__*/React__default.default.createElement(designSystem.H5, null, "Site Settings"), /*#__PURE__*/React__default.default.createElement(designSystem.Text, null, "Configure website settings and appearance")), /*#__PURE__*/React__default.default.createElement(designSystem.Box, {
      style: {
        flex: 1,
        padding: '20px',
        margin: '10px',
        backgroundColor: '#f0f0f0',
        borderRadius: '10px'
      }
    }, /*#__PURE__*/React__default.default.createElement(designSystem.H5, null, "Message of the Day"), /*#__PURE__*/React__default.default.createElement(designSystem.Text, null, "Manage rotating facts about Finn Wolfhard")), /*#__PURE__*/React__default.default.createElement(designSystem.Box, {
      style: {
        flex: 1,
        padding: '20px',
        margin: '10px',
        backgroundColor: '#f0f0f0',
        borderRadius: '10px'
      }
    }, /*#__PURE__*/React__default.default.createElement(designSystem.H5, null, "Accessibility"), /*#__PURE__*/React__default.default.createElement(designSystem.Text, null, "Configure accessibility features")))));
  };

  AdminJS.UserComponents = {};
  AdminJS.UserComponents.Dashboard = Dashboard;

})(React, AdminJSDesignSystem);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYnVuZGxlLmpzIiwic291cmNlcyI6WyIuLi9zcmMvbGliL3NlcnZlci9hZG1pbi9jb21wb25lbnRzL2Rhc2hib2FyZC5qc3giLCJlbnRyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQm94LCBIMiwgSDUsIFRleHQgfSBmcm9tICdAYWRtaW5qcy9kZXNpZ24tc3lzdGVtJztcblxuY29uc3QgRGFzaGJvYXJkID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxCb3ggdmFyaWFudD1cImdyZXlcIj5cbiAgICAgIDxCb3ggdmFyaWFudD1cIndoaXRlXCIgc3R5bGU9e3sgcGFkZGluZzogJzIwcHgnIH19PlxuICAgICAgICA8SDI+V2VsY29tZSB0byBGaW5uIFdvbGZoYXJkIEZhbiBDbHViIEFkbWluIFBhbmVsPC9IMj5cbiAgICAgICAgPFRleHQ+VGhpcyBpcyB0aGUgYWRtaW5pc3RyYXRpb24gcGFuZWwgZm9yIHRoZSBGaW5uIFdvbGZoYXJkIEZhbiBDbHViIHdlYnNpdGUuPC9UZXh0PlxuXG4gICAgICAgIDxCb3ggc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLCBtYXJnaW5Ub3A6ICcyMHB4JyB9fT5cbiAgICAgICAgICA8Qm94IHN0eWxlPXt7IGZsZXg6IDEsIHBhZGRpbmc6ICcyMHB4JywgbWFyZ2luOiAnMTBweCcsIGJhY2tncm91bmRDb2xvcjogJyNmMGYwZjAnLCBib3JkZXJSYWRpdXM6ICcxMHB4JyB9fT5cbiAgICAgICAgICAgIDxINT5Vc2VyIE1hbmFnZW1lbnQ8L0g1PlxuICAgICAgICAgICAgPFRleHQ+TWFuYWdlIHVzZXJzLCByb2xlcywgYW5kIHBlcm1pc3Npb25zPC9UZXh0PlxuICAgICAgICAgIDwvQm94PlxuXG4gICAgICAgICAgPEJveCBzdHlsZT17eyBmbGV4OiAxLCBwYWRkaW5nOiAnMjBweCcsIG1hcmdpbjogJzEwcHgnLCBiYWNrZ3JvdW5kQ29sb3I6ICcjZjBmMGYwJywgYm9yZGVyUmFkaXVzOiAnMTBweCcgfX0+XG4gICAgICAgICAgICA8SDU+Q29udGVudCBNYW5hZ2VtZW50PC9INT5cbiAgICAgICAgICAgIDxUZXh0Pk1hbmFnZSBuZXdzIGFydGljbGVzIGFuZCBnYWxsZXJ5IGltYWdlczwvVGV4dD5cbiAgICAgICAgICA8L0JveD5cblxuICAgICAgICAgIDxCb3ggc3R5bGU9e3sgZmxleDogMSwgcGFkZGluZzogJzIwcHgnLCBtYXJnaW46ICcxMHB4JywgYmFja2dyb3VuZENvbG9yOiAnI2YwZjBmMCcsIGJvcmRlclJhZGl1czogJzEwcHgnIH19PlxuICAgICAgICAgICAgPEg1PkNvbW11bml0eSBNYW5hZ2VtZW50PC9INT5cbiAgICAgICAgICAgIDxUZXh0Pk1vZGVyYXRlIG1lc3NhZ2VzLCByZXBsaWVzLCBhbmQgY29tbWVudHM8L1RleHQ+XG4gICAgICAgICAgPC9Cb3g+XG4gICAgICAgIDwvQm94PlxuXG4gICAgICAgIDxCb3ggc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLCBtYXJnaW5Ub3A6ICcyMHB4JyB9fT5cbiAgICAgICAgICA8Qm94IHN0eWxlPXt7IGZsZXg6IDEsIHBhZGRpbmc6ICcyMHB4JywgbWFyZ2luOiAnMTBweCcsIGJhY2tncm91bmRDb2xvcjogJyNmMGYwZjAnLCBib3JkZXJSYWRpdXM6ICcxMHB4JyB9fT5cbiAgICAgICAgICAgIDxINT5TaXRlIFNldHRpbmdzPC9INT5cbiAgICAgICAgICAgIDxUZXh0PkNvbmZpZ3VyZSB3ZWJzaXRlIHNldHRpbmdzIGFuZCBhcHBlYXJhbmNlPC9UZXh0PlxuICAgICAgICAgIDwvQm94PlxuXG4gICAgICAgICAgPEJveCBzdHlsZT17eyBmbGV4OiAxLCBwYWRkaW5nOiAnMjBweCcsIG1hcmdpbjogJzEwcHgnLCBiYWNrZ3JvdW5kQ29sb3I6ICcjZjBmMGYwJywgYm9yZGVyUmFkaXVzOiAnMTBweCcgfX0+XG4gICAgICAgICAgICA8SDU+TWVzc2FnZSBvZiB0aGUgRGF5PC9INT5cbiAgICAgICAgICAgIDxUZXh0Pk1hbmFnZSByb3RhdGluZyBmYWN0cyBhYm91dCBGaW5uIFdvbGZoYXJkPC9UZXh0PlxuICAgICAgICAgIDwvQm94PlxuXG4gICAgICAgICAgPEJveCBzdHlsZT17eyBmbGV4OiAxLCBwYWRkaW5nOiAnMjBweCcsIG1hcmdpbjogJzEwcHgnLCBiYWNrZ3JvdW5kQ29sb3I6ICcjZjBmMGYwJywgYm9yZGVyUmFkaXVzOiAnMTBweCcgfX0+XG4gICAgICAgICAgICA8SDU+QWNjZXNzaWJpbGl0eTwvSDU+XG4gICAgICAgICAgICA8VGV4dD5Db25maWd1cmUgYWNjZXNzaWJpbGl0eSBmZWF0dXJlczwvVGV4dD5cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgPC9Cb3g+XG4gICAgICA8L0JveD5cbiAgICA8L0JveD5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IERhc2hib2FyZDtcbiIsIkFkbWluSlMuVXNlckNvbXBvbmVudHMgPSB7fVxuaW1wb3J0IERhc2hib2FyZCBmcm9tICcuLi9zcmMvbGliL3NlcnZlci9hZG1pbi9jb21wb25lbnRzL2Rhc2hib2FyZCdcbkFkbWluSlMuVXNlckNvbXBvbmVudHMuRGFzaGJvYXJkID0gRGFzaGJvYXJkIl0sIm5hbWVzIjpbIkRhc2hib2FyZCIsIlJlYWN0IiwiY3JlYXRlRWxlbWVudCIsIkJveCIsInZhcmlhbnQiLCJzdHlsZSIsInBhZGRpbmciLCJIMiIsIlRleHQiLCJkaXNwbGF5IiwianVzdGlmeUNvbnRlbnQiLCJtYXJnaW5Ub3AiLCJmbGV4IiwibWFyZ2luIiwiYmFja2dyb3VuZENvbG9yIiwiYm9yZGVyUmFkaXVzIiwiSDUiLCJBZG1pbkpTIiwiVXNlckNvbXBvbmVudHMiXSwibWFwcGluZ3MiOiI7Ozs7Ozs7RUFHQSxNQUFNQSxTQUFTLEdBQUdBLE1BQU07RUFDdEIsRUFBQSxvQkFDRUMsc0JBQUEsQ0FBQUMsYUFBQSxDQUFDQyxnQkFBRyxFQUFBO0VBQUNDLElBQUFBLE9BQU8sRUFBQztFQUFNLEdBQUEsZUFDakJILHNCQUFBLENBQUFDLGFBQUEsQ0FBQ0MsZ0JBQUcsRUFBQTtFQUFDQyxJQUFBQSxPQUFPLEVBQUMsT0FBTztFQUFDQyxJQUFBQSxLQUFLLEVBQUU7RUFBRUMsTUFBQUEsT0FBTyxFQUFFO0VBQU87S0FDNUNMLGVBQUFBLHNCQUFBLENBQUFDLGFBQUEsQ0FBQ0ssZUFBRSxFQUFDLElBQUEsRUFBQSwrQ0FBaUQsQ0FBQyxlQUN0RE4sc0JBQUEsQ0FBQUMsYUFBQSxDQUFDTSxpQkFBSSxRQUFDLDBFQUE4RSxDQUFDLGVBRXJGUCxzQkFBQSxDQUFBQyxhQUFBLENBQUNDLGdCQUFHLEVBQUE7RUFBQ0UsSUFBQUEsS0FBSyxFQUFFO0VBQUVJLE1BQUFBLE9BQU8sRUFBRSxNQUFNO0VBQUVDLE1BQUFBLGNBQWMsRUFBRSxlQUFlO0VBQUVDLE1BQUFBLFNBQVMsRUFBRTtFQUFPO0VBQUUsR0FBQSxlQUNsRlYsc0JBQUEsQ0FBQUMsYUFBQSxDQUFDQyxnQkFBRyxFQUFBO0VBQUNFLElBQUFBLEtBQUssRUFBRTtFQUFFTyxNQUFBQSxJQUFJLEVBQUUsQ0FBQztFQUFFTixNQUFBQSxPQUFPLEVBQUUsTUFBTTtFQUFFTyxNQUFBQSxNQUFNLEVBQUUsTUFBTTtFQUFFQyxNQUFBQSxlQUFlLEVBQUUsU0FBUztFQUFFQyxNQUFBQSxZQUFZLEVBQUU7RUFBTztLQUN2R2QsZUFBQUEsc0JBQUEsQ0FBQUMsYUFBQSxDQUFDYyxlQUFFLEVBQUMsSUFBQSxFQUFBLGlCQUFtQixDQUFDLGVBQ3hCZixzQkFBQSxDQUFBQyxhQUFBLENBQUNNLGlCQUFJLEVBQUMsSUFBQSxFQUFBLHNDQUEwQyxDQUM3QyxDQUFDLGVBRU5QLHNCQUFBLENBQUFDLGFBQUEsQ0FBQ0MsZ0JBQUcsRUFBQTtFQUFDRSxJQUFBQSxLQUFLLEVBQUU7RUFBRU8sTUFBQUEsSUFBSSxFQUFFLENBQUM7RUFBRU4sTUFBQUEsT0FBTyxFQUFFLE1BQU07RUFBRU8sTUFBQUEsTUFBTSxFQUFFLE1BQU07RUFBRUMsTUFBQUEsZUFBZSxFQUFFLFNBQVM7RUFBRUMsTUFBQUEsWUFBWSxFQUFFO0VBQU87S0FDdkdkLGVBQUFBLHNCQUFBLENBQUFDLGFBQUEsQ0FBQ2MsZUFBRSxFQUFDLElBQUEsRUFBQSxvQkFBc0IsQ0FBQyxlQUMzQmYsc0JBQUEsQ0FBQUMsYUFBQSxDQUFDTSxpQkFBSSxFQUFDLElBQUEsRUFBQSx5Q0FBNkMsQ0FDaEQsQ0FBQyxlQUVOUCxzQkFBQSxDQUFBQyxhQUFBLENBQUNDLGdCQUFHLEVBQUE7RUFBQ0UsSUFBQUEsS0FBSyxFQUFFO0VBQUVPLE1BQUFBLElBQUksRUFBRSxDQUFDO0VBQUVOLE1BQUFBLE9BQU8sRUFBRSxNQUFNO0VBQUVPLE1BQUFBLE1BQU0sRUFBRSxNQUFNO0VBQUVDLE1BQUFBLGVBQWUsRUFBRSxTQUFTO0VBQUVDLE1BQUFBLFlBQVksRUFBRTtFQUFPO0tBQ3ZHZCxlQUFBQSxzQkFBQSxDQUFBQyxhQUFBLENBQUNjLGVBQUUsUUFBQyxzQkFBd0IsQ0FBQyxlQUM3QmYsc0JBQUEsQ0FBQUMsYUFBQSxDQUFDTSxpQkFBSSxFQUFBLElBQUEsRUFBQywwQ0FBOEMsQ0FDakQsQ0FDRixDQUFDLGVBRU5QLHNCQUFBLENBQUFDLGFBQUEsQ0FBQ0MsZ0JBQUcsRUFBQTtFQUFDRSxJQUFBQSxLQUFLLEVBQUU7RUFBRUksTUFBQUEsT0FBTyxFQUFFLE1BQU07RUFBRUMsTUFBQUEsY0FBYyxFQUFFLGVBQWU7RUFBRUMsTUFBQUEsU0FBUyxFQUFFO0VBQU87RUFBRSxHQUFBLGVBQ2xGVixzQkFBQSxDQUFBQyxhQUFBLENBQUNDLGdCQUFHLEVBQUE7RUFBQ0UsSUFBQUEsS0FBSyxFQUFFO0VBQUVPLE1BQUFBLElBQUksRUFBRSxDQUFDO0VBQUVOLE1BQUFBLE9BQU8sRUFBRSxNQUFNO0VBQUVPLE1BQUFBLE1BQU0sRUFBRSxNQUFNO0VBQUVDLE1BQUFBLGVBQWUsRUFBRSxTQUFTO0VBQUVDLE1BQUFBLFlBQVksRUFBRTtFQUFPO0tBQ3ZHZCxlQUFBQSxzQkFBQSxDQUFBQyxhQUFBLENBQUNjLGVBQUUsRUFBQyxJQUFBLEVBQUEsZUFBaUIsQ0FBQyxlQUN0QmYsc0JBQUEsQ0FBQUMsYUFBQSxDQUFDTSxpQkFBSSxFQUFDLElBQUEsRUFBQSwyQ0FBK0MsQ0FDbEQsQ0FBQyxlQUVOUCxzQkFBQSxDQUFBQyxhQUFBLENBQUNDLGdCQUFHLEVBQUE7RUFBQ0UsSUFBQUEsS0FBSyxFQUFFO0VBQUVPLE1BQUFBLElBQUksRUFBRSxDQUFDO0VBQUVOLE1BQUFBLE9BQU8sRUFBRSxNQUFNO0VBQUVPLE1BQUFBLE1BQU0sRUFBRSxNQUFNO0VBQUVDLE1BQUFBLGVBQWUsRUFBRSxTQUFTO0VBQUVDLE1BQUFBLFlBQVksRUFBRTtFQUFPO0tBQ3ZHZCxlQUFBQSxzQkFBQSxDQUFBQyxhQUFBLENBQUNjLGVBQUUsRUFBQyxJQUFBLEVBQUEsb0JBQXNCLENBQUMsZUFDM0JmLHNCQUFBLENBQUFDLGFBQUEsQ0FBQ00saUJBQUksRUFBQyxJQUFBLEVBQUEsMkNBQStDLENBQ2xELENBQUMsZUFFTlAsc0JBQUEsQ0FBQUMsYUFBQSxDQUFDQyxnQkFBRyxFQUFBO0VBQUNFLElBQUFBLEtBQUssRUFBRTtFQUFFTyxNQUFBQSxJQUFJLEVBQUUsQ0FBQztFQUFFTixNQUFBQSxPQUFPLEVBQUUsTUFBTTtFQUFFTyxNQUFBQSxNQUFNLEVBQUUsTUFBTTtFQUFFQyxNQUFBQSxlQUFlLEVBQUUsU0FBUztFQUFFQyxNQUFBQSxZQUFZLEVBQUU7RUFBTztLQUN2R2QsZUFBQUEsc0JBQUEsQ0FBQUMsYUFBQSxDQUFDYyxlQUFFLEVBQUMsSUFBQSxFQUFBLGVBQWlCLENBQUMsZUFDdEJmLHNCQUFBLENBQUFDLGFBQUEsQ0FBQ00saUJBQUksRUFBQyxJQUFBLEVBQUEsa0NBQXNDLENBQ3pDLENBQ0YsQ0FDRixDQUNGLENBQUM7RUFFVixDQUFDOztFQzlDRFMsT0FBTyxDQUFDQyxjQUFjLEdBQUcsRUFBRTtFQUUzQkQsT0FBTyxDQUFDQyxjQUFjLENBQUNsQixTQUFTLEdBQUdBLFNBQVM7Ozs7OzsifQ==
