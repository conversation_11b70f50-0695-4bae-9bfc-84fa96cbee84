import { db } from '$lib/server/db';
import { news } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ url }) => {
  try {
    // Get query parameters for pagination
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Fetch published news articles with pagination
    const articles = await db.select()
      .from(news)
      .where(eq(news.published, true))
      .limit(limit)
      .offset(offset)
      .orderBy(news.createdAt);

    // Get total count for pagination
    const totalResult = await db.select({ count: news.id })
      .from(news)
      .where(eq(news.published, true));
    
    const total = totalResult.length;
    const totalPages = Math.ceil(total / limit);

    return {
      articles,
      pagination: {
        currentPage: page,
        totalPages,
        limit,
        total,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('Error fetching news articles:', error);
    return {
      articles: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        limit: 10,
        total: 0,
        hasNext: false,
        hasPrev: false
      }
    };
  }
};
