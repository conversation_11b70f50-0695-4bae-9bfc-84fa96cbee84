import { db } from './db';
import { users } from './db/schema';
import { eq } from 'drizzle-orm';
import crypto from 'crypto';

// Simple password hashing function
export function hashPassword(password: string): string {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
}

// Verify password
export function verifyPassword(password: string, hashedPassword: string): boolean {
  const [salt, storedHash] = hashedPassword.split(':');
  const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  return storedHash === hash;
}

// User authentication
export async function authenticateUser(username: string, password: string) {
  const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
  
  if (result.length === 0) {
    return null;
  }
  
  const user = result[0];
  
  if (!verifyPassword(password, user.passwordHash)) {
    return null;
  }
  
  // Don't return the password hash
  const { passwordHash, ...userWithoutPassword } = user;
  return userWithoutPassword;
}

// Create a new user
export async function createUser(userData: {
  username: string;
  displayName: string;
  email: string;
  password: string;
  role?: string;
}) {
  const { password, ...rest } = userData;
  const passwordHash = hashPassword(password);
  
  try {
    const result = await db.insert(users).values({
      ...rest,
      passwordHash,
      role: userData.role || 'user'
    }).returning();
    
    if (result.length > 0) {
      const { passwordHash, ...userWithoutPassword } = result[0];
      return userWithoutPassword;
    }
    return null;
  } catch (error) {
    console.error('Error creating user:', error);
    return null;
  }
}

// Get user by ID
export async function getUserById(id: number) {
  const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
  
  if (result.length === 0) {
    return null;
  }
  
  const { passwordHash, ...userWithoutPassword } = result[0];
  return userWithoutPassword;
}

// Update user
export async function updateUser(id: number, userData: Partial<{
  displayName: string;
  email: string;
  password: string;
  role: string;
  preferences: {
    highContrast: boolean;
    largeText: boolean;
    simplifiedInterface: boolean;
  };
}>) {
  const updateData: any = { ...userData };
  
  // If password is being updated, hash it
  if (userData.password) {
    updateData.passwordHash = hashPassword(userData.password);
    delete updateData.password;
  }
  
  try {
    const result = await db.update(users)
      .set(updateData)
      .where(eq(users.id, id))
      .returning();
    
    if (result.length > 0) {
      const { passwordHash, ...userWithoutPassword } = result[0];
      return userWithoutPassword;
    }
    return null;
  } catch (error) {
    console.error('Error updating user:', error);
    return null;
  }
}
