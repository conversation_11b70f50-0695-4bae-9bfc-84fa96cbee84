import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { scheduledContent, users, news, gallery, comments, messages, contentAuthorship, auditLogs } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// PATCH /api/admin/scheduled-content/[id] - Update scheduled content item
export const PATCH: RequestHandler = async ({ params, request, locals, getClientAddress }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const itemId = parseInt(params.id);
    if (isNaN(itemId)) {
      return json({
        success: false,
        error: 'Invalid item ID'
      }, { status: 400 });
    }

    const body = await request.json();
    const { status } = body;

    if (!status || !['pending', 'published', 'failed', 'cancelled'].includes(status)) {
      return json({
        success: false,
        error: 'Invalid status'
      }, { status: 400 });
    }

    // Get the current item
    const currentItem = await db.select()
      .from(scheduledContent)
      .where(eq(scheduledContent.id, itemId))
      .limit(1);

    if (currentItem.length === 0) {
      return json({
        success: false,
        error: 'Scheduled content item not found'
      }, { status: 404 });
    }

    // Update the item
    const result = await db.update(scheduledContent)
      .set({
        status: status as any,
        updatedAt: new Date().toISOString(),
        ...(status === 'pending' && { errorMessage: null })
      })
      .where(eq(scheduledContent.id, itemId))
      .returning();

    // Log admin action
    await logAdminAction(
      locals.user.id,
      'update_scheduled_content',
      'scheduled_content',
      itemId,
      currentItem[0].asUserId,
      {
        oldStatus: currentItem[0].status,
        newStatus: status
      },
      getClientAddress()
    );

    logger.info('Scheduled content item updated', {
      adminUser: locals.user.username,
      itemId,
      oldStatus: currentItem[0].status,
      newStatus: status
    });

    return json({
      success: true,
      data: result[0],
      message: `Scheduled content ${status === 'cancelled' ? 'cancelled' : 'updated'} successfully`
    });

  } catch (error) {
    logger.error('Error updating scheduled content item:', error);
    return json({
      success: false,
      error: 'Failed to update scheduled content item'
    }, { status: 500 });
  }
};

// DELETE /api/admin/scheduled-content/[id] - Delete scheduled content item
export const DELETE: RequestHandler = async ({ params, locals, getClientAddress }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const itemId = parseInt(params.id);
    if (isNaN(itemId)) {
      return json({
        success: false,
        error: 'Invalid item ID'
      }, { status: 400 });
    }

    // Get the current item
    const currentItem = await db.select()
      .from(scheduledContent)
      .where(eq(scheduledContent.id, itemId))
      .limit(1);

    if (currentItem.length === 0) {
      return json({
        success: false,
        error: 'Scheduled content item not found'
      }, { status: 404 });
    }

    // Only allow deletion of pending or failed items
    if (!['pending', 'failed', 'cancelled'].includes(currentItem[0].status)) {
      return json({
        success: false,
        error: 'Cannot delete published scheduled content'
      }, { status: 400 });
    }

    // Delete the item
    await db.delete(scheduledContent)
      .where(eq(scheduledContent.id, itemId));

    // Log admin action
    await logAdminAction(
      locals.user.id,
      'delete_scheduled_content',
      'scheduled_content',
      itemId,
      currentItem[0].asUserId,
      {
        contentType: currentItem[0].contentType,
        status: currentItem[0].status
      },
      getClientAddress()
    );

    logger.info('Scheduled content item deleted', {
      adminUser: locals.user.username,
      itemId,
      contentType: currentItem[0].contentType
    });

    return json({
      success: true,
      message: 'Scheduled content item deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting scheduled content item:', error);
    return json({
      success: false,
      error: 'Failed to delete scheduled content item'
    }, { status: 500 });
  }
};

// Helper function to log admin actions
async function logAdminAction(
  adminUserId: number,
  action: string,
  targetType: string,
  targetId: number | null,
  targetUserId: number | null,
  details: any,
  ipAddress: string
) {
  try {
    await db.insert(auditLogs).values({
      adminUserId,
      action,
      targetType,
      targetId,
      targetUserId,
      details: JSON.stringify(details),
      ipAddress
    });
  } catch (error) {
    logger.error('Failed to log admin action:', error);
  }
}
