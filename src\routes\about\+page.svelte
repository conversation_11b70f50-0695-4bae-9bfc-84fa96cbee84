<script>
	// Timeline data
	const timeline = [
		{
			year: 2002,
			event: '<PERSON> was born on December 23 in Vancouver, Canada.'
		},
		{
			year: 2014,
			event: 'Made his acting debut in the independent film "Aftermath".'
		},
		{
			year: 2016,
			event: '<PERSON> to fame playing <PERSON> in the Netflix series "Stranger Things".'
		},
		{
			year: 2017,
			event: 'Starred as <PERSON> in the horror film "It".'
		},
		{
			year: 2019,
			event: 'Reprised his role as <PERSON> in "It Chapter Two".'
		},
		{
			year: 2019,
			event: 'Voiced <PERSON><PERSON><PERSON> in the animated film "The Addams Family".'
		},
		{
			year: 2020,
			event: 'Made his directorial debut with the short film "Night Shifts".'
		},
		{
			year: 2021,
			event: 'Starred in "Ghostbusters: Afterlife" as <PERSON>.'
		},
		{
			year: 2023,
			event: 'Released music with his band The Aubreys.'
		}
	];
</script>

<svelte:head>
	<title>About Finn <PERSON> - Fan Club</title>
	<meta name="description" content="Learn about actor and musician <PERSON>'s career, achievements, and biography" />
</svelte:head>

<div class="about-container">
	<section class="hero">
		<h1>About <PERSON></h1>
		<div class="hero-image">
			<img src="/images/finn-profile.jpg" alt="Finn <PERSON>" />
		</div>
	</section>

	<section class="biography">
		<h2>Biography</h2>
		<div class="bio-content">
			<p>
				Finn Wolfhard is a Canadian actor, musician, screenwriter, and director. He gained recognition for playing <PERSON> <PERSON> in the Netflix series <strong>Stranger Things</strong> and has since become one of the most recognizable young actors in Hollywood.
			</p>
			<p>
				Born on December 23, 2002, in Vancouver, British Columbia, Canada, <PERSON> began his acting career with small roles before landing his breakout role in Stranger Things in 2016. His performance earned him critical acclaim and several awards as part of the ensemble cast.
			</p>
			<p>
				Beyond acting, Finn is also passionate about music. He was previously the lead vocalist and guitarist for the rock band Calpurnia until their disbandment in 2019. He later formed a new band called The Aubreys.
			</p>
			<p>
				Finn has demonstrated his versatility as an actor through various roles in films such as <strong>It</strong> (2017), <strong>The Goldfinch</strong> (2019), <strong>The Addams Family</strong> (2019), and <strong>Ghostbusters: Afterlife</strong> (2021).
			</p>
			<p>
				In 2020, Finn made his directorial debut with the short film <strong>Night Shifts</strong>, showcasing his interest in filmmaking and storytelling from behind the camera.
			</p>
		</div>
	</section>

	<section class="timeline">
		<h2>Career Timeline</h2>
		<div class="timeline-container">
			{#each timeline as item}
				<div class="timeline-item">
					<div class="timeline-year">{item.year}</div>
					<div class="timeline-content">
						<p>{item.event}</p>
					</div>
				</div>
			{/each}
		</div>
	</section>

	<section class="achievements">
		<h2>Awards & Achievements</h2>
		<ul class="achievements-list">
			<li>Screen Actors Guild Award for Outstanding Performance by an Ensemble in a Drama Series for "Stranger Things" (2017)</li>
			<li>MTV Movie & TV Award for Best On-Screen Team for "It" (2018)</li>
			<li>Teen Choice Award nomination for Choice Summer TV Actor for "Stranger Things" (2019)</li>
			<li>People's Choice Award nomination for Favorite Male TV Star for "Stranger Things" (2019)</li>
			<li>Named one of The Hollywood Reporter's "30 Under 18" influential stars (2018)</li>
		</ul>
	</section>

	<section class="fan-club">
		<h2>About Our Fan Club</h2>
		<p>
			The Finn Wolfhard Fan Club was established in 2020 by a group of dedicated fans who wanted to create a supportive community for people who admire Finn's work and talent. Our mission is to celebrate Finn's career, share news and updates, and connect fans from around the world.
		</p>
		<p>
			We are not officially affiliated with Finn Wolfhard, his management, or any production companies. This is a fan-created and fan-run community.
		</p>
		<div class="join-cta">
			<a href="/join" class="btn primary">Join Our Community</a>
		</div>
	</section>
</div>

<style>
	.about-container {
		max-width: 900px;
		margin: 0 auto;
	}

	section {
		margin-bottom: 3rem;
	}

	.hero {
		text-align: center;
		margin-bottom: 2rem;
	}

	.hero h1 {
		font-size: 2.5rem;
		margin-bottom: 1.5rem;
	}

	.hero-image {
		max-width: 400px;
		margin: 0 auto;
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
	}

	.hero-image img {
		width: 100%;
		height: auto;
		display: block;
	}

	h2 {
		font-size: 1.8rem;
		margin-bottom: 1.5rem;
		color: var(--theme-accent-primary);
		border-bottom: 2px solid var(--theme-accent-primary);
		padding-bottom: 0.5rem;
	}

	.bio-content p {
		margin-bottom: 1rem;
		line-height: 1.6;
		color: var(--theme-text-primary);
	}

	.timeline-container {
		position: relative;
		max-width: 800px;
		margin: 0 auto;
	}

	.timeline-container::before {
		content: '';
		position: absolute;
		top: 0;
		bottom: 0;
		left: 120px;
		width: 2px;
		background-color: var(--theme-accent-secondary);
	}

	.timeline-item {
		display: flex;
		margin-bottom: 1.5rem;
		position: relative;
	}

	.timeline-year {
		width: 100px;
		font-weight: bold;
		text-align: right;
		padding-right: 20px;
		flex-shrink: 0;
		color: var(--theme-accent-primary);
	}

	.timeline-content {
		flex-grow: 1;
		padding-left: 40px;
		position: relative;
		color: var(--theme-text-primary);
	}

	.timeline-content::before {
		content: '';
		position: absolute;
		left: -6px;
		top: 5px;
		width: 14px;
		height: 14px;
		border-radius: 50%;
		background-color: var(--theme-accent-primary);
	}

	.achievements-list {
		padding-left: 1.5rem;
	}

	.achievements-list li {
		margin-bottom: 0.8rem;
		line-height: 1.5;
		color: var(--theme-text-primary);
	}

	.fan-club {
		background-color: var(--theme-bg-secondary);
		color: var(--theme-text-primary);
		padding: 2rem;
		border-radius: 8px;
		border: 1px solid var(--theme-border);
	}

	.join-cta {
		margin-top: 1.5rem;
		text-align: center;
	}

	.btn {
		display: inline-block;
		padding: 0.8rem 1.5rem;
		border-radius: 4px;
		text-decoration: none;
		font-weight: bold;
		transition: all 0.3s ease;
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
	}

	.btn.primary:hover {
		background-color: var(--theme-accent-primary-hover);
	}

	@media (max-width: 768px) {
		.timeline-container::before {
			left: 60px;
		}

		.timeline-year {
			width: 50px;
		}

		.timeline-content {
			padding-left: 30px;
		}
	}
</style>
