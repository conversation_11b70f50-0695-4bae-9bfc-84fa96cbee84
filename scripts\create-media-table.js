import Database from 'better-sqlite3';
import * as dotenv from 'dotenv';
import * as path from 'path';
import * as fs from 'fs';

// Load environment variables
dotenv.config();

// Get database URL from environment variables
const databaseUrl = process.env.DATABASE_URL || 'local.db';

// Create database directory if it doesn't exist
const dbDir = path.dirname(databaseUrl);
if (dbDir !== '.' && !fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Connect to the database
console.log('Connecting to database:', databaseUrl);
const db = new Database(databaseUrl);

// Check if media table exists
const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='media'").get();

if (!tableExists) {
  console.log('Creating media table...');
  db.exec(`
    CREATE TABLE IF NOT EXISTS media (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      filename TEXT NOT NULL,
      original_name TEXT NOT NULL,
      path TEXT NOT NULL,
      thumbnail_path TEXT,
      type TEXT NOT NULL,
      mime_type TEXT NOT NULL,
      size INTEGER NOT NULL,
      width INTEGER,
      height INTEGER,
      alt TEXT,
      caption TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      author_id INTEGER,
      FOREIGN KEY (author_id) REFERENCES users(id)
    )
  `);
  console.log('Media table created successfully!');
} else {
  console.log('Media table already exists.');
}

// List all tables in the database
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
console.log('Tables in database:', tables.map(t => t.name).join(', '));

// Close the database connection
db.close();
console.log('Database connection closed.');
