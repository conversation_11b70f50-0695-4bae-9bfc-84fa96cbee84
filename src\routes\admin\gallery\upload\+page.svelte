<script>
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';

	// State variables
	let files = [];
	let uploading = false;
	let uploadProgress = 0;
	let uploadedFiles = [];
	let error = null;

	// Form data
	let title = '';
	let description = '';
	let category = 'tv'; // Default category
	let published = false;

	// Available categories
	const categories = [
		{ id: 'tv', name: 'TV Shows' },
		{ id: 'movies', name: 'Movies' },
		{ id: 'music', name: 'Music' },
		{ id: 'events', name: 'Events' },
		{ id: 'photoshoots', name: 'Photoshoots' },
		{ id: 'directing', name: 'Directing' }
	];

	// Handle file selection
	function handleFileSelect(event) {
		const fileList = event.target.files;
		if (fileList.length > 0) {
			files = Array.from(fileList);
			error = null;
		}
	}

	// Handle file drop
	function handleDrop(event) {
		event.preventDefault();
		const fileList = event.dataTransfer.files;
		if (fileList.length > 0) {
			files = Array.from(fileList);
			error = null;
		}
	}

	// Prevent default drag behavior
	function handleDragOver(event) {
		event.preventDefault();
	}

	// Upload a single file
	async function uploadFile(file) {
		const formData = new FormData();
		formData.append('file', file);
		formData.append('category', 'gallery');

		try {
			const response = await fetch('/api/upload', {
				method: 'POST',
				body: formData
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Upload failed');
			}

			return await response.json();
		} catch (err) {
			console.error('Error uploading file:', err);
			throw err;
		}
	}

	// Create a gallery item with the uploaded image
	async function createGalleryItem(imageData) {
		try {
			const response = await fetch('/api/gallery', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					title,
					description,
					imageUrl: imageData.imageUrl,
					thumbnailUrl: imageData.thumbnailUrl,
					category,
					published
				})
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to create gallery item');
			}

			return await response.json();
		} catch (err) {
			console.error('Error creating gallery item:', err);
			throw err;
		}
	}

	// Handle form submission
	async function handleSubmit(event) {
		event.preventDefault();

		if (files.length === 0) {
			error = 'Please select a file to upload';
			return;
		}

		if (!title) {
			error = 'Please enter a title';
			return;
		}

		try {
			uploading = true;
			error = null;
			uploadedFiles = [];

			// Upload each file and create gallery items
			for (let i = 0; i < files.length; i++) {
				const file = files[i];

				// Update progress
				uploadProgress = Math.round((i / files.length) * 100);

				// Upload the file
				const uploadResult = await uploadFile(file);

				// Create a gallery item with the uploaded image
				const galleryResult = await createGalleryItem(uploadResult.data);

				// Add to uploaded files
				uploadedFiles.push({
					...uploadResult.data,
					galleryId: galleryResult.data.id
				});
			}

			// Complete
			uploadProgress = 100;

			// Reset form after successful upload
			title = '';
			description = '';
			files = [];

			// Redirect to gallery admin after a short delay
			setTimeout(() => {
				goto('/admin/gallery');
			}, 2000);
		} catch (err) {
			error = err.message || 'Upload failed';
		} finally {
			uploading = false;
		}
	}
</script>

<svelte:head>
	<title>Upload Gallery Images - Admin</title>
</svelte:head>

<div class="upload-container">
	<h1>Upload Gallery Images</h1>

	{#if error}
		<div class="error-message">
			<p>{error}</p>
		</div>
	{/if}

	{#if uploadedFiles.length > 0}
		<div class="success-message">
			<p>Successfully uploaded {uploadedFiles.length} {uploadedFiles.length === 1 ? 'image' : 'images'}!</p>
			<div class="uploaded-previews">
				{#each uploadedFiles as file}
					<div class="uploaded-item">
						<img src={file.thumbnailUrl} alt={file.originalName} />
						<p>{file.originalName}</p>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<form on:submit={handleSubmit}>
		<div class="form-group">
			<label for="title">Title *</label>
			<input
				type="text"
				id="title"
				bind:value={title}
				required
				disabled={uploading}
			/>
		</div>

		<div class="form-group">
			<label for="description">Description</label>
			<textarea
				id="description"
				bind:value={description}
				rows="4"
				disabled={uploading}
			></textarea>
		</div>

		<div class="form-group">
			<label for="category">Category</label>
			<select id="category" bind:value={category} disabled={uploading}>
				{#each categories as cat}
					<option value={cat.id}>{cat.name}</option>
				{/each}
			</select>
		</div>

		<div class="form-group checkbox">
			<label>
				<input type="checkbox" bind:checked={published} disabled={uploading} />
				Publish immediately
			</label>
		</div>

		<div
			class="file-drop-area"
			on:drop={handleDrop}
			on:dragover={handleDragOver}
			role="region"
			aria-label="Image upload drop zone"
		>
			<input
				type="file"
				id="file-input"
				on:change={handleFileSelect}
				accept="image/*"
				multiple
				disabled={uploading}
			/>
			<label for="file-input" class="file-label">
				{#if files.length > 0}
					{files.length} {files.length === 1 ? 'file' : 'files'} selected
				{:else}
					Drag & drop images here or click to browse
				{/if}
			</label>

			{#if files.length > 0}
				<div class="file-list">
					{#each files as file}
						<div class="file-item">
							<span class="file-name">{file.name}</span>
							<span class="file-size">({Math.round(file.size / 1024)} KB)</span>
						</div>
					{/each}
				</div>
			{/if}
		</div>

		{#if uploading}
			<div class="progress-bar">
				<div class="progress" style="width: {uploadProgress}%"></div>
				<span class="progress-text">{uploadProgress}%</span>
			</div>
		{/if}

		<div class="form-actions">
			<button type="submit" class="btn primary" disabled={uploading}>
				{#if uploading}
					Uploading...
				{:else}
					Upload Images
				{/if}
			</button>
			<a href="/admin/gallery" class="btn secondary" class:disabled={uploading}>Cancel</a>
		</div>
	</form>
</div>

<style>
	.upload-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem;
	}

	h1 {
		margin-bottom: 2rem;
	}

	.form-group {
		margin-bottom: 1.5rem;
	}

	label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: bold;
	}

	input[type="text"],
	textarea,
	select {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
	}

	.checkbox {
		display: flex;
		align-items: center;
	}

	.checkbox label {
		display: flex;
		align-items: center;
		font-weight: normal;
	}

	.checkbox input {
		margin-right: 0.5rem;
	}

	.file-drop-area {
		border: 2px dashed #ddd;
		border-radius: 4px;
		padding: 2rem;
		text-align: center;
		margin-bottom: 1.5rem;
		position: relative;
	}

	.file-drop-area input[type="file"] {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		opacity: 0;
		cursor: pointer;
	}

	.file-label {
		font-size: 1.1rem;
		color: #666;
	}

	.file-list {
		margin-top: 1rem;
		text-align: left;
	}

	.file-item {
		padding: 0.5rem;
		background-color: #f5f5f5;
		border-radius: 4px;
		margin-bottom: 0.5rem;
	}

	.file-size {
		color: #666;
		font-size: 0.9rem;
	}

	.progress-bar {
		height: 20px;
		background-color: #f0f0f0;
		border-radius: 10px;
		margin-bottom: 1.5rem;
		overflow: hidden;
		position: relative;
	}

	.progress {
		height: 100%;
		background-color: #4caf50;
		transition: width 0.3s ease;
	}

	.progress-text {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		text-align: center;
		line-height: 20px;
		font-size: 0.8rem;
		color: #fff;
		text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
	}

	.form-actions {
		display: flex;
		gap: 1rem;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	.primary {
		background-color: #4caf50;
		color: white;
	}

	.secondary {
		background-color: #f0f0f0;
		color: #333;
	}

	.disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.error-message {
		background-color: #ffebee;
		color: #c62828;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
	}

	.success-message {
		background-color: #e8f5e9;
		color: #2e7d32;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
	}

	.uploaded-previews {
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;
		margin-top: 1rem;
	}

	.uploaded-item {
		width: 100px;
		text-align: center;
	}

	.uploaded-item img {
		width: 100%;
		height: 100px;
		object-fit: cover;
		border-radius: 4px;
	}

	.uploaded-item p {
		font-size: 0.8rem;
		margin-top: 0.5rem;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
</style>
