# Manual Debugging Steps for Reactivity Error

## Test Sequence to Reproduce `effect_update_depth_exceeded` Error

### 1. Initial Page Load
- [ ] Open http://localhost:5173/admin/simulate-interactions
- [ ] Check debug panel for initial logs
- [ ] Verify no errors during page load
- [ ] Note: Look for any rapid successive calls in debug log

### 2. Content Type Switching Test
- [ ] Change content type from "News Articles" to "Gallery Items"
- [ ] Watch debug panel for:
  - `BIND: selectedContentType changed`
  - `FUNCTION: handleContentTypeChange - Called`
  - `FUNCTION: loadContentItems - Starting`
  - `STATE: contentItems getter called`
- [ ] Change back to "News Articles"
- [ ] Repeat switching 3-5 times rapidly
- [ ] Look for any infinite loops or excessive calls

### 3. Template Selection Test
- [ ] Select different interaction templates
- [ ] Watch for `BIND: selectedTemplate changed` logs
- [ ] Check if template selection triggers any unexpected reactive updates

### 4. User Selection Test
- [ ] Click individual user checkboxes
- [ ] Use "Select All" / "Deselect All" button
- [ ] Watch for `FUNCTION: toggleUserSelection` and `FUNCTION: selectAllUsers` logs
- [ ] Check if user selection triggers content reloads

### 5. Content Item Selection Test
- [ ] Select different content items from dropdown
- [ ] Watch for:
  - `BIND: selectedContentId changed`
  - `REACTIVE: selectedContentItem computed`
- [ ] Check if this triggers any loops

### 6. Form Field Changes Test
- [ ] Type in custom message textarea
- [ ] Change max interactions number
- [ ] Set scheduled date/time
- [ ] Toggle "Generate variations" checkbox
- [ ] Watch for corresponding BIND logs

### 7. Rapid Interaction Test
- [ ] Quickly switch between content types multiple times
- [ ] Rapidly select/deselect users
- [ ] Quickly change content items
- [ ] Look for any error messages or infinite loops

## What to Look For in Debug Panel

### Normal Behavior
- Each action should trigger 1-2 log entries
- No rapid successive identical calls
- Clear sequence: BIND → FUNCTION → STATE updates

### Error Indicators
- Rapid successive identical log entries (>10 in quick succession)
- `REACTIVE ERROR DETECTED` messages
- `GLOBAL ERROR` entries
- Missing completion logs for functions
- Unexpected state getter calls

### Specific Patterns to Watch
1. **Content Type Change Loop:**
   ```
   BIND: selectedContentType changed
   FUNCTION: handleContentTypeChange - Called
   FUNCTION: loadContentItems - Starting
   STATE: contentItems getter called
   REACTIVE: selectedContentItem computed
   [REPEAT RAPIDLY]
   ```

2. **Reactive Statement Loop:**
   ```
   REACTIVE: selectedContentItem - Starting execution
   STATE: selectedContentId getter called
   STATE: contentItems getter called
   [REPEAT RAPIDLY]
   ```

3. **Binding Loop:**
   ```
   BIND: selectedContentType changed
   BIND: selectedContentType changed
   BIND: selectedContentType changed
   [REPEAT RAPIDLY]
   ```

## Expected Error Messages
- `effect_update_depth_exceeded`
- `Maximum update depth exceeded`
- `Svelte limits the number of nested updates`

## Debugging Commands
Open browser console and run:
```javascript
// Check current state
console.log('Debug log length:', window.debugLog?.length);

// Monitor reactive calls
let reactiveCallCount = 0;
const originalLog = console.log;
console.log = function(...args) {
  if (args[0]?.includes('REACTIVE:')) {
    reactiveCallCount++;
    if (reactiveCallCount > 20) {
      console.error('POTENTIAL INFINITE LOOP DETECTED');
    }
  }
  originalLog.apply(console, args);
};

// Reset counter
reactiveCallCount = 0;
```

## Recovery Steps if Error Occurs
1. Note the exact sequence of actions that triggered the error
2. Check browser console for full stack trace
3. Review debug panel for the last 20 log entries before error
4. Clear debug log and try to reproduce with minimal steps
5. Document the exact trigger sequence

## Success Criteria
- All interactions work without triggering infinite loops
- Debug panel shows clean, predictable log patterns
- No `effect_update_depth_exceeded` errors occur
- Page remains responsive during all interactions
