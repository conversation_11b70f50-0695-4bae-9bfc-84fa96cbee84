import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON>and<PERSON> } from './$types';
import fs from 'fs';
import path from 'path';

// GET /api/admin/uploads - List uploaded files (admin only)
export const GET: RequestHandler = async ({ locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    const UPLOAD_DIR = path.resolve('static/uploads/gallery');

    // Check if directory exists
    if (!fs.existsSync(UPLOAD_DIR)) {
      return json({
        success: true,
        files: []
      });
    }

    // Get all files in the gallery directory (excluding thumbnails)
    const files = fs.readdirSync(UPLOAD_DIR)
      .filter(file => {
        const filePath = path.join(UPLOAD_DIR, file);
        const stat = fs.statSync(filePath);
        return stat.isFile() && !file.startsWith('.') && file !== 'thumbnails';
      })
      .map(filename => {
        const filePath = path.join(UPLOAD_DIR, filename);
        const stat = fs.statSync(filePath);
        
        return {
          name: filename,
          size: stat.size,
          mtime: stat.mtime.toISOString(),
          path: `/uploads/gallery/${filename}`
        };
      })
      .sort((a, b) => new Date(b.mtime).getTime() - new Date(a.mtime).getTime());

    return json({
      success: true,
      files
    });
  } catch (error) {
    console.error('Error listing uploaded files:', error);
    return json({
      success: false,
      error: 'Failed to list uploaded files'
    }, { status: 500 });
  }
};
