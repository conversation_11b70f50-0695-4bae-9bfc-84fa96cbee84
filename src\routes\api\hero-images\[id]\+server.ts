import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { heroImages } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/hero-images/[id] - Get a specific hero image
export const GET: RequestHandler = async ({ params }) => {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }
    
    const item = await db.select()
      .from(heroImages)
      .where(eq(heroImages.id, id))
      .limit(1);
    
    if (item.length === 0) {
      return json({
        success: false,
        error: 'Hero image not found'
      }, { status: 404 });
    }
    
    return json({
      success: true,
      data: item[0]
    });
  } catch (error) {
    console.error('Error fetching hero image:', error);
    return json({
      success: false,
      error: 'Failed to fetch hero image'
    }, { status: 500 });
  }
};

// PUT /api/hero-images/[id] - Update a hero image (admin only)
export const PUT: RequestHandler = async ({ params, request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }
    
    const body = await request.json();
    
    // Check if hero image exists
    const existingItem = await db.select()
      .from(heroImages)
      .where(eq(heroImages.id, id))
      .limit(1);
    
    if (existingItem.length === 0) {
      return json({
        success: false,
        error: 'Hero image not found'
      }, { status: 404 });
    }
    
    // If this hero image is being set as active, deactivate all others
    if (body.active && !existingItem[0].active) {
      await db.update(heroImages)
        .set({ active: false })
        .where(eq(heroImages.active, true));
    }
    
    // Update the hero image
    const result = await db.update(heroImages)
      .set({
        title: body.title,
        subtitle: body.subtitle,
        imageUrl: body.imageUrl,
        active: body.active,
        sortOrder: body.sortOrder,
        updatedAt: new Date().toISOString()
      })
      .where(eq(heroImages.id, id))
      .returning();
    
    return json({
      success: true,
      data: result[0]
    });
  } catch (error) {
    console.error('Error updating hero image:', error);
    return json({
      success: false,
      error: 'Failed to update hero image'
    }, { status: 500 });
  }
};

// DELETE /api/hero-images/[id] - Delete a hero image (admin only)
export const DELETE: RequestHandler = async ({ params, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }
    
    // Check if hero image exists
    const existingItem = await db.select()
      .from(heroImages)
      .where(eq(heroImages.id, id))
      .limit(1);
    
    if (existingItem.length === 0) {
      return json({
        success: false,
        error: 'Hero image not found'
      }, { status: 404 });
    }
    
    // Delete the hero image
    await db.delete(heroImages)
      .where(eq(heroImages.id, id));
    
    return json({
      success: true,
      message: 'Hero image deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting hero image:', error);
    return json({
      success: false,
      error: 'Failed to delete hero image'
    }, { status: 500 });
  }
};
