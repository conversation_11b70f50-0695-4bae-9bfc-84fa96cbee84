/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {
      colors: {
        // Background colors
        bg: {
          primary: 'var(--color-bg-primary)',
          secondary: 'var(--color-bg-secondary)',
          tertiary: 'var(--color-bg-tertiary)',
          quaternary: 'var(--color-bg-quaternary)',
          overlay: 'var(--color-bg-overlay)',
        },
        
        // Text colors
        text: {
          primary: 'var(--color-text-primary)',
          secondary: 'var(--color-text-secondary)',
          tertiary: 'var(--color-text-tertiary)',
          muted: 'var(--color-text-muted)',
          inverse: 'var(--color-text-inverse)',
        },
        
        // Border colors
        border: {
          primary: 'var(--color-border-primary)',
          secondary: 'var(--color-border-secondary)',
          tertiary: 'var(--color-border-tertiary)',
          focus: 'var(--color-border-focus)',
          error: 'var(--color-border-error)',
        },
        
        // Surface colors
        surface: {
          primary: 'var(--color-surface-primary)',
          secondary: 'var(--color-surface-secondary)',
          tertiary: 'var(--color-surface-tertiary)',
          elevated: 'var(--color-surface-elevated)',
        },
        
        // Interactive colors
        interactive: {
          primary: 'var(--color-interactive-primary)',
          'primary-hover': 'var(--color-interactive-primary-hover)',
          'primary-active': 'var(--color-interactive-primary-active)',
          secondary: 'var(--color-interactive-secondary)',
          'secondary-hover': 'var(--color-interactive-secondary-hover)',
        },
        
        // Status colors
        success: {
          DEFAULT: 'var(--color-success)',
          bg: 'var(--color-success-bg)',
          border: 'var(--color-success-border)',
        },
        warning: {
          DEFAULT: 'var(--color-warning)',
          bg: 'var(--color-warning-bg)',
          border: 'var(--color-warning-border)',
        },
        error: {
          DEFAULT: 'var(--color-error)',
          bg: 'var(--color-error-bg)',
          border: 'var(--color-error-border)',
        },
        info: {
          DEFAULT: 'var(--color-info)',
          bg: 'var(--color-info-bg)',
          border: 'var(--color-info-border)',
        },
        
        // Component-specific semantic colors
        button: {
          'primary-bg': 'var(--color-button-primary-bg)',
          'primary-text': 'var(--color-button-primary-text)',
          'primary-border': 'var(--color-button-primary-border)',
          'primary-hover-bg': 'var(--color-button-primary-hover-bg)',
          'primary-hover-border': 'var(--color-button-primary-hover-border)',
          'secondary-bg': 'var(--color-button-secondary-bg)',
          'secondary-text': 'var(--color-button-secondary-text)',
          'secondary-border': 'var(--color-button-secondary-border)',
          'secondary-hover-bg': 'var(--color-button-secondary-hover-bg)',
          'secondary-hover-text': 'var(--color-button-secondary-hover-text)',
        },
        
        input: {
          bg: 'var(--color-input-bg)',
          text: 'var(--color-input-text)',
          border: 'var(--color-input-border)',
          'border-focus': 'var(--color-input-border-focus)',
          placeholder: 'var(--color-input-placeholder)',
        },
        
        card: {
          bg: 'var(--color-card-bg)',
          border: 'var(--color-card-border)',
        },
        
        nav: {
          bg: 'var(--color-nav-bg)',
          text: 'var(--color-nav-text)',
          'text-hover': 'var(--color-nav-text-hover)',
          border: 'var(--color-nav-border)',
        },
        
        modal: {
          bg: 'var(--color-modal-bg)',
          backdrop: 'var(--color-modal-backdrop)',
          border: 'var(--color-modal-border)',
        },
        
        tooltip: {
          bg: 'var(--color-tooltip-bg)',
          text: 'var(--color-tooltip-text)',
          border: 'var(--color-tooltip-border)',
        },
        
        dropdown: {
          bg: 'var(--color-dropdown-bg)',
          border: 'var(--color-dropdown-border)',
          'item-hover': 'var(--color-dropdown-item-hover)',
        },
      },
      
      spacing: {
        xs: 'var(--space-xs)',
        sm: 'var(--space-sm)',
        md: 'var(--space-md)',
        lg: 'var(--space-lg)',
        xl: 'var(--space-xl)',
        '2xl': 'var(--space-2xl)',
        '3xl': 'var(--space-3xl)',
      },
      
      fontSize: {
        xs: 'var(--font-size-xs)',
        sm: 'var(--font-size-sm)',
        base: 'var(--font-size-md)',
        lg: 'var(--font-size-lg)',
        xl: 'var(--font-size-xl)',
        '2xl': 'var(--font-size-2xl)',
        '3xl': 'var(--font-size-3xl)',
      },
      
      fontWeight: {
        light: 'var(--font-weight-light)',
        normal: 'var(--font-weight-normal)',
        medium: 'var(--font-weight-medium)',
        semibold: 'var(--font-weight-semibold)',
        bold: 'var(--font-weight-bold)',
      },
      
      lineHeight: {
        tight: 'var(--line-height-tight)',
        normal: 'var(--line-height-normal)',
        relaxed: 'var(--line-height-relaxed)',
      },
      
      borderRadius: {
        sm: 'var(--border-radius-sm)',
        DEFAULT: 'var(--border-radius-md)',
        md: 'var(--border-radius-md)',
        lg: 'var(--border-radius-lg)',
        xl: 'var(--border-radius-xl)',
        full: 'var(--border-radius-full)',
      },
      
      borderWidth: {
        thin: 'var(--border-width-thin)',
        DEFAULT: 'var(--border-width-thin)',
        medium: 'var(--border-width-medium)',
        thick: 'var(--border-width-thick)',
      },
      
      boxShadow: {
        sm: 'var(--shadow-sm)',
        DEFAULT: 'var(--shadow-md)',
        md: 'var(--shadow-md)',
        lg: 'var(--shadow-lg)',
        xl: 'var(--shadow-xl)',
        'primary': '0 0 0 3px var(--color-shadow-primary)',
        'secondary': '0 0 0 3px var(--color-shadow-secondary)',
        'hover': '0 0 0 3px var(--color-shadow-hover)',
        'focus': '0 0 0 3px var(--color-shadow-focus)',
      },
      
      transitionDuration: {
        fast: 'var(--transition-fast)',
        DEFAULT: 'var(--transition-normal)',
        normal: 'var(--transition-normal)',
        slow: 'var(--transition-slow)',
      },
      
      zIndex: {
        dropdown: 'var(--z-dropdown)',
        sticky: 'var(--z-sticky)',
        fixed: 'var(--z-fixed)',
        'modal-backdrop': 'var(--z-modal-backdrop)',
        modal: 'var(--z-modal)',
        popover: 'var(--z-popover)',
        tooltip: 'var(--z-tooltip)',
        toast: 'var(--z-toast)',
      },
    },
  },
  plugins: [],
}
