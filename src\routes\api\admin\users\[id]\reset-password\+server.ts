import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHand<PERSON> } from './$types';
import logger from '$lib/server/services/logger';
import crypto from 'crypto';
import { hashPassword } from '$lib/server/auth';

// Simple password reset token storage (in production, use Redis or database)
const resetTokens = new Map<string, { userId: number; expires: number; email: string }>();

// Generate password reset token
function generateResetToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

// Generate secure temporary password
function generateTempPassword(): string {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789!@#$%&*';
  let password = '';
  
  // Ensure at least one of each type
  password += 'ABCDEFGHJKMNPQRSTUVWXYZ'[Math.floor(Math.random() * 23)]; // Uppercase
  password += 'abcdefghijkmnpqrstuvwxyz'[Math.floor(Math.random() * 23)]; // Lowercase
  password += '23456789'[Math.floor(Math.random() * 8)]; // Number
  password += '!@#$%&*'[Math.floor(Math.random() * 7)]; // Special
  
  // Fill the rest
  for (let i = 4; i < 12; i++) {
    password += chars[Math.floor(Math.random() * chars.length)];
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

// POST /api/admin/users/[id]/reset-password - Generate password reset link
export const POST: RequestHandler = async ({ params, request, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || (locals.user.role !== 'admin' && locals.user.role !== 'moderator')) {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const userId = parseInt(params.id);
    if (isNaN(userId)) {
      return json({
        success: false,
        error: 'Invalid user ID'
      }, { status: 400 });
    }

    const body = await request.json();
    const resetType = body.type || 'link'; // 'link' or 'temp'

    // Get user details
    const user = await db.select({
      id: users.id,
      username: users.username,
      displayName: users.displayName,
      email: users.email,
      role: users.role
    }).from(users).where(eq(users.id, userId)).limit(1);

    if (user.length === 0) {
      return json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    const targetUser = user[0];

    // Prevent non-admin users from resetting admin passwords
    if (locals.user.role !== 'admin' && targetUser.role === 'admin') {
      return json({
        success: false,
        error: 'Insufficient privileges to reset admin passwords'
      }, { status: 403 });
    }

    if (resetType === 'temp') {
      // Generate temporary password and update user
      const tempPassword = generateTempPassword();
      const hashedPassword = hashPassword(tempPassword);

      await db.update(users)
        .set({ 
          passwordHash: hashedPassword,
          updatedAt: new Date().toISOString()
        })
        .where(eq(users.id, userId));

      // Log admin action
      logger.info('Temporary password generated by admin', {
        adminUser: locals.user.username,
        targetUser: targetUser.username,
        userId
      });

      return json({
        success: true,
        data: {
          type: 'temporary',
          tempPassword,
          message: 'Temporary password generated successfully'
        }
      });
    } else {
      // Generate reset token and link
      const token = generateResetToken();
      const expires = Date.now() + (24 * 60 * 60 * 1000); // 24 hours

      // Store token
      resetTokens.set(token, {
        userId,
        expires,
        email: targetUser.email
      });

      // Generate reset link
      const resetLink = `${request.headers.get('origin')}/reset-password?token=${token}`;

      // Log admin action
      logger.info('Password reset link generated by admin', {
        adminUser: locals.user.username,
        targetUser: targetUser.username,
        userId,
        resetLink
      });

      // In development, log the reset link
      console.log(`\n🔗 Password Reset Link for ${targetUser.email}:`);
      console.log(`${resetLink}\n`);

      return json({
        success: true,
        data: {
          type: 'link',
          resetLink,
          token,
          expires: new Date(expires).toISOString(),
          message: 'Password reset link generated successfully',
          // In development, include the link for testing
          ...(process.env.NODE_ENV === 'development' && {
            resetLink
          })
        }
      });
    }
  } catch (error) {
    logger.error('Error generating password reset:', error);
    return json({
      success: false,
      error: 'Failed to generate password reset'
    }, { status: 500 });
  }
};

// GET /api/admin/users/[id]/reset-password - Get reset token status
export const GET: RequestHandler = async ({ params, url, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || (locals.user.role !== 'admin' && locals.user.role !== 'moderator')) {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const token = url.searchParams.get('token');
    
    if (!token) {
      return json({
        success: false,
        error: 'Reset token is required'
      }, { status: 400 });
    }

    // Check if token exists and is valid
    const tokenData = resetTokens.get(token);
    
    if (!tokenData) {
      return json({
        success: false,
        error: 'Invalid or expired reset token'
      }, { status: 400 });
    }

    // Check if token has expired
    if (Date.now() > tokenData.expires) {
      resetTokens.delete(token);
      return json({
        success: false,
        error: 'Reset token has expired'
      }, { status: 400 });
    }

    return json({
      success: true,
      data: {
        valid: true,
        userId: tokenData.userId,
        email: tokenData.email,
        expires: new Date(tokenData.expires).toISOString()
      }
    });
  } catch (error) {
    logger.error('Error checking reset token:', error);
    return json({
      success: false,
      error: 'Failed to check reset token'
    }, { status: 500 });
  }
};

// DELETE /api/admin/users/[id]/reset-password - Cancel reset token
export const DELETE: RequestHandler = async ({ request, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || (locals.user.role !== 'admin' && locals.user.role !== 'moderator')) {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const body = await request.json();
    const token = body.token;

    if (!token) {
      return json({
        success: false,
        error: 'Reset token is required'
      }, { status: 400 });
    }

    // Remove the token
    const tokenData = resetTokens.get(token);
    if (tokenData) {
      resetTokens.delete(token);
      
      logger.info('Password reset token cancelled by admin', {
        adminUser: locals.user.username,
        userId: tokenData.userId,
        token
      });
    }

    return json({
      success: true,
      message: 'Reset token cancelled successfully'
    });
  } catch (error) {
    logger.error('Error cancelling reset token:', error);
    return json({
      success: false,
      error: 'Failed to cancel reset token'
    }, { status: 500 });
  }
};

// Cleanup expired tokens periodically
setInterval(() => {
  const now = Date.now();
  for (const [token, data] of resetTokens.entries()) {
    if (now > data.expires) {
      resetTokens.delete(token);
    }
  }
}, 60 * 60 * 1000); // Clean up every hour
