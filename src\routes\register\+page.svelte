<script lang="ts">
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { debounce } from '$lib/utils/validation';
	import { 
		validateRegistrationForm, 
		validateUsername, 
		validateEmail, 
		validateDisplayName, 
		validatePassword, 
		validatePasswordConfirmation,
		type RegistrationFormData,
		type FormValidationErrors
	} from '$lib/utils/validation';
	import PasswordStrengthIndicator from '$lib/components/PasswordStrengthIndicator.svelte';
	import ProfilePictureSelector from '$lib/components/ProfilePictureSelector.svelte';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';

	// Form data
	let formData: RegistrationFormData = {
		username: '',
		displayName: '',
		email: '',
		password: '',
		confirmPassword: '',
		acceptTerms: false,
		acceptPrivacy: false
	};

	// Form state
	let errors: FormValidationErrors = {};
	let isLoading = false;
	let submitError = '';
	let submitSuccess = false;
	let profilePictureUrl = '';

	// CAPTCHA
	let captchaQuestion = '';
	let captchaAnswer = '';
	let captchaExpected = 0;

	// Real-time validation flags
	let touchedFields: Set<string> = new Set();

	// Generate CAPTCHA
	function generateCaptcha() {
		const num1 = Math.floor(Math.random() * 10) + 1;
		const num2 = Math.floor(Math.random() * 10) + 1;
		captchaQuestion = `${num1} + ${num2} = ?`;
		captchaExpected = num1 + num2;
	}

	// Mark field as touched
	function markFieldTouched(fieldName: string) {
		touchedFields.add(fieldName);
		touchedFields = touchedFields; // Trigger reactivity
	}

	// Real-time validation functions
	const debouncedValidateUsername = debounce((username: string) => {
		if (touchedFields.has('username')) {
			const result = validateUsername(username);
			if (!result.isValid) {
				errors.username = result.error;
			} else {
				delete errors.username;
			}
			errors = errors; // Trigger reactivity
		}
	}, 300);

	const debouncedValidateEmail = debounce((email: string) => {
		if (touchedFields.has('email')) {
			const result = validateEmail(email);
			if (!result.isValid) {
				errors.email = result.error;
			} else {
				delete errors.email;
			}
			errors = errors; // Trigger reactivity
		}
	}, 300);

	const debouncedValidateDisplayName = debounce((displayName: string) => {
		if (touchedFields.has('displayName')) {
			const result = validateDisplayName(displayName);
			if (!result.isValid) {
				errors.displayName = result.error;
			} else {
				delete errors.displayName;
			}
			errors = errors; // Trigger reactivity
		}
	}, 300);

	const debouncedValidatePassword = debounce((password: string) => {
		if (touchedFields.has('password')) {
			const result = validatePassword(password);
			if (!result.isValid) {
				errors.password = result.error;
			} else {
				delete errors.password;
			}
			errors = errors; // Trigger reactivity
		}
	}, 300);

	const debouncedValidateConfirmPassword = debounce((password: string, confirmPassword: string) => {
		if (touchedFields.has('confirmPassword')) {
			const result = validatePasswordConfirmation(password, confirmPassword);
			if (!result.isValid) {
				errors.confirmPassword = result.error;
			} else {
				delete errors.confirmPassword;
			}
			errors = errors; // Trigger reactivity
		}
	}, 300);

	// Reactive validation
	$: debouncedValidateUsername(formData.username);
	$: debouncedValidateEmail(formData.email);
	$: debouncedValidateDisplayName(formData.displayName);
	$: debouncedValidatePassword(formData.password);
	$: debouncedValidateConfirmPassword(formData.password, formData.confirmPassword);

	// Handle profile picture selection
	function handleProfilePictureSelect(event: CustomEvent) {
		profilePictureUrl = event.detail.thumbnailUrl || event.detail.imageUrl || '';
	}

	// Handle form submission
	async function handleSubmit() {
		// Mark all fields as touched for validation
		touchedFields = new Set(['username', 'displayName', 'email', 'password', 'confirmPassword']);
		
		// Validate entire form
		const validation = validateRegistrationForm(formData);
		errors = validation.errors;

		if (!validation.isValid) {
			submitError = 'Please fix the errors above before submitting.';
			return;
		}

		// Validate CAPTCHA
		if (parseInt(captchaAnswer) !== captchaExpected) {
			submitError = 'CAPTCHA answer is incorrect. Please try again.';
			generateCaptcha();
			captchaAnswer = '';
			return;
		}

		isLoading = true;
		submitError = '';

		try {
			const response = await fetch('/api/auth/register', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					...formData,
					captchaAnswer: parseInt(captchaAnswer),
					captchaExpected,
					profilePictureUrl
				})
			});

			const data = await response.json();

			if (response.ok && data.success) {
				submitSuccess = true;
				// Redirect to login page after a short delay
				setTimeout(() => {
					goto('/login?registered=true');
				}, 2000);
			} else {
				submitError = data.error || 'Registration failed. Please try again.';
				// Regenerate CAPTCHA on error
				generateCaptcha();
				captchaAnswer = '';
			}
		} catch (error) {
			console.error('Registration error:', error);
			submitError = 'An error occurred during registration. Please try again.';
			generateCaptcha();
			captchaAnswer = '';
		} finally {
			isLoading = false;
		}
	}

	// Initialize component
	onMount(() => {
		generateCaptcha();
	});
</script>

<svelte:head>
	<title>Register - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Join the Finn Wolfhard Fan Club community" />
</svelte:head>

<div class="register-container">
	<div class="register-card">
		<header class="register-header">
			<h1>Join the FWFC Community</h1>
			<p class="register-subtitle">
				Create your account to connect with fellow Finn Wolfhard fans
			</p>
		</header>

		{#if submitSuccess}
			<div class="success-message" role="alert" aria-live="polite">
				<h2>Registration Successful! 🎉</h2>
				<p>Welcome to the Finn Wolfhard Fan Club! You'll be redirected to the login page shortly.</p>
			</div>
		{:else}
			{#if submitError}
				<ErrorMessage 
					title="Registration Error"
					message={submitError}
					type="error"
					dismissible={true}
					onDismiss={() => submitError = ''}
				/>
			{/if}

			<form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} novalidate>
				<!-- Profile Picture Selection -->
				<ProfilePictureSelector 
					bind:selectedImageUrl={profilePictureUrl}
					disabled={isLoading}
					on:select={handleProfilePictureSelect}
				/>

				<!-- Username Field -->
				<div class="form-group">
					<label for="username" class="required">Username</label>
					<input
						type="text"
						id="username"
						bind:value={formData.username}
						onblur={() => markFieldTouched('username')}
						oninput={() => markFieldTouched('username')}
						placeholder="Choose a unique username"
						disabled={isLoading}
						class:error={errors.username}
						aria-describedby={errors.username ? 'username-error' : undefined}
						aria-invalid={!!errors.username}
						autocomplete="username"
						required
					/>
					{#if errors.username}
						<div id="username-error" class="field-error" role="alert">
							{errors.username}
						</div>
					{/if}
					<div class="field-help">
						3-20 characters, letters, numbers, underscores, and hyphens only
					</div>
				</div>

				<!-- Display Name Field -->
				<div class="form-group">
					<label for="displayName" class="required">Display Name</label>
					<input
						type="text"
						id="displayName"
						bind:value={formData.displayName}
						onblur={() => markFieldTouched('displayName')}
						oninput={() => markFieldTouched('displayName')}
						placeholder="Your display name"
						disabled={isLoading}
						class:error={errors.displayName}
						aria-describedby={errors.displayName ? 'displayName-error' : undefined}
						aria-invalid={!!errors.displayName}
						autocomplete="name"
						required
					/>
					{#if errors.displayName}
						<div id="displayName-error" class="field-error" role="alert">
							{errors.displayName}
						</div>
					{/if}
					<div class="field-help">
						This is how your name will appear to other members
					</div>
				</div>

				<!-- Email Field -->
				<div class="form-group">
					<label for="email" class="required">Email Address</label>
					<input
						type="email"
						id="email"
						bind:value={formData.email}
						onblur={() => markFieldTouched('email')}
						oninput={() => markFieldTouched('email')}
						placeholder="<EMAIL>"
						disabled={isLoading}
						class:error={errors.email}
						aria-describedby={errors.email ? 'email-error' : undefined}
						aria-invalid={!!errors.email}
						autocomplete="email"
						required
					/>
					{#if errors.email}
						<div id="email-error" class="field-error" role="alert">
							{errors.email}
						</div>
					{/if}
					<div class="field-help">
						We'll use this to send you important updates (we won't spam you!)
					</div>
				</div>

				<!-- Password Field -->
				<div class="form-group">
					<label for="password" class="required">Password</label>
					<input
						type="password"
						id="password"
						bind:value={formData.password}
						onblur={() => markFieldTouched('password')}
						oninput={() => markFieldTouched('password')}
						placeholder="Create a strong password"
						disabled={isLoading}
						class:error={errors.password}
						aria-describedby={errors.password ? 'password-error' : 'password-strength'}
						aria-invalid={!!errors.password}
						autocomplete="new-password"
						required
					/>
					{#if errors.password}
						<div id="password-error" class="field-error" role="alert">
							{errors.password}
						</div>
					{/if}
					<div id="password-strength">
						<PasswordStrengthIndicator
							password={formData.password}
							showRequirements={true}
							showFeedback={true}
						/>
					</div>
				</div>

				<!-- Confirm Password Field -->
				<div class="form-group">
					<label for="confirmPassword" class="required">Confirm Password</label>
					<input
						type="password"
						id="confirmPassword"
						bind:value={formData.confirmPassword}
						onblur={() => markFieldTouched('confirmPassword')}
						oninput={() => markFieldTouched('confirmPassword')}
						placeholder="Confirm your password"
						disabled={isLoading}
						class:error={errors.confirmPassword}
						aria-describedby={errors.confirmPassword ? 'confirmPassword-error' : undefined}
						aria-invalid={!!errors.confirmPassword}
						autocomplete="new-password"
						required
					/>
					{#if errors.confirmPassword}
						<div id="confirmPassword-error" class="field-error" role="alert">
							{errors.confirmPassword}
						</div>
					{/if}
				</div>

				<!-- CAPTCHA -->
				<div class="form-group">
					<label for="captcha" class="required">Security Check</label>
					<div class="captcha-container">
						<div class="captcha-question">
							<span class="captcha-text">{captchaQuestion}</span>
						</div>
						<input
							type="number"
							id="captcha"
							bind:value={captchaAnswer}
							placeholder="Enter answer"
							disabled={isLoading}
							required
							aria-label="CAPTCHA answer: {captchaQuestion}"
							class="captcha-input"
						/>
						<button
							type="button"
							class="captcha-refresh"
							onclick={generateCaptcha}
							disabled={isLoading}
							aria-label="Generate new CAPTCHA question"
						>
							🔄
						</button>
					</div>
					<div class="field-help">
						Please solve this simple math problem to verify you're human
					</div>
				</div>

				<!-- Terms and Privacy -->
				<div class="form-group checkbox-group">
					<div class="checkbox-item">
						<input
							type="checkbox"
							id="acceptTerms"
							bind:checked={formData.acceptTerms}
							disabled={isLoading}
							class:error={errors.acceptTerms}
							aria-describedby={errors.acceptTerms ? 'terms-error' : undefined}
							aria-invalid={!!errors.acceptTerms}
							required
						/>
						<label for="acceptTerms" class="checkbox-label">
							I accept the <a href="/terms" target="_blank" rel="noopener noreferrer">Terms of Service</a>
							<span class="required-indicator">*</span>
						</label>
					</div>
					{#if errors.acceptTerms}
						<div id="terms-error" class="field-error" role="alert">
							{errors.acceptTerms}
						</div>
					{/if}

					<div class="checkbox-item">
						<input
							type="checkbox"
							id="acceptPrivacy"
							bind:checked={formData.acceptPrivacy}
							disabled={isLoading}
							class:error={errors.acceptPrivacy}
							aria-describedby={errors.acceptPrivacy ? 'privacy-error' : undefined}
							aria-invalid={!!errors.acceptPrivacy}
							required
						/>
						<label for="acceptPrivacy" class="checkbox-label">
							I accept the <a href="/privacy" target="_blank" rel="noopener noreferrer">Privacy Policy</a>
							<span class="required-indicator">*</span>
						</label>
					</div>
					{#if errors.acceptPrivacy}
						<div id="privacy-error" class="field-error" role="alert">
							{errors.acceptPrivacy}
						</div>
					{/if}
				</div>

				<!-- Submit Button -->
				<div class="form-actions">
					<button
						type="submit"
						class="btn primary submit-btn"
						disabled={isLoading}
						aria-describedby="submit-help"
					>
						{#if isLoading}
							<LoadingSpinner size="small" inline={true} message="Creating Account..." />
						{:else}
							Create Account
						{/if}
					</button>
					<div id="submit-help" class="submit-help">
						By creating an account, you agree to our terms and privacy policy
					</div>
				</div>
			</form>

			<!-- Login Link -->
			<div class="login-prompt">
				<p>Already have an account? <a href="/login">Sign in here</a></p>
			</div>
		{/if}
	</div>

	<!-- Accessibility Information -->
	<div class="accessibility-info">
		<h2>Accessibility Features</h2>
		<p>Our registration form is designed to be accessible to all users:</p>
		<ul>
			<li><strong>Screen Reader Support:</strong> All form fields have proper labels and descriptions</li>
			<li><strong>Keyboard Navigation:</strong> Use Tab to navigate between fields</li>
			<li><strong>Real-time Validation:</strong> Get immediate feedback as you type</li>
			<li><strong>High Contrast Mode:</strong> Available in the footer accessibility controls</li>
			<li><strong>Large Text Mode:</strong> Increase text size for better readability</li>
		</ul>
		<p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
	</div>
</div>

<style>
	.register-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem 1rem;
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	.register-card {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 12px;
		box-shadow: 0 4px 6px var(--theme-shadow);
		padding: 2.5rem;
		color: var(--theme-text-primary);
	}

	.register-header {
		text-align: center;
		margin-bottom: 2rem;
	}

	.register-header h1 {
		margin: 0 0 0.5rem 0;
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--theme-text-primary);
		background: linear-gradient(135deg, var(--theme-accent-primary), var(--theme-accent-secondary, #4075a6));
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}

	.register-subtitle {
		margin: 0;
		font-size: 1.1rem;
		color: var(--theme-text-secondary);
		line-height: 1.5;
	}

	.success-message {
		text-align: center;
		padding: 3rem 2rem;
		background: linear-gradient(135deg, var(--theme-accent-success, #28a745), #20c997);
		color: white;
		border-radius: 12px;
		margin-bottom: 2rem;
	}

	.success-message h2 {
		margin: 0 0 1rem 0;
		font-size: 2rem;
		font-weight: 600;
	}

	.success-message p {
		margin: 0;
		font-size: 1.1rem;
		opacity: 0.9;
	}

	/* Form Styles */
	.form-group {
		margin-bottom: 1.5rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 600;
		color: var(--theme-text-primary);
		font-size: 1rem;
	}

	.form-group label.required::after {
		content: ' *';
		color: var(--theme-accent-danger, #dc3545);
		font-weight: bold;
	}

	.required-indicator {
		color: var(--theme-accent-danger, #dc3545);
		font-weight: bold;
	}

	.form-group input[type="text"],
	.form-group input[type="email"],
	.form-group input[type="password"],
	.form-group input[type="number"] {
		width: 100%;
		padding: 0.875rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 8px;
		font-size: 1rem;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		transition: all 0.2s ease;
		box-sizing: border-box;
	}

	.form-group input:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
		box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
	}

	.form-group input.error {
		border-color: var(--theme-accent-danger, #dc3545);
		box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
	}

	.form-group input:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		background-color: var(--theme-bg-tertiary);
	}

	.field-error {
		margin-top: 0.5rem;
		color: var(--theme-accent-danger, #dc3545);
		font-size: 0.875rem;
		font-weight: 500;
		display: flex;
		align-items: center;
		gap: 0.25rem;
	}

	.field-error::before {
		content: '⚠️';
		font-size: 0.75rem;
	}

	.field-help {
		margin-top: 0.5rem;
		font-size: 0.875rem;
		color: var(--theme-text-muted);
		line-height: 1.4;
	}

	/* CAPTCHA Styles */
	.captcha-container {
		display: flex;
		align-items: center;
		gap: 1rem;
		margin-top: 0.5rem;
	}

	.captcha-question {
		background-color: var(--theme-bg-secondary);
		border: 2px solid var(--theme-border);
		border-radius: 8px;
		padding: 0.875rem 1rem;
		font-weight: 600;
		font-size: 1.1rem;
		color: var(--theme-text-primary);
		min-width: 100px;
		text-align: center;
	}

	.captcha-input {
		flex: 1;
		max-width: 120px;
	}

	.captcha-refresh {
		background-color: var(--theme-button-bg);
		border: 2px solid var(--theme-border);
		border-radius: 8px;
		padding: 0.875rem;
		cursor: pointer;
		font-size: 1.2rem;
		color: var(--theme-text-primary);
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		min-width: 48px;
		height: 48px;
	}

	.captcha-refresh:hover:not(:disabled) {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
		transform: rotate(90deg);
	}

	.captcha-refresh:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	/* Checkbox Styles */
	.checkbox-group {
		border: 1px solid var(--theme-border-light);
		border-radius: 8px;
		padding: 1.5rem;
		background-color: var(--theme-bg-secondary);
	}

	.checkbox-item {
		display: flex;
		align-items: flex-start;
		gap: 0.75rem;
		margin-bottom: 1rem;
	}

	.checkbox-item:last-child {
		margin-bottom: 0;
	}

	.checkbox-item input[type="checkbox"] {
		margin: 0;
		width: 18px;
		height: 18px;
		flex-shrink: 0;
		margin-top: 0.125rem;
		accent-color: var(--theme-accent-primary);
	}

	.checkbox-item input[type="checkbox"]:focus {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
	}

	.checkbox-item input[type="checkbox"].error {
		outline: 2px solid var(--theme-accent-danger, #dc3545);
		outline-offset: 1px;
	}

	.checkbox-label {
		font-size: 0.95rem;
		line-height: 1.5;
		color: var(--theme-text-primary);
		cursor: pointer;
		flex: 1;
	}

	.checkbox-label a {
		color: var(--theme-accent-primary);
		text-decoration: none;
		font-weight: 600;
	}

	.checkbox-label a:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	/* Button Styles */
	.form-actions {
		margin-top: 2rem;
		text-align: center;
	}

	.btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		padding: 1rem 2rem;
		border: 2px solid var(--theme-border);
		border-radius: 8px;
		font-size: 1.1rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
		min-height: 48px;
		box-sizing: border-box;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border-color: var(--theme-accent-primary);
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
	}

	.submit-btn {
		min-width: 200px;
	}

	.submit-help {
		margin-top: 1rem;
		font-size: 0.875rem;
		color: var(--theme-text-muted);
		line-height: 1.4;
	}

	/* Login Prompt */
	.login-prompt {
		text-align: center;
		margin-top: 2rem;
		padding-top: 2rem;
		border-top: 1px solid var(--theme-border);
	}

	.login-prompt p {
		margin: 0;
		color: var(--theme-text-secondary);
	}

	.login-prompt a {
		color: var(--theme-accent-primary);
		font-weight: 600;
		text-decoration: none;
	}

	.login-prompt a:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	/* Accessibility Info */
	.accessibility-info {
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		padding: 2rem;
		border-radius: 12px;
		color: var(--theme-text-primary);
	}

	.accessibility-info h2 {
		margin-top: 0;
		margin-bottom: 1rem;
		font-size: 1.5rem;
		color: var(--theme-text-primary);
	}

	.accessibility-info ul {
		margin-bottom: 1rem;
		padding-left: 1.5rem;
	}

	.accessibility-info li {
		margin-bottom: 0.5rem;
		color: var(--theme-text-secondary);
		line-height: 1.5;
	}

	.accessibility-info a {
		color: var(--theme-accent-primary);
		text-decoration: none;
	}

	.accessibility-info a:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	/* Responsive Design */
	@media (min-width: 768px) {
		.register-container {
			flex-direction: row;
			align-items: flex-start;
		}

		.register-card {
			flex: 2;
		}

		.accessibility-info {
			flex: 1;
			max-width: 400px;
		}
	}

	@media (max-width: 640px) {
		.register-container {
			padding: 1rem;
		}

		.register-card {
			padding: 1.5rem;
		}

		.register-header h1 {
			font-size: 2rem;
		}

		.captcha-container {
			flex-direction: column;
			align-items: stretch;
		}

		.captcha-input {
			max-width: none;
		}

		.btn {
			padding: 0.875rem 1.5rem;
			font-size: 1rem;
		}
	}

	/* High Contrast Mode */
	:global(.high-contrast) .register-card {
		border-width: 3px;
	}

	:global(.high-contrast) .form-group input {
		border-width: 3px;
	}

	:global(.high-contrast) .form-group input:focus {
		box-shadow: 0 0 0 4px var(--theme-accent-primary);
	}

	:global(.high-contrast) .checkbox-item input[type="checkbox"] {
		width: 20px;
		height: 20px;
	}

	:global(.high-contrast) .btn {
		border-width: 3px;
	}

	/* Large Text Mode */
	:global(.large-text) .register-header h1 {
		font-size: 3rem;
	}

	:global(.large-text) .form-group input {
		padding: 1rem;
		font-size: 1.2rem;
	}

	:global(.large-text) .btn {
		padding: 1.25rem 2.5rem;
		font-size: 1.3rem;
	}

	/* Simplified Interface */
	:global(.simplified-interface) .accessibility-info {
		display: none;
	}

	:global(.simplified-interface) .register-header {
		margin-bottom: 1.5rem;
	}

	:global(.simplified-interface) .field-help {
		display: none;
	}

	/* Focus Management */
	.form-group input:focus,
	.checkbox-item input:focus,
	.btn:focus {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
	}

	/* Animation for form submission */
	@keyframes submitPulse {
		0% { transform: scale(1); }
		50% { transform: scale(1.05); }
		100% { transform: scale(1); }
	}

	.btn.primary:active:not(:disabled) {
		animation: submitPulse 0.2s ease;
	}

	/* Loading state styles */
	.btn:disabled {
		position: relative;
	}

	/* Error state animations */
	@keyframes shake {
		0%, 100% { transform: translateX(0); }
		25% { transform: translateX(-5px); }
		75% { transform: translateX(5px); }
	}

	.form-group input.error {
		animation: shake 0.3s ease-in-out;
	}
</style>
