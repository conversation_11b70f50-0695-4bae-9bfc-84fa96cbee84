<script lang="ts">
	export let message = 'Loading...';
	export let size: 'small' | 'medium' | 'large' = 'medium';
	export let inline = false;
</script>

<div 
	class="loading-spinner" 
	class:inline 
	class:small={size === 'small'}
	class:medium={size === 'medium'}
	class:large={size === 'large'}
	role="status" 
	aria-live="polite"
>
	<div class="spinner" aria-hidden="true"></div>
	<span class="loading-text">{message}</span>
</div>

<style>
	.loading-spinner {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 1rem;
		padding: 2rem;
		text-align: center;
	}

	.loading-spinner.inline {
		flex-direction: row;
		padding: 0.5rem;
		gap: 0.5rem;
	}

	.spinner {
		border: 3px solid var(--color-border-secondary);
		border-top: 3px solid var(--color-interactive-primary);
		border-radius: var(--border-radius-full);
		animation: spin 1s linear infinite;
	}

	.small .spinner {
		width: 20px;
		height: 20px;
		border-width: 2px;
	}

	.medium .spinner {
		width: 40px;
		height: 40px;
		border-width: 3px;
	}

	.large .spinner {
		width: 60px;
		height: 60px;
		border-width: 4px;
	}

	.loading-text {
		color: var(--color-text-secondary);
		font-size: var(--font-size-md);
	}

	.small .loading-text {
		font-size: 0.875rem;
	}

	.large .loading-text {
		font-size: 1.125rem;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	/* Respect reduced motion preferences */
	@media (prefers-reduced-motion: reduce) {
		.spinner {
			animation: none;
			border-top-color: var(--theme-accent-primary);
		}
	}
</style>
