<script>
	import { goto } from '$app/navigation';
	import { api } from '$lib/utils/api';
	import logger from '$lib/client/logger';
	import ImageSelector from '$lib/components/admin/ImageSelector.svelte';
	import RichTextEditor from '$lib/components/admin/RichTextEditor.svelte';

	// Create a logger with context
	const log = logger.withContext('news-create');

	// Form state
	let article = {
		title: '',
		content: '',
		imageUrl: '',
		published: false
	};

	let loading = false;
	/** @type {string|null} */
	let error = null;

	/**
	 * Handle image selection from ImageSelector
	 */
	function handleImageSelect(event) {
		const { imageUrl } = event.detail;
		article.imageUrl = imageUrl;
	}

	/**
	 * Create the news article
	 */
	async function createArticle() {
		if (!article.title || !article.content) {
			error = 'Title and content are required';
			return;
		}

		loading = true;
		error = null;

		log.info('Creating new news article', { title: article.title });
		const { success, data, error: apiError } = await api.post('/api/news', article);

		if (success && data) {
			log.info('News article created successfully', data);
			// Redirect to the news management page
			goto('/admin/news');
		} else {
			log.error('Failed to create news article', apiError);
			error = apiError || 'Failed to create news article';
			loading = false;
		}
	}

	/**
	 * Handle content change for rich text editor
	 */
	function handleContentChange(event) {
		article.content = event.detail.content;
	}
</script>

<svelte:head>
	<title>Create News Article - Admin</title>
</svelte:head>

<div class="create-news-container">
	<div class="header">
		<h1>Create News Article</h1>
		<a href="/admin/news" class="btn secondary">← Back to News</a>
	</div>

	{#if error}
		<div class="error-message">
			<p>{error}</p>
			<button class="btn secondary" on:click={() => error = null}>Dismiss</button>
		</div>
	{/if}

	<form on:submit|preventDefault={createArticle} class="article-form">
		<div class="form-group">
			<label for="title">Title *</label>
			<input
				type="text"
				id="title"
				bind:value={article.title}
				required
				placeholder="Enter article title"
				disabled={loading}
			/>
		</div>

		<div class="form-group">
			<label>Featured Image</label>
			<ImageSelector
				selectedImageUrl={article.imageUrl}
				on:select={handleImageSelect}
				disabled={loading}
			/>
		</div>

		<div class="form-group">
			<label for="content">Content *</label>
			<RichTextEditor
				bind:value={article.content}
				placeholder="Write your article content here..."
				disabled={loading}
				height={400}
				on:change={handleContentChange}
			/>
		</div>

		<div class="form-group checkbox">
			<label>
				<input
					type="checkbox"
					bind:checked={article.published}
					disabled={loading}
				/>
				Publish immediately
			</label>
		</div>

		<div class="form-actions">
			<button type="submit" class="btn primary" disabled={loading}>
				{loading ? 'Creating...' : 'Create Article'}
			</button>
			<a href="/admin/news" class="btn secondary">Cancel</a>
		</div>
	</form>
</div>

<style>
	.create-news-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border: 1px solid var(--theme-accent-primary);
	}

	.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
	}

	.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border: 1px solid var(--theme-border);
	}

	.secondary:hover:not(:disabled) {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.error-message {
		background-color: var(--theme-accent-danger);
		color: white;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border: 1px solid var(--theme-accent-danger);
	}

	.article-form {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		padding: 2rem;
		border-radius: 8px;
		color: var(--theme-text-primary);
	}

	.form-group {
		margin-bottom: 1.5rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: bold;
		color: var(--theme-text-primary);
	}

	.form-group input,
	.form-group textarea {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid var(--theme-input-border);
		border-radius: 4px;
		font-size: 1rem;
		font-family: inherit;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
	}

	.form-group input:focus,
	.form-group textarea:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
		box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
	}

	.form-group textarea {
		resize: vertical;
		min-height: 300px;
	}

	.form-group.checkbox label {
		display: flex;
		align-items: center;
		font-weight: normal;
	}

	.form-group.checkbox input {
		width: auto;
		margin-right: 0.5rem;
	}

	.help-text {
		font-size: 0.9rem;
		color: var(--theme-text-secondary);
		margin-top: 0.5rem;
		margin-bottom: 0;
	}

	.form-actions {
		display: flex;
		gap: 1rem;
		margin-top: 2rem;
	}
</style>
