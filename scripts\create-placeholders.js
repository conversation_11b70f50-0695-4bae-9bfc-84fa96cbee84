// Script to create placeholder images for development
import fs from 'fs';
import path from 'path';

// List of image files to create
const imagesToCreate = [
  'community.jpg',
  'finn-profile.jpg',
  'hero-bg.jpg',
  // Gallery thumbnails
  ...Array.from({ length: 8 }, (_, i) => `gallery-${i + 1}-thumb.jpg`),
  // Gallery full images
  ...Array.from({ length: 8 }, (_, i) => `gallery-${i + 1}.jpg`),
  // News images
  ...Array.from({ length: 4 }, (_, i) => `news-${i + 1}.jpg`),
  // Featured news images
  ...Array.from({ length: 3 }, (_, i) => `featured-news-${i + 1}.jpg`),
];

// Path to the images directory
const imagesDir = path.resolve('static/images');

// Create placeholder content
const placeholderContent = 'PLACEHOLDER';

// Create each placeholder image
imagesToCreate.forEach(imageName => {
  const imagePath = path.join(imagesDir, imageName);
  
  // Check if the file already exists
  if (!fs.existsSync(imagePath)) {
    try {
      // Write the placeholder content to the file
      fs.writeFileSync(imagePath, placeholderContent);
      console.log(`Created placeholder: ${imageName}`);
    } catch (error) {
      console.error(`Error creating ${imageName}:`, error);
    }
  } else {
    console.log(`File already exists: ${imageName}`);
  }
});

console.log('Placeholder creation complete!');
