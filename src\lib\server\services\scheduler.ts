import { db } from '$lib/server/db';
import { scheduledContent, users, news, gallery, comments, messages, contentAuthorship } from '$lib/server/db/schema';
import { eq, and, lte } from 'drizzle-orm';
import logger from './logger';

class ContentScheduler {
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private readonly checkInterval = 60000; // Check every minute

  start() {
    if (this.isRunning) {
      logger.warn('Content scheduler is already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting content scheduler');

    // Run immediately, then at intervals
    this.processScheduledContent();
    this.intervalId = setInterval(() => {
      this.processScheduledContent();
    }, this.checkInterval);
  }

  stop() {
    if (!this.isRunning) {
      logger.warn('Content scheduler is not running');
      return;
    }

    this.isRunning = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    logger.info('Content scheduler stopped');
  }

  async processScheduledContent() {
    try {
      const now = new Date().toISOString();
      
      // Get all pending scheduled content that should be published now
      const itemsToPublish = await db.select()
        .from(scheduledContent)
        .where(
          and(
            eq(scheduledContent.status, 'pending'),
            lte(scheduledContent.scheduledFor, now)
          )
        );

      if (itemsToPublish.length === 0) {
        return; // Nothing to process
      }

      logger.info(`Processing ${itemsToPublish.length} scheduled content items`);

      for (const item of itemsToPublish) {
        await this.publishScheduledItem(item);
      }

    } catch (error) {
      logger.error('Error processing scheduled content:', error);
    }
  }

  private async publishScheduledItem(item: any) {
    try {
      // Parse content data
      let contentData: any;
      try {
        contentData = JSON.parse(item.contentData);
      } catch (error) {
        await this.markItemAsFailed(item.id, 'Invalid content data format');
        return;
      }

      // Check if target user exists
      const targetUser = await db.select()
        .from(users)
        .where(eq(users.id, item.asUserId))
        .limit(1);

      if (targetUser.length === 0) {
        await this.markItemAsFailed(item.id, 'Target user not found');
        return;
      }

      let contentId: number;
      let result: any;

      // Publish content based on type
      switch (item.contentType) {
        case 'news':
          result = await db.insert(news).values({
            title: contentData.title,
            content: contentData.content,
            imageUrl: contentData.imageUrl || null,
            authorId: item.asUserId,
            published: true
          }).returning();
          contentId = result[0].id;
          break;

        case 'gallery':
          result = await db.insert(gallery).values({
            title: contentData.title,
            description: contentData.description || null,
            imageUrl: contentData.imageUrl,
            thumbnailUrl: contentData.thumbnailUrl || contentData.imageUrl,
            authorId: item.asUserId,
            published: true
          }).returning();
          contentId = result[0].id;
          break;

        case 'comment':
          result = await db.insert(comments).values({
            userId: item.asUserId,
            content: contentData.content,
            itemType: contentData.itemType as 'news' | 'gallery',
            itemId: contentData.itemId,
            approved: true
          }).returning();
          contentId = result[0].id;
          break;

        case 'message':
          result = await db.insert(messages).values({
            userId: item.asUserId,
            content: contentData.content,
            approved: true
          }).returning();
          contentId = result[0].id;
          break;

        default:
          await this.markItemAsFailed(item.id, `Unsupported content type: ${item.contentType}`);
          return;
      }

      // Record content authorship
      await db.insert(contentAuthorship).values({
        contentType: item.contentType,
        contentId,
        actualAuthorId: item.createdByUserId,
        displayAuthorId: item.asUserId,
        isSimulated: true
      });

      // Update scheduled content status
      await db.update(scheduledContent)
        .set({
          status: 'published',
          publishedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })
        .where(eq(scheduledContent.id, item.id));

      // Update user's last active time
      await db.update(users)
        .set({ lastActiveAt: new Date().toISOString() })
        .where(eq(users.id, item.asUserId));

      logger.info('Scheduled content published', {
        scheduledItemId: item.id,
        contentType: item.contentType,
        contentId,
        targetUser: targetUser[0].username,
        scheduledFor: item.scheduledFor
      });

    } catch (error) {
      logger.error(`Error publishing scheduled item ${item.id}:`, error);
      await this.markItemAsFailed(
        item.id, 
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  private async markItemAsFailed(itemId: number, errorMessage: string) {
    try {
      await db.update(scheduledContent)
        .set({
          status: 'failed',
          errorMessage,
          updatedAt: new Date().toISOString()
        })
        .where(eq(scheduledContent.id, itemId));

      logger.error(`Marked scheduled item ${itemId} as failed: ${errorMessage}`);
    } catch (error) {
      logger.error(`Error marking item ${itemId} as failed:`, error);
    }
  }

  // Get scheduler status
  getStatus() {
    return {
      isRunning: this.isRunning,
      checkInterval: this.checkInterval
    };
  }
}

// Create singleton instance
export const contentScheduler = new ContentScheduler();

// Auto-start in production
if (process.env.NODE_ENV === 'production') {
  contentScheduler.start();
}

export default contentScheduler;
