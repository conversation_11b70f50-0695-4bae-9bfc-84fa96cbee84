{"name": "fwfc2", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "admin": "node admin-server.js", "dev:all": "concurrently \"npm run dev\" \"npm run admin\"", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:unit": "vitest run src/tests/admin/", "test:integration": "vitest run src/tests/api/", "test:e2e": "vitest run src/tests/e2e/"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.0", "@types/better-sqlite3": "^7.6.12", "@types/node": "^22", "@vitest/coverage-v8": "^3.2.4", "concurrently": "^9.1.2", "drizzle-kit": "^0.30.2", "jsdom": "^26.1.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "ts-node": "^10.9.2", "typescript": "^5.0.0", "vite": "^6.2.6", "vitest": "^3.2.4"}, "dependencies": {"@adminjs/express": "^6.1.1", "@adminjs/sql": "^2.2.6", "@fontsource/fira-mono": "^5.2.6", "@rollup/rollup-win32-x64-msvc": "^4.40.2", "@tinymce/tinymce-svelte": "^3.1.0", "adminjs": "^7.8.15", "better-sqlite3": "^11.10.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.40.0", "express": "^5.1.0", "express-formidable": "^1.2.0", "express-session": "^1.18.1", "sharp": "^0.34.1", "winston": "^3.17.0"}}