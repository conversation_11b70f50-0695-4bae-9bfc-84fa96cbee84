import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export type Theme = 'light' | 'dark';

// Create the theme store
function createThemeStore() {
	// Default to light theme
	const defaultTheme: Theme = 'light';
	
	// Initialize with default theme
	const { subscribe, set, update } = writable<Theme>(defaultTheme);

	return {
		subscribe,
		
		/**
		 * Initialize theme from localStorage or default
		 */
		init: () => {
			if (browser) {
				const stored = localStorage.getItem('fwfc-theme') as Theme;
				const theme = stored && (stored === 'light' || stored === 'dark') ? stored : defaultTheme;
				set(theme);
				applyTheme(theme);
			}
		},
		
		/**
		 * Set theme and persist to localStorage
		 */
		setTheme: (theme: Theme) => {
			if (browser) {
				localStorage.setItem('fwfc-theme', theme);
				applyTheme(theme);
			}
			set(theme);
		},
		
		/**
		 * Toggle between light and dark themes
		 */
		toggle: () => {
			update(currentTheme => {
				const newTheme = currentTheme === 'light' ? 'dark' : 'light';
				if (browser) {
					localStorage.setItem('fwfc-theme', newTheme);
					applyTheme(newTheme);
				}
				return newTheme;
			});
		}
	};
}

/**
 * Apply theme to document
 */
function applyTheme(theme: Theme) {
	if (browser && document.documentElement) {
		if (theme === 'dark') {
			document.documentElement.setAttribute('data-theme', 'dark');
		} else {
			document.documentElement.removeAttribute('data-theme');
		}
	}
}

/**
 * Get system theme preference
 */
export function getSystemTheme(): Theme {
	if (browser && window.matchMedia) {
		return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
	}
	return 'light';
}

/**
 * Listen for system theme changes
 */
export function watchSystemTheme(callback: (theme: Theme) => void) {
	if (browser && window.matchMedia) {
		const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
		
		const handler = (e: MediaQueryListEvent) => {
			callback(e.matches ? 'dark' : 'light');
		};
		
		mediaQuery.addEventListener('change', handler);
		
		// Return cleanup function
		return () => mediaQuery.removeEventListener('change', handler);
	}
	
	return () => {}; // No-op cleanup for non-browser environments
}

// Export the theme store
export const theme = createThemeStore();
