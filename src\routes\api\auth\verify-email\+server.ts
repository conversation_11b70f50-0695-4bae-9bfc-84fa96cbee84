import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';
import crypto from 'crypto';

// Simple email verification token storage (in production, use Redis or database)
const verificationTokens = new Map<string, { email: string; expires: number }>();

// Generate verification token
function generateVerificationToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

// POST /api/auth/verify-email - Send verification email
export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json();
    
    if (!body.email) {
      return json({
        success: false,
        error: 'Email address is required'
      }, { status: 400 });
    }

    // Check if user exists
    const user = await db.select()
      .from(users)
      .where(eq(users.email, body.email))
      .limit(1);

    if (user.length === 0) {
      return json({
        success: false,
        error: 'No account found with this email address'
      }, { status: 404 });
    }

    // Generate verification token
    const token = generateVerificationToken();
    const expires = Date.now() + (24 * 60 * 60 * 1000); // 24 hours

    // Store token (in production, store in database)
    verificationTokens.set(token, {
      email: body.email,
      expires
    });

    // In a real application, you would send an email here
    // For demo purposes, we'll just log the verification link
    const verificationLink = `${request.headers.get('origin')}/verify-email?token=${token}`;
    
    logger.info('Email verification requested', {
      email: body.email,
      verificationLink,
      token
    });

    console.log(`\n🔗 Email Verification Link for ${body.email}:`);
    console.log(`${verificationLink}\n`);

    return json({
      success: true,
      message: 'Verification email sent! Check your email for the verification link.',
      // In development, include the link for testing
      ...(process.env.NODE_ENV === 'development' && {
        verificationLink
      })
    });
  } catch (error) {
    logger.error('Error sending verification email:', error);
    return json({
      success: false,
      error: 'Failed to send verification email'
    }, { status: 500 });
  }
};

// GET /api/auth/verify-email - Verify email with token
export const GET: RequestHandler = async ({ url }) => {
  try {
    const token = url.searchParams.get('token');
    
    if (!token) {
      return json({
        success: false,
        error: 'Verification token is required'
      }, { status: 400 });
    }

    // Check if token exists and is valid
    const tokenData = verificationTokens.get(token);
    
    if (!tokenData) {
      return json({
        success: false,
        error: 'Invalid or expired verification token'
      }, { status: 400 });
    }

    // Check if token has expired
    if (Date.now() > tokenData.expires) {
      verificationTokens.delete(token);
      return json({
        success: false,
        error: 'Verification token has expired'
      }, { status: 400 });
    }

    // Update user's email verification status
    // Note: In this simple implementation, we don't have an emailVerified field
    // In production, you would add this field to the users table
    
    // Remove the used token
    verificationTokens.delete(token);

    logger.info('Email verified successfully', {
      email: tokenData.email,
      token
    });

    return json({
      success: true,
      message: 'Email verified successfully!',
      email: tokenData.email
    });
  } catch (error) {
    logger.error('Error verifying email:', error);
    return json({
      success: false,
      error: 'Failed to verify email'
    }, { status: 500 });
  }
};

// DELETE /api/auth/verify-email - Resend verification email
export const DELETE: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json();
    
    if (!body.token) {
      return json({
        success: false,
        error: 'Token is required'
      }, { status: 400 });
    }

    // Remove the token
    const tokenData = verificationTokens.get(body.token);
    if (tokenData) {
      verificationTokens.delete(body.token);
      
      logger.info('Verification token cancelled', {
        email: tokenData.email,
        token: body.token
      });
    }

    return json({
      success: true,
      message: 'Verification token cancelled'
    });
  } catch (error) {
    logger.error('Error cancelling verification token:', error);
    return json({
      success: false,
      error: 'Failed to cancel verification token'
    }, { status: 500 });
  }
};

// Cleanup expired tokens periodically (in production, use a proper job scheduler)
setInterval(() => {
  const now = Date.now();
  for (const [token, data] of verificationTokens.entries()) {
    if (now > data.expires) {
      verificationTokens.delete(token);
    }
  }
}, 60 * 60 * 1000); // Clean up every hour
