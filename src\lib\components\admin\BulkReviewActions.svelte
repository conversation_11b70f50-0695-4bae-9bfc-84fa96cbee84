<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import LoadingSpinner from '../LoadingSpinner.svelte';
	import ErrorMessage from '../ErrorMessage.svelte';
	import { api } from '$lib/utils/api';

	const dispatch = createEventDispatcher();

	// Props
	export let selectedReviews: number[] = [];
	export let disabled = false;

	// State
	let isProcessing = false;
	let error = '';
	let showConfirmDialog = false;
	let pendingAction = '';

	// Bulk action options
	const bulkActions = [
		{ value: 'approve', label: 'Approve Selected', icon: '✓', color: 'success' },
		{ value: 'reject', label: 'Reject Selected', icon: '✗', color: 'danger' },
		{ value: 'needs_revision', label: 'Mark as Needs Revision', icon: '✏️', color: 'info' },
		{ value: 'delete', label: 'Delete Selected', icon: '🗑️', color: 'danger' }
	];

	/**
	 * Handle bulk action selection
	 */
	function handleBulkAction(action: string) {
		if (selectedReviews.length === 0) {
			error = 'Please select at least one review';
			return;
		}

		pendingAction = action;
		showConfirmDialog = true;
	}

	/**
	 * Confirm and execute bulk action
	 */
	async function executeBulkAction() {
		if (!pendingAction || selectedReviews.length === 0) return;

		isProcessing = true;
		error = '';
		showConfirmDialog = false;

		try {
			const promises = selectedReviews.map(reviewId => {
				if (pendingAction === 'delete') {
					return api.delete(`/api/admin/ai-content-reviews/${reviewId}`);
				} else {
					return api.patch(`/api/admin/ai-content-reviews/${reviewId}`, {
						reviewStatus: pendingAction,
						reviewNotes: `Bulk action: ${pendingAction}`
					});
				}
			});

			const results = await Promise.allSettled(promises);
			
			// Check for failures
			const failures = results.filter(result => result.status === 'rejected');
			const successes = results.filter(result => result.status === 'fulfilled');

			if (failures.length > 0) {
				error = `${failures.length} operations failed. ${successes.length} succeeded.`;
			}

			// Dispatch success event
			dispatch('completed', {
				action: pendingAction,
				reviewIds: selectedReviews,
				successCount: successes.length,
				failureCount: failures.length
			});

			// Clear selection
			selectedReviews = [];
			pendingAction = '';

		} catch (err) {
			console.error('Error executing bulk action:', err);
			error = 'An error occurred while processing the bulk action';
		} finally {
			isProcessing = false;
		}
	}

	/**
	 * Cancel bulk action
	 */
	function cancelBulkAction() {
		showConfirmDialog = false;
		pendingAction = '';
	}

	/**
	 * Get action details
	 */
	function getActionDetails(action: string) {
		return bulkActions.find(a => a.value === action);
	}

	/**
	 * Export selected reviews
	 */
	async function exportReviews() {
		if (selectedReviews.length === 0) {
			error = 'Please select at least one review to export';
			return;
		}

		try {
			// Create CSV content
			const csvContent = await generateCSVContent();
			
			// Download CSV file
			const blob = new Blob([csvContent], { type: 'text/csv' });
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `ai-content-reviews-${new Date().toISOString().split('T')[0]}.csv`;
			document.body.appendChild(a);
			a.click();
			document.body.removeChild(a);
			window.URL.revokeObjectURL(url);

			dispatch('exported', { reviewIds: selectedReviews });
		} catch (err) {
			console.error('Error exporting reviews:', err);
			error = 'Failed to export reviews';
		}
	}

	/**
	 * Generate CSV content for export
	 */
	async function generateCSVContent(): Promise<string> {
		const headers = [
			'ID',
			'Content Type',
			'Original Prompt',
			'Authenticity Score',
			'Review Status',
			'Target User',
			'Created At',
			'Reviewed At'
		];

		let csvContent = headers.join(',') + '\n';

		for (const reviewId of selectedReviews) {
			try {
				const response = await api.get(`/api/admin/ai-content-reviews/${reviewId}`);
				if (response.success && response.data) {
					const review = response.data;
					const row = [
						review.id,
						review.contentType,
						`"${review.originalPrompt.replace(/"/g, '""')}"`,
						review.authenticityScore,
						review.reviewStatus,
						review.targetUser?.displayName || 'Unknown',
						review.createdAt,
						review.reviewedAt || ''
					];
					csvContent += row.join(',') + '\n';
				}
			} catch (err) {
				console.error(`Error fetching review ${reviewId}:`, err);
			}
		}

		return csvContent;
	}
</script>

{#if selectedReviews.length > 0}
	<div class="bulk-actions-bar" class:disabled>
		<div class="selection-info">
			<span class="selection-count">{selectedReviews.length}</span>
			{selectedReviews.length === 1 ? 'review' : 'reviews'} selected
		</div>

		{#if error}
			<ErrorMessage
				title="Error"
				message={error}
				type="error"
				dismissible={true}
				onDismiss={() => error = ''}
			/>
		{/if}

		<div class="bulk-actions">
			{#each bulkActions as action}
				<button
					class="bulk-action-btn {action.color}"
					onclick={() => handleBulkAction(action.value)}
					disabled={disabled || isProcessing}
					title={action.label}
				>
					<span class="action-icon" aria-hidden="true">{action.icon}</span>
					{action.label}
				</button>
			{/each}

			<button
				class="bulk-action-btn secondary"
				onclick={exportReviews}
				disabled={disabled || isProcessing}
				title="Export selected reviews to CSV"
			>
				<span class="action-icon" aria-hidden="true">📊</span>
				Export CSV
			</button>
		</div>

		{#if isProcessing}
			<div class="processing-indicator">
				<LoadingSpinner size="small" inline={true} message="Processing..." />
			</div>
		{/if}
	</div>
{/if}

<!-- Confirmation Dialog -->
{#if showConfirmDialog}
	<div class="modal-overlay" onclick={cancelBulkAction}>
		<div class="confirmation-dialog" onclick={(e) => e.stopPropagation()}>
			<div class="dialog-header">
				<h3>Confirm Bulk Action</h3>
			</div>

			<div class="dialog-body">
				{@const actionDetails = getActionDetails(pendingAction)}
				<p>
					Are you sure you want to <strong>{actionDetails?.label.toLowerCase()}</strong>
					<strong>{selectedReviews.length}</strong>
					{selectedReviews.length === 1 ? 'review' : 'reviews'}?
				</p>

				{#if pendingAction === 'delete'}
					<div class="warning-message">
						⚠️ This action cannot be undone. The reviews will be permanently deleted.
					</div>
				{/if}
			</div>

			<div class="dialog-actions">
				<button class="btn secondary" onclick={cancelBulkAction}>
					Cancel
				</button>
				<button 
					class="btn {actionDetails?.color || 'primary'}" 
					onclick={executeBulkAction}
				>
					{actionDetails?.icon} Confirm
				</button>
			</div>
		</div>
	</div>
{/if}

<style>
	.bulk-actions-bar {
		position: sticky;
		top: 0;
		z-index: 100;
		background: var(--theme-bg-primary);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		padding: 1rem;
		margin-bottom: 1.5rem;
		box-shadow: 0 2px 8px var(--theme-shadow);
		display: flex;
		align-items: center;
		gap: 1rem;
		flex-wrap: wrap;
	}

	.bulk-actions-bar.disabled {
		opacity: 0.6;
		pointer-events: none;
	}

	.selection-info {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-weight: 500;
		color: var(--theme-text-primary);
	}

	.selection-count {
		background: var(--theme-accent-primary);
		color: white;
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-weight: 600;
		font-size: 0.9rem;
	}

	.bulk-actions {
		display: flex;
		gap: 0.5rem;
		flex-wrap: wrap;
		margin-left: auto;
	}

	.bulk-action-btn {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem 1rem;
		border: none;
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		white-space: nowrap;
	}

	.bulk-action-btn.success {
		background: var(--theme-accent-success);
		color: white;
	}

	.bulk-action-btn.danger {
		background: var(--theme-accent-danger);
		color: white;
	}

	.bulk-action-btn.info {
		background: var(--theme-accent-info);
		color: white;
	}

	.bulk-action-btn.secondary {
		background: var(--theme-bg-tertiary);
		color: var(--theme-text-primary);
		border: 1px solid var(--theme-border);
	}

	.bulk-action-btn:hover:not(:disabled) {
		transform: translateY(-1px);
		box-shadow: 0 2px 8px var(--theme-shadow-hover);
	}

	.bulk-action-btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;
	}

	.action-icon {
		font-size: 1rem;
	}

	.processing-indicator {
		margin-left: auto;
	}

	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 1rem;
	}

	.confirmation-dialog {
		background: var(--theme-bg-primary);
		border-radius: 8px;
		max-width: 500px;
		width: 100%;
		box-shadow: 0 10px 30px var(--theme-shadow);
	}

	.dialog-header {
		padding: 1.5rem 1.5rem 0 1.5rem;
	}

	.dialog-header h3 {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1.25rem;
	}

	.dialog-body {
		padding: 1rem 1.5rem;
	}

	.dialog-body p {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		line-height: 1.5;
	}

	.warning-message {
		padding: 1rem;
		background: var(--theme-accent-warning);
		color: white;
		border-radius: 6px;
		font-weight: 500;
	}

	.dialog-actions {
		padding: 0 1.5rem 1.5rem 1.5rem;
		display: flex;
		gap: 1rem;
		justify-content: flex-end;
	}

	.btn {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.btn.secondary {
		background: var(--theme-bg-tertiary);
		color: var(--theme-text-primary);
		border: 1px solid var(--theme-border);
	}

	.btn.success {
		background: var(--theme-accent-success);
		color: white;
	}

	.btn.danger {
		background: var(--theme-accent-danger);
		color: white;
	}

	.btn.info {
		background: var(--theme-accent-info);
		color: white;
	}

	.btn:hover {
		transform: translateY(-1px);
		box-shadow: 0 2px 8px var(--theme-shadow-hover);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.bulk-actions-bar {
			flex-direction: column;
			align-items: stretch;
		}

		.bulk-actions {
			margin-left: 0;
			justify-content: stretch;
		}

		.bulk-action-btn {
			flex: 1;
			justify-content: center;
		}

		.dialog-actions {
			flex-direction: column;
		}
	}

	/* High Contrast Mode */
	@media (prefers-contrast: high) {
		.bulk-actions-bar,
		.confirmation-dialog {
			border: 2px solid currentColor;
		}

		.bulk-action-btn {
			border: 1px solid currentColor;
		}
	}

	/* Reduced Motion */
	@media (prefers-reduced-motion: reduce) {
		.bulk-action-btn,
		.btn {
			transition: none;
		}
	}
</style>
