import { json } from '@sveltejs/kit';
import logger from '$lib/server/services/logger';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    const errorData = await request.json();
    
    // Add user info if available
    const userContext = locals.user 
      ? { userId: locals.user.id, username: locals.user.username, role: locals.user.role }
      : { anonymous: true };
    
    // Log the client-side error
    logger.error(`Client Error: ${errorData.message}`, {
      ...errorData,
      user: userContext,
      source: 'client'
    });
    
    return json({ success: true });
  } catch (error) {
    logger.error('Error logging client error', { error });
    return json({ success: false }, { status: 500 });
  }
};
