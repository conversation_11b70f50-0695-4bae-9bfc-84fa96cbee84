import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { gallery } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/gallery - Get gallery items (published only for public, all for admin)
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Get query parameters
    const limit = Number(url.searchParams.get('limit') || '20');
    const offset = Number(url.searchParams.get('offset') || '0');
    const all = url.searchParams.get('all') === 'true';

    // Check if user is admin and requesting all items
    const isAdmin = locals.user && locals.user.role === 'admin';
    const showAll = all && isAdmin;

    // Build query
    let query = db.select().from(gallery);

    // If not admin or not requesting all, only show published items
    if (!showAll) {
      query = query.where(eq(gallery.published, true));
    }

    // Apply limit, offset, and ordering
    const items = await query
      .limit(limit)
      .offset(offset)
      .orderBy(gallery.createdAt);

    return json({
      success: true,
      data: items
    });
  } catch (error) {
    console.error('Error fetching gallery items:', error);
    return json({
      success: false,
      error: 'Failed to fetch gallery items'
    }, { status: 500 });
  }
};

// POST /api/gallery - Create a new gallery item (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    const body = await request.json();

    // Validate required fields
    if (!body.title || !body.imageUrl || !body.thumbnailUrl) {
      return json({
        success: false,
        error: 'Title, imageUrl, and thumbnailUrl are required'
      }, { status: 400 });
    }

    // Insert new gallery item into the database
    const result = await db.insert(gallery).values({
      title: body.title,
      description: body.description || null,
      imageUrl: body.imageUrl,
      thumbnailUrl: body.thumbnailUrl,
      authorId: locals.user.id,
      published: body.published || false
    }).returning();

    return json({
      success: true,
      data: result[0]
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating gallery item:', error);
    return json({
      success: false,
      error: 'Failed to create gallery item'
    }, { status: 500 });
  }
};
