import { sql } from 'drizzle-orm';
import { sqliteTable, text, integer, blob, primaryKey, timestamp } from 'drizzle-orm/sqlite-core';

export async function up(db: any) {
  // Create users table
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT NOT NULL UNIQUE,
      display_name TEXT NOT NULL,
      email TEXT NOT NULL UNIQUE,
      password_hash TEXT NOT NULL,
      role TEXT NOT NULL DEFAULT 'user',
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      preferences TEXT DEFAULT '{"highContrast": false, "largeText": false, "simplifiedInterface": false}'
    )
  `);

  // Create news table
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS news (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      content TEXT NOT NULL,
      image_url TEXT,
      author_id INTEGER,
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      published INTEGER NOT NULL DEFAULT 0,
      FOREIGN KEY (author_id) REFERENCES users(id)
    )
  `);

  // Create gallery table
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS gallery (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      description TEXT,
      image_url TEXT NOT NULL,
      thumbnail_url TEXT NOT NULL,
      author_id INTEGER,
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      published INTEGER NOT NULL DEFAULT 0,
      FOREIGN KEY (author_id) REFERENCES users(id)
    )
  `);

  // Create messages table
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      content TEXT NOT NULL,
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      approved INTEGER NOT NULL DEFAULT 0,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `);

  // Create replies table
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS replies (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      message_id INTEGER NOT NULL,
      user_id INTEGER,
      content TEXT NOT NULL,
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      approved INTEGER NOT NULL DEFAULT 0,
      FOREIGN KEY (message_id) REFERENCES messages(id),
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `);

  // Create comments table
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS comments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      content TEXT NOT NULL,
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      approved INTEGER NOT NULL DEFAULT 0,
      item_type TEXT NOT NULL,
      item_id INTEGER NOT NULL,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `);

  // Create site_settings table
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS site_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      category TEXT NOT NULL,
      settings TEXT NOT NULL
    )
  `);

  // Create message_of_the_day table
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS message_of_the_day (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      content TEXT NOT NULL,
      active INTEGER NOT NULL DEFAULT 1,
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Insert default admin user
  await db.run(sql`
    INSERT INTO users (username, display_name, email, password_hash, role)
    VALUES ('admin', 'Administrator', '<EMAIL>', '$2a$10$JdJF.3Q9HeAQJmNgdqA3ZO7YRoJI5xYdA.ZuXGpXZbR.Uy/vz/Kru', 'admin')
  `);

  // Insert default site settings
  await db.run(sql`
    INSERT INTO site_settings (category, settings)
    VALUES 
      ('general', '{"siteName": "Finn Wolfhard Fan Club", "description": "Official fan club for Finn Wolfhard", "contactEmail": "<EMAIL>"}'),
      ('appearance', '{"primaryColor": "#4a90e2", "secondaryColor": "#27ae60", "textColor": "#333333", "backgroundColor": "#ffffff", "darkMode": false}'),
      ('accessibility', '{"defaultHighContrast": false, "defaultLargeText": false, "defaultSimplifiedInterface": false}'),
      ('social', '{"twitter": "https://twitter.com/FinnWolfhard", "instagram": "https://www.instagram.com/finnwolfhardofficial/", "facebook": "https://www.facebook.com/FinnWolfhardOfficial"}')
  `);

  // Insert some initial message of the day entries
  await db.run(sql`
    INSERT INTO message_of_the_day (content, active)
    VALUES 
      ('Finn Wolfhard was born on December 23, 2002, in Vancouver, Canada.', 1),
      ('Finn Wolfhard plays Mike Wheeler in the Netflix series Stranger Things.', 1),
      ('Finn is also the lead vocalist and guitarist for the rock band The Aubreys.', 1),
      ('Finn made his directorial debut with the short film "Night Shifts" in 2020.', 1),
      ('Finn starred as Richie Tozier in the horror films "It" (2017) and "It Chapter Two" (2019).', 1)
  `);
}

export async function down(db: any) {
  await db.run(sql`DROP TABLE IF EXISTS message_of_the_day`);
  await db.run(sql`DROP TABLE IF EXISTS site_settings`);
  await db.run(sql`DROP TABLE IF EXISTS comments`);
  await db.run(sql`DROP TABLE IF EXISTS replies`);
  await db.run(sql`DROP TABLE IF EXISTS messages`);
  await db.run(sql`DROP TABLE IF EXISTS gallery`);
  await db.run(sql`DROP TABLE IF EXISTS news`);
  await db.run(sql`DROP TABLE IF EXISTS users`);
}
