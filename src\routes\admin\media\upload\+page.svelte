<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';

  // State variables
  let files = [];
  let uploading = false;
  let uploadProgress = 0;
  let uploadedFiles = [];
  let error = null;

  // Form data
  let alt = '';
  let caption = '';
  let type = 'image'; // Default type

  // Available types
  const mediaTypes = [
    { id: 'image', name: 'Image' },
    { id: 'video', name: 'Video' },
    { id: 'document', name: 'Document' }
  ];

  // Handle file selection
  function handleFileSelect(event) {
    const fileList = event.target.files;
    if (fileList.length > 0) {
      files = Array.from(fileList);
      error = null;
    }
  }

  // Handle file drop
  function handleDrop(event) {
    event.preventDefault();
    const fileList = event.dataTransfer.files;
    if (fileList.length > 0) {
      files = Array.from(fileList);
      error = null;
    }
  }

  // Prevent default drag behavior
  function handleDragOver(event) {
    event.preventDefault();
  }

  // Upload a single file
  async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      return await response.json();
    } catch (err) {
      console.error('Error uploading file:', err);
      throw err;
    }
  }

  // Create a media item with the uploaded file
  async function createMediaItem(uploadData) {
    try {
      const response = await fetch('/api/media', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          filename: uploadData.filename,
          originalName: uploadData.originalName,
          path: uploadData.imageUrl,
          thumbnailPath: uploadData.thumbnailUrl,
          type,
          mimeType: uploadData.type,
          size: uploadData.size,
          alt,
          caption
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create media item');
      }

      return await response.json();
    } catch (err) {
      console.error('Error creating media item:', err);
      throw err;
    }
  }

  // Handle form submission
  async function handleSubmit(event) {
    event.preventDefault();

    if (files.length === 0) {
      error = 'Please select a file to upload';
      return;
    }

    try {
      uploading = true;
      error = null;
      uploadedFiles = [];

      // Upload each file and create media items
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Update progress
        uploadProgress = Math.round((i / files.length) * 100);

        // Upload the file
        const uploadResult = await uploadFile(file);

        // Create a media item with the uploaded file
        const mediaResult = await createMediaItem(uploadResult.data);

        // Add to uploaded files
        uploadedFiles.push({
          ...uploadResult.data,
          mediaId: mediaResult.data.id
        });
      }

      // Complete
      uploadProgress = 100;

      // Reset form after successful upload
      alt = '';
      caption = '';
      files = [];

      // Redirect to media admin after a short delay
      setTimeout(() => {
        goto('/admin/media');
      }, 2000);
    } catch (err) {
      error = err.message || 'Upload failed';
    } finally {
      uploading = false;
    }
  }
</script>

<svelte:head>
  <title>Upload Media - Admin</title>
</svelte:head>

<div class="upload-container">
  <h1>Upload Media</h1>

  {#if error}
    <div class="error-message">
      <p>{error}</p>
    </div>
  {/if}

  {#if uploadedFiles.length > 0}
    <div class="success-message">
      <p>Successfully uploaded {uploadedFiles.length} {uploadedFiles.length === 1 ? 'file' : 'files'}!</p>
      <div class="uploaded-previews">
        {#each uploadedFiles as file}
          <div class="uploaded-item">
            <img src={file.thumbnailUrl} alt={file.originalName} />
            <p>{file.originalName}</p>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <form on:submit={handleSubmit}>
    <div class="form-group">
      <label for="type">Media Type</label>
      <select id="type" bind:value={type} disabled={uploading}>
        {#each mediaTypes as mediaType}
          <option value={mediaType.id}>{mediaType.name}</option>
        {/each}
      </select>
    </div>

    <div class="form-group">
      <label for="alt">Alt Text</label>
      <input
        type="text"
        id="alt"
        bind:value={alt}
        placeholder="Brief description of the media (for accessibility)"
        disabled={uploading}
      />
    </div>

    <div class="form-group">
      <label for="caption">Caption</label>
      <textarea
        id="caption"
        bind:value={caption}
        rows="3"
        placeholder="Optional caption or description"
        disabled={uploading}
      ></textarea>
    </div>

    <div
      class="file-drop-area"
      on:drop={handleDrop}
      on:dragover={handleDragOver}
      role="region"
      aria-label="File upload drop zone"
    >
      <input
        type="file"
        id="file-input"
        on:change={handleFileSelect}
        accept="image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
        multiple
        disabled={uploading}
      />
      <label for="file-input" class="file-label">
        {#if files.length > 0}
          {files.length} {files.length === 1 ? 'file' : 'files'} selected
        {:else}
          Drag & drop files here or click to browse
        {/if}
      </label>

      {#if files.length > 0}
        <div class="file-list">
          {#each files as file}
            <div class="file-item">
              <span class="file-name">{file.name}</span>
              <span class="file-size">({Math.round(file.size / 1024)} KB)</span>
            </div>
          {/each}
        </div>
      {/if}
    </div>

    {#if uploading}
      <div class="progress-bar">
        <div class="progress" style="width: {uploadProgress}%"></div>
        <span class="progress-text">{uploadProgress}%</span>
      </div>
    {/if}

    <div class="form-actions">
      <button type="submit" class="btn primary" disabled={uploading}>
        {#if uploading}
          Uploading...
        {:else}
          Upload Files
        {/if}
      </button>
      <a href="/admin/media" class="btn secondary" class:disabled={uploading}>Cancel</a>
    </div>
  </form>
</div>

<style>
  .upload-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  h1 {
    margin-bottom: 2rem;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
  }

  input[type="text"],
  textarea,
  select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
  }

  .file-drop-area {
    border: 2px dashed #ddd;
    border-radius: 4px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
    position: relative;
  }

  .file-drop-area input[type="file"] {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
  }

  .file-label {
    font-size: 1.1rem;
    color: #666;
  }

  .file-list {
    margin-top: 1rem;
    text-align: left;
  }

  .file-item {
    padding: 0.5rem;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 0.5rem;
  }

  .file-size {
    color: #666;
    font-size: 0.9rem;
  }

  .progress-bar {
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    overflow: hidden;
    position: relative;
  }

  .progress {
    height: 100%;
    background-color: #4caf50;
    transition: width 0.3s ease;
  }

  .progress-text {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    text-align: center;
    line-height: 20px;
    font-size: 0.8rem;
    color: #fff;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .primary {
    background-color: #4caf50;
    color: white;
  }

  .secondary {
    background-color: #f0f0f0;
    color: #333;
  }

  .disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
  }

  .success-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
  }

  .uploaded-previews {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
  }

  .uploaded-item {
    width: 100px;
    text-align: center;
  }

  .uploaded-item img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
  }

  .uploaded-item p {
    font-size: 0.8rem;
    margin-top: 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
