<script>
	import { goto } from '$app/navigation';

	// Props from page data
	export let data;

	// Extract data
	$: image = data.image;
	$: navigation = data.navigation;

	/**
	 * Format date for display
	 * @param {string} dateString - ISO date string
	 * @returns {string} Formatted date
	 */
	function formatDate(dateString) {
		const date = new Date(dateString);
		return date.toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	/**
	 * Handle keyboard navigation
	 * @param {KeyboardEvent} event
	 */
	function handleKeydown(event) {
		if (event.key === 'ArrowLeft' && navigation.previous) {
			goto(`/gallery/${navigation.previous.id}`);
		} else if (event.key === 'ArrowRight' && navigation.next) {
			goto(`/gallery/${navigation.next.id}`);
		} else if (event.key === 'Escape') {
			goto('/gallery');
		}
	}

	/**
	 * Download the original image
	 */
	function downloadImage() {
		const link = document.createElement('a');
		link.href = image.imageUrl;
		link.download = `${image.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.jpg`;
		link.target = '_blank';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}
</script>

<svelte:head>
	<title>{image.title} - FWFC Gallery</title>
	<meta name="description" content={image.description || `View ${image.title} in the FWFC photo gallery`} />
	
	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="article" />
	<meta property="og:title" content="{image.title} - FWFC Gallery" />
	<meta property="og:description" content={image.description || `View ${image.title} in the FWFC photo gallery`} />
	<meta property="og:image" content={image.imageUrl} />
	<meta property="og:url" content="https://fwfc.com/gallery/{image.id}" />
	
	<!-- Twitter -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:title" content="{image.title} - FWFC Gallery" />
	<meta name="twitter:description" content={image.description || `View ${image.title} in the FWFC photo gallery`} />
	<meta name="twitter:image" content={image.imageUrl} />
</svelte:head>

<svelte:window on:keydown={handleKeydown} />

<div class="gallery-detail">
	<!-- Header with navigation -->
	<header class="gallery-header">
		<div class="container">
			<nav class="breadcrumb">
				<a href="/">Home</a> / 
				<a href="/gallery">Gallery</a> / 
				<span>{image.title}</span>
			</nav>
			
			<div class="gallery-meta">
				<h1>{image.title}</h1>
				{#if image.description}
					<p class="description">{image.description}</p>
				{/if}
				<div class="image-info">
					<span class="position">
						Image {navigation.currentPosition} of {navigation.totalImages}
					</span>
					<span class="date">
						Uploaded {formatDate(image.createdAt)}
					</span>
				</div>
			</div>
		</div>
	</header>

	<!-- Main image display -->
	<main class="image-container">
		<div class="image-wrapper">
			<img 
				src={image.imageUrl} 
				alt={image.title}
				class="main-image"
			/>
			
			<!-- Image overlay controls -->
			<div class="image-controls">
				<button 
					class="control-btn download-btn" 
					on:click={downloadImage}
					title="Download original image"
				>
					<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
						<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
						<polyline points="7,10 12,15 17,10"/>
						<line x1="12" y1="15" x2="12" y2="3"/>
					</svg>
					Download
				</button>
			</div>
		</div>
	</main>

	<!-- Navigation controls -->
	<nav class="image-navigation">
		<div class="container">
			<div class="nav-controls">
				<!-- Previous image -->
				{#if navigation.previous}
					<a 
						href="/gallery/{navigation.previous.id}" 
						class="nav-btn prev-btn"
						title="Previous image: {navigation.previous.title}"
					>
						<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<polyline points="15,18 9,12 15,6"/>
						</svg>
						<span class="nav-text">
							<span class="nav-label">Previous</span>
							<span class="nav-title">{navigation.previous.title}</span>
						</span>
					</a>
				{:else}
					<div class="nav-btn disabled">
						<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<polyline points="15,18 9,12 15,6"/>
						</svg>
						<span class="nav-text">
							<span class="nav-label">Previous</span>
							<span class="nav-title">No previous image</span>
						</span>
					</div>
				{/if}

				<!-- Back to gallery -->
				<a href="/gallery" class="nav-btn gallery-btn">
					<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
						<rect x="3" y="3" width="7" height="7"/>
						<rect x="14" y="3" width="7" height="7"/>
						<rect x="14" y="14" width="7" height="7"/>
						<rect x="3" y="14" width="7" height="7"/>
					</svg>
					Back to Gallery
				</a>

				<!-- Next image -->
				{#if navigation.next}
					<a 
						href="/gallery/{navigation.next.id}" 
						class="nav-btn next-btn"
						title="Next image: {navigation.next.title}"
					>
						<span class="nav-text">
							<span class="nav-label">Next</span>
							<span class="nav-title">{navigation.next.title}</span>
						</span>
						<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<polyline points="9,18 15,12 9,6"/>
						</svg>
					</a>
				{:else}
					<div class="nav-btn disabled">
						<span class="nav-text">
							<span class="nav-label">Next</span>
							<span class="nav-title">No next image</span>
						</span>
						<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<polyline points="9,18 15,12 9,6"/>
						</svg>
					</div>
				{/if}
			</div>
		</div>
	</nav>

	<!-- Keyboard shortcuts help -->
	<div class="keyboard-help">
		<p>
			<strong>Keyboard shortcuts:</strong> 
			← Previous image | → Next image | Esc Back to gallery
		</p>
	</div>
</div>

<style>
	.gallery-detail {
		min-height: 100vh;
		background-color: var(--theme-gallery-bg);
		color: var(--theme-gallery-text);
	}

	.container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	/* Header */
	.gallery-header {
		background: var(--theme-gallery-overlay);
		padding: 2rem 0;
		position: relative;
		z-index: 10;
		border-bottom: 1px solid var(--theme-border);
	}

	.breadcrumb {
		font-size: 0.9rem;
		margin-bottom: 1rem;
		opacity: 0.8;
	}

	.breadcrumb a {
		color: var(--theme-accent-primary);
		text-decoration: none;
	}

	.breadcrumb a:hover {
		text-decoration: underline;
		color: var(--theme-accent-primary-hover);
	}

	.gallery-meta h1 {
		font-size: 2.5rem;
		margin: 0 0 1rem 0;
		font-weight: bold;
	}

	.description {
		font-size: 1.2rem;
		margin: 0 0 1rem 0;
		opacity: 0.9;
		line-height: 1.6;
	}

	.image-info {
		display: flex;
		gap: 2rem;
		font-size: 0.9rem;
		opacity: 0.8;
	}

	/* Main image */
	.image-container {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 60vh;
		padding: 2rem 0;
	}

	.image-wrapper {
		position: relative;
		max-width: 100%;
		max-height: 80vh;
	}

	.main-image {
		max-width: 100%;
		max-height: 80vh;
		width: auto;
		height: auto;
		object-fit: contain;
		border-radius: 8px;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
	}

	/* Image controls */
	.image-controls {
		position: absolute;
		top: 1rem;
		right: 1rem;
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	.image-wrapper:hover .image-controls {
		opacity: 1;
	}

	.control-btn {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		background: var(--theme-gallery-overlay);
		color: var(--theme-gallery-text);
		border: 1px solid var(--theme-border);
		padding: 0.75rem 1rem;
		border-radius: 6px;
		cursor: pointer;
		font-size: 0.9rem;
		transition: all 0.2s ease;
	}

	.control-btn:hover {
		background: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
	}

	/* Navigation */
	.image-navigation {
		background: var(--theme-bg-secondary);
		padding: 2rem 0;
		border-top: 1px solid var(--theme-border);
	}

	.nav-controls {
		display: grid;
		grid-template-columns: 1fr auto 1fr;
		gap: 2rem;
		align-items: center;
	}

	.nav-btn {
		display: flex;
		align-items: center;
		gap: 1rem;
		padding: 1rem;
		background: var(--theme-card-bg);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		color: var(--theme-text-primary);
		text-decoration: none;
		transition: all 0.3s ease;
		min-height: 80px;
	}

	.nav-btn:not(.disabled):hover {
		background: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-2px);
		box-shadow: 0 4px 12px var(--theme-shadow-hover);
	}

	.nav-btn.disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.nav-btn.gallery-btn {
		justify-content: center;
		background: var(--theme-accent-primary);
		border-color: var(--theme-accent-primary);
		color: white;
		font-weight: 500;
	}

	.nav-btn.gallery-btn:hover {
		background: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
	}

	.nav-btn.next-btn {
		justify-self: end;
		flex-direction: row-reverse;
	}

	.nav-text {
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
	}

	.nav-label {
		font-size: 0.8rem;
		opacity: 0.8;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.nav-title {
		font-size: 0.9rem;
		font-weight: 500;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		max-width: 200px;
	}

	/* Keyboard help */
	.keyboard-help {
		text-align: center;
		padding: 1rem 0;
		font-size: 0.8rem;
		opacity: 0.6;
		border-top: 1px solid var(--theme-border);
		color: var(--theme-text-secondary);
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.container {
			padding: 0 1rem;
		}

		.gallery-meta h1 {
			font-size: 2rem;
		}

		.image-info {
			flex-direction: column;
			gap: 0.5rem;
		}

		.nav-controls {
			grid-template-columns: 1fr;
			gap: 1rem;
		}

		.nav-btn.next-btn {
			justify-self: stretch;
			flex-direction: row;
		}

		.nav-title {
			max-width: 150px;
		}

		.image-controls {
			position: static;
			opacity: 1;
			margin-top: 1rem;
			text-align: center;
		}
	}
</style>
