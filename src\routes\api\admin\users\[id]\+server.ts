import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';
import crypto from 'crypto';

// GET /api/admin/users/[id] - Get specific user details
export const GET: RequestHandler = async ({ params, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || (locals.user.role !== 'admin' && locals.user.role !== 'moderator')) {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const userId = parseInt(params.id);
    if (isNaN(userId)) {
      return json({
        success: false,
        error: 'Invalid user ID'
      }, { status: 400 });
    }

    // Get user details
    const user = await db.select({
      id: users.id,
      username: users.username,
      displayName: users.displayName,
      email: users.email,
      role: users.role,
      status: users.status,
      isSimulated: users.isSimulated,
      bio: users.bio,
      avatarUrl: users.avatarUrl,
      location: users.location,
      website: users.website,
      birthDate: users.birthDate,
      interests: users.interests,
      simulatedPersonality: users.simulatedPersonality,
      lastActiveAt: users.lastActiveAt,
      createdAt: users.createdAt,
      updatedAt: users.updatedAt,
      preferences: users.preferences
    }).from(users).where(eq(users.id, userId)).limit(1);

    if (user.length === 0) {
      return json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Log admin action
    logger.info('Admin accessed user details', {
      adminUser: locals.user.username,
      targetUser: user[0].username,
      userId
    });

    return json({
      success: true,
      data: user[0]
    });
  } catch (error) {
    logger.error('Error fetching user details:', error);
    return json({
      success: false,
      error: 'Failed to fetch user details'
    }, { status: 500 });
  }
};

// PUT /api/admin/users/[id] - Update user details
export const PUT: RequestHandler = async ({ params, request, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || (locals.user.role !== 'admin' && locals.user.role !== 'moderator')) {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const userId = parseInt(params.id);
    if (isNaN(userId)) {
      return json({
        success: false,
        error: 'Invalid user ID'
      }, { status: 400 });
    }

    const body = await request.json();

    // Check if user exists
    const existingUser = await db.select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (existingUser.length === 0) {
      return json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Prevent non-admin users from modifying admin accounts
    if (locals.user.role !== 'admin' && existingUser[0].role === 'admin') {
      return json({
        success: false,
        error: 'Insufficient privileges to modify admin accounts'
      }, { status: 403 });
    }

    // Prevent users from modifying their own role (except admin)
    if (locals.user.id === userId && locals.user.role !== 'admin' && body.role) {
      return json({
        success: false,
        error: 'Cannot modify your own role'
      }, { status: 403 });
    }

    // Build update object
    const updateData: any = {
      updatedAt: new Date().toISOString()
    };

    if (body.displayName !== undefined) {
      updateData.displayName = body.displayName;
    }

    if (body.email !== undefined) {
      // Check if email is already taken by another user
      const emailCheck = await db.select()
        .from(users)
        .where(eq(users.email, body.email))
        .limit(1);

      if (emailCheck.length > 0 && emailCheck[0].id !== userId) {
        return json({
          success: false,
          error: 'Email address is already taken'
        }, { status: 409 });
      }

      updateData.email = body.email;
    }

    if (body.role !== undefined) {
      // Only admins can change roles
      if (locals.user.role !== 'admin') {
        return json({
          success: false,
          error: 'Only administrators can change user roles'
        }, { status: 403 });
      }

      if (!['admin', 'moderator', 'user'].includes(body.role)) {
        return json({
          success: false,
          error: 'Invalid role specified'
        }, { status: 400 });
      }

      updateData.role = body.role;
    }

    if (body.preferences !== undefined) {
      updateData.preferences = body.preferences;
    }

    // Handle new profile fields
    if (body.status !== undefined) {
      if (!['active', 'inactive', 'suspended'].includes(body.status)) {
        return json({
          success: false,
          error: 'Invalid status specified'
        }, { status: 400 });
      }
      updateData.status = body.status;
    }

    if (body.isSimulated !== undefined) {
      updateData.isSimulated = body.isSimulated;
    }

    if (body.bio !== undefined) {
      updateData.bio = body.bio;
    }

    if (body.avatarUrl !== undefined) {
      updateData.avatarUrl = body.avatarUrl;
    }

    if (body.location !== undefined) {
      updateData.location = body.location;
    }

    if (body.website !== undefined) {
      updateData.website = body.website;
    }

    if (body.birthDate !== undefined) {
      updateData.birthDate = body.birthDate;
    }

    if (body.interests !== undefined) {
      updateData.interests = body.interests;
    }

    if (body.simulatedPersonality !== undefined) {
      updateData.simulatedPersonality = body.simulatedPersonality;
    }

    // Update user
    const result = await db.update(users)
      .set(updateData)
      .where(eq(users.id, userId))
      .returning({
        id: users.id,
        username: users.username,
        displayName: users.displayName,
        email: users.email,
        role: users.role,
        status: users.status,
        isSimulated: users.isSimulated,
        bio: users.bio,
        avatarUrl: users.avatarUrl,
        location: users.location,
        website: users.website,
        birthDate: users.birthDate,
        interests: users.interests,
        simulatedPersonality: users.simulatedPersonality,
        lastActiveAt: users.lastActiveAt,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        preferences: users.preferences
      });

    // Log admin action
    logger.info('User updated by admin', {
      adminUser: locals.user.username,
      targetUser: result[0].username,
      userId,
      changes: Object.keys(updateData)
    });

    return json({
      success: true,
      data: result[0],
      message: 'User updated successfully'
    });
  } catch (error) {
    logger.error('Error updating user:', error);
    return json({
      success: false,
      error: 'Failed to update user'
    }, { status: 500 });
  }
};

// DELETE /api/admin/users/[id] - Delete user (admin only)
export const DELETE: RequestHandler = async ({ params, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const userId = parseInt(params.id);
    if (isNaN(userId)) {
      return json({
        success: false,
        error: 'Invalid user ID'
      }, { status: 400 });
    }

    // Prevent users from deleting themselves
    if (locals.user.id === userId) {
      return json({
        success: false,
        error: 'Cannot delete your own account'
      }, { status: 403 });
    }

    // Check if user exists
    const existingUser = await db.select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (existingUser.length === 0) {
      return json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Delete user
    await db.delete(users).where(eq(users.id, userId));

    // Log admin action
    logger.info('User deleted by admin', {
      adminUser: locals.user.username,
      deletedUser: existingUser[0].username,
      userId
    });

    return json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting user:', error);
    return json({
      success: false,
      error: 'Failed to delete user'
    }, { status: 500 });
  }
};
