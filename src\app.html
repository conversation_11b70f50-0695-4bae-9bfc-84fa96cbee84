<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<style>
			/* CSS Custom Properties for Theme System */
			:root {
				/* Light Theme (Default) */
				--theme-bg-primary: #ffffff;
				--theme-bg-secondary: #f8f9fa;
				--theme-bg-tertiary: #e9ecef;
				--theme-text-primary: #212529;
				--theme-text-secondary: #6c757d;
				--theme-text-muted: #adb5bd;
				--theme-border: #dee2e6;
				--theme-border-light: #e9ecef;
				--theme-shadow: rgba(0, 0, 0, 0.1);
				--theme-shadow-hover: rgba(0, 0, 0, 0.15);

				/* Accent Colors (consistent across themes) */
				--theme-accent-primary: #4caf50;
				--theme-accent-primary-hover: #45a049;
				--theme-accent-secondary: #2196f3;
				--theme-accent-danger: #f44336;
				--theme-accent-warning: #ff9800;
				--theme-accent-success: #4caf50;

				/* Component-specific colors */
				--theme-card-bg: var(--theme-bg-primary);
				--theme-card-border: var(--theme-border);
				--theme-input-bg: var(--theme-bg-primary);
				--theme-input-border: var(--theme-border);
				--theme-button-bg: var(--theme-bg-secondary);
				--theme-button-text: var(--theme-text-primary);
				--theme-nav-bg: var(--theme-bg-primary);
				--theme-nav-text: var(--theme-text-primary);

				/* Gallery-specific colors */
				--theme-gallery-bg: var(--theme-bg-primary);
				--theme-gallery-overlay: rgba(255, 255, 255, 0.9);
				--theme-gallery-text: var(--theme-text-primary);
			}

			/* Dark Theme */
			[data-theme="dark"] {
				--theme-bg-primary: #1a1a1a;
				--theme-bg-secondary: #2d2d2d;
				--theme-bg-tertiary: #404040;
				--theme-text-primary: #ffffff;
				--theme-text-secondary: #e0e0e0;
				--theme-text-muted: #c0c0c0;
				--theme-border: #404040;
				--theme-border-light: #2d2d2d;
				--theme-shadow: rgba(0, 0, 0, 0.3);
				--theme-shadow-hover: rgba(0, 0, 0, 0.4);

				/* Component-specific colors for dark theme */
				--theme-card-bg: var(--theme-bg-secondary);
				--theme-card-border: var(--theme-border);
				--theme-input-bg: var(--theme-bg-secondary);
				--theme-input-border: var(--theme-border);
				--theme-button-bg: var(--theme-bg-tertiary);
				--theme-button-text: var(--theme-text-primary);
				--theme-nav-bg: var(--theme-bg-primary);
				--theme-nav-text: var(--theme-text-primary);

				/* Gallery-specific colors for dark theme */
				--theme-gallery-bg: var(--theme-bg-primary);
				--theme-gallery-overlay: rgba(0, 0, 0, 0.9);
				--theme-gallery-text: var(--theme-text-primary);
			}

			/* Smooth transitions for theme changes */
			* {
				transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
			}
		</style>
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
