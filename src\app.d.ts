// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			user?: {
				id: number;
				username: string;
				displayName: string;
				email: string;
				role: string;
				preferences?: {
					highContrast: boolean;
					largeText: boolean;
					simplifiedInterface: boolean;
				};
			};
			accessibility?: {
				highContrast: boolean;
				largeText: boolean;
				simplifiedInterface: boolean;
			};
			theme?: 'light' | 'dark';
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

// Add theme-related types
declare type ThemePreference = 'light' | 'dark' | 'auto';
declare type Theme = 'light' | 'dark';
declare type ThemeState = {
	current: Theme;
	preference: ThemePreference;
	systemTheme: Theme;
};

declare type ThemeColors = {
	primary: string;
	secondary: string;
	tertiary: string;
	text: {
		primary: string;
		secondary: string;
		muted: string;
	};
	border: {
		primary: string;
		light: string;
	};
	shadow: {
		primary: string;
		hover: string;
	};
	accent: {
		primary: string;
		secondary: string;
		danger: string;
		warning: string;
		success: string;
	};
};

export {};
