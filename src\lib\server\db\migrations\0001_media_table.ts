import { sql } from 'drizzle-orm';

export async function up(db: any) {
  // Create media table
  await db.run(sql`
    CREATE TABLE IF NOT EXISTS media (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      filename TEXT NOT NULL,
      original_name TEXT NOT NULL,
      path TEXT NOT NULL,
      thumbnail_path TEXT,
      type TEXT NOT NULL,
      mime_type TEXT NOT NULL,
      size INTEGER NOT NULL,
      width INTEGER,
      height INTEGER,
      alt TEXT,
      caption TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      author_id INTEGER,
      FOREIGN KEY (author_id) REFERENCES users(id)
    )
  `);
}

export async function down(db: any) {
  await db.run(sql`DROP TABLE IF EXISTS media`);
}
