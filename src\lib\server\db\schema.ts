import {
	sqliteTable,
	text,
	integer,
	blob,
	primaryKey
} from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

import { relations } from 'drizzle-orm';

export const users = sqliteTable('users', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	username: text('username').notNull().unique(),
	displayName: text('display_name').notNull(),
	email: text('email').notNull().unique(),
	passwordHash: text('password_hash').notNull(),
	role: text('role', { enum: ['admin', 'moderator', 'user'] }).default('user').notNull(),
	// Enhanced profile fields
	bio: text('bio'),
	avatarUrl: text('avatar_url'),
	location: text('location'),
	website: text('website'),
	birthDate: text('birth_date'),
	interests: text('interests', { mode: 'json' }).$type<string[]>().default('[]'),
	// Account status and management
	status: text('status', { enum: ['active', 'inactive', 'suspended'] }).default('active').notNull(),
	isSimulated: integer('is_simulated', { mode: 'boolean' }).notNull().default(false),
	simulatedPersonality: text('simulated_personality', { mode: 'json' }).$type<{
		traits: string[];
		interests: string[];
		writingStyle: string;
		activityLevel: 'low' | 'medium' | 'high';
	}>(),
	lastActiveAt: text('last_active_at'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	preferences: text('preferences', { mode: 'json' }).$type<{
		highContrast: boolean;
		largeText: boolean;
		simplifiedInterface: boolean
	 }>().default('{"highContrast": false, "largeText": false, "simplifiedInterface": false}')
});

export const news = sqliteTable('news', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	content: text('content').notNull(),
	imageUrl: text('image_url'),
	authorId: integer('author_id').references(() => users.id),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	published: integer('published', { mode: 'boolean' }).notNull().default(false)
});

export const gallery = sqliteTable('gallery', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	description: text('description'),
	imageUrl: text('image_url').notNull(),
	thumbnailUrl: text('thumbnail_url').notNull(),
	authorId: integer('author_id').references(() => users.id),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	published: integer('published', { mode: 'boolean' }).notNull().default(false)
});

export const messages = sqliteTable('messages', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	userId: integer('user_id').references(() => users.id),
	content: text('content').notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	approved: integer('approved', { mode: 'boolean' }).notNull().default(false)
});

export const replies = sqliteTable('replies', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	messageId: integer('message_id').references(() => messages.id).notNull(),
	userId: integer('user_id').references(() => users.id),
	content: text('content').notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	approved: integer('approved', { mode: 'boolean' }).notNull().default(false)
});

export const comments = sqliteTable('comments', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	userId: integer('user_id').references(() => users.id),
	content: text('content').notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	approved: integer('approved', { mode: 'boolean' }).notNull().default(false),
	itemType: text('item_type', { enum: ['news', 'gallery'] }).notNull(),
	itemId: integer('item_id').notNull()
});

export const siteSettings = sqliteTable('site_settings', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	category: text('category', {
		enum: [
			'general',
			'appearance',
			'accessibility',
			'social'
		]
	}).notNull(),
	settings: text('settings', { mode: 'json' }).notNull()
});

export const messageOfTheDay = sqliteTable('message_of_the_day', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	content: text('content').notNull(),
	active: integer('active', { mode: 'boolean' }).notNull().default(true),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

export const media = sqliteTable('media', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	filename: text('filename').notNull(),
	originalName: text('original_name').notNull(),
	path: text('path').notNull(),
	thumbnailPath: text('thumbnail_path'),
	type: text('type').notNull(), // image, video, document, etc.
	mimeType: text('mime_type').notNull(),
	size: integer('size').notNull(),
	width: integer('width'),
	height: integer('height'),
	alt: text('alt'),
	caption: text('caption'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	authorId: integer('author_id').references(() => users.id)
});

export const heroImages = sqliteTable('hero_images', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	subtitle: text('subtitle'),
	imageUrl: text('image_url').notNull(),
	active: integer('active', { mode: 'boolean' }).notNull().default(false),
	sortOrder: integer('sort_order').notNull().default(0),
	authorId: integer('author_id').references(() => users.id),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// Audit logging for admin actions
export const auditLogs = sqliteTable('audit_logs', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	adminUserId: integer('admin_user_id').references(() => users.id).notNull(),
	action: text('action').notNull(), // 'create', 'update', 'delete', 'login', 'post_as_user', etc.
	targetType: text('target_type').notNull(), // 'user', 'news', 'gallery', 'comment', etc.
	targetId: integer('target_id'), // ID of the affected record
	targetUserId: integer('target_user_id').references(() => users.id), // User being acted upon or posted as
	details: text('details', { mode: 'json' }).$type<{
		before?: any;
		after?: any;
		metadata?: any;
	}>(),
	ipAddress: text('ip_address'),
	userAgent: text('user_agent'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// Scheduled content for natural posting patterns
export const scheduledContent = sqliteTable('scheduled_content', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	contentType: text('content_type', { enum: ['news', 'gallery', 'comment', 'message', 'reply'] }).notNull(),
	contentData: text('content_data', { mode: 'json' }).notNull(), // The actual content to be posted
	asUserId: integer('as_user_id').references(() => users.id).notNull(), // User to post as
	scheduledFor: text('scheduled_for').notNull(), // ISO datetime string
	status: text('status', { enum: ['pending', 'published', 'failed', 'cancelled'] }).default('pending').notNull(),
	createdByUserId: integer('created_by_user_id').references(() => users.id).notNull(), // Admin who scheduled it
	publishedAt: text('published_at'),
	errorMessage: text('error_message'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// Content authorship tracking for admin-posted content
export const contentAuthorship = sqliteTable('content_authorship', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	contentType: text('content_type', { enum: ['news', 'gallery', 'comment', 'message', 'reply'] }).notNull(),
	contentId: integer('content_id').notNull(),
	actualAuthorId: integer('actual_author_id').references(() => users.id).notNull(), // Admin who actually created it
	displayAuthorId: integer('display_author_id').references(() => users.id).notNull(), // User it appears to be from
	isSimulated: integer('is_simulated', { mode: 'boolean' }).notNull().default(true),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// AI-generated content review system
export const aiContentReviews = sqliteTable('ai_content_reviews', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	contentType: text('content_type', { enum: ['news', 'gallery', 'comment', 'message', 'reply'] }).notNull(),
	originalPrompt: text('original_prompt').notNull(),
	generatedContent: text('generated_content', { mode: 'json' }).notNull(), // JSON with title, content, description
	authenticityScore: integer('authenticity_score').notNull(),
	aiConfig: text('ai_config', { mode: 'json' }).notNull(), // JSON with tone, length, focusAreas, etc.
	targetUserId: integer('target_user_id').references(() => users.id).notNull(),
	reviewStatus: text('review_status', { enum: ['pending', 'approved', 'rejected', 'needs_revision'] }).notNull().default('pending'),
	reviewedById: integer('reviewed_by_id').references(() => users.id),
	reviewNotes: text('review_notes'),
	reviewedAt: text('reviewed_at'),
	createdById: integer('created_by_id').references(() => users.id).notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// AI content generation metrics and monitoring
export const aiGenerationMetrics = sqliteTable('ai_generation_metrics', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	contentType: text('content_type', { enum: ['news', 'gallery', 'comment', 'message', 'reply'] }).notNull(),
	promptLength: integer('prompt_length').notNull(),
	generatedLength: integer('generated_length').notNull(),
	authenticityScore: integer('authenticity_score').notNull(),
	tone: text('tone').notNull(),
	length: text('length').notNull(),
	focusAreas: text('focus_areas', { mode: 'json' }).$type<string[]>().default('[]'),
	generationTimeMs: integer('generation_time_ms').notNull(),
	success: integer('success', { mode: 'boolean' }).notNull(),
	errorMessage: text('error_message'),
	userId: integer('user_id').references(() => users.id).notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// User interaction templates for natural conversation patterns
export const interactionTemplates = sqliteTable('interaction_templates', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	name: text('name').notNull(),
	category: text('category').notNull(), // 'greeting', 'comment', 'reaction', 'question', etc.
	template: text('template').notNull(), // Template with placeholders like {username}, {topic}, etc.
	variables: text('variables', { mode: 'json' }).$type<string[]>().default('[]'), // Available variables
	personality: text('personality', { mode: 'json' }).$type<string[]>().default('[]'), // Personality traits this fits
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

export const usersRelations = relations(users, ({ many }) => ({
	news: many(news),
	gallery: many(gallery),
	messages: many(messages),
	replies: many(replies),
	comments: many(comments),
	media: many(media),
	heroImages: many(heroImages),
	auditLogsAsAdmin: many(auditLogs, { relationName: 'adminAuditLogs' }),
	auditLogsAsTarget: many(auditLogs, { relationName: 'targetAuditLogs' }),
	scheduledContentAsUser: many(scheduledContent, { relationName: 'userScheduledContent' }),
	scheduledContentAsCreator: many(scheduledContent, { relationName: 'creatorScheduledContent' }),
	contentAuthorshipAsActual: many(contentAuthorship, { relationName: 'actualAuthorContent' }),
	contentAuthorshipAsDisplay: many(contentAuthorship, { relationName: 'displayAuthorContent' })
}));

export const newsRelations = relations(news, ({ one, many }) => ({
	author: one(users, {
		fields: [news.authorId],
		references: [users.id]
	}),
	comments: many(comments, {
		filterForeignFields: (comment) => comment.itemType.equals('news')
	})
}));

export const galleryRelations = relations(gallery, ({ one, many }) => ({
	author: one(users, {
		fields: [gallery.authorId],
		references: [users.id]
	}),
	comments: many(comments, {
		filterForeignFields: (comment) => comment.itemType.equals('gallery')
	})
}));

export const messagesRelations = relations(messages, ({ one, many }) => ({
	user: one(users, {
		fields: [messages.userId],
		references: [users.id]
	}),
	replies: many(replies)
}));

export const repliesRelations = relations(replies, ({ one }) => ({
	message: one(messages, {
		fields: [replies.messageId],
		references: [messages.id]
	}),
	user: one(users, {
		fields: [replies.userId],
		references: [users.id]
	})
}));

export const commentsRelations = relations(comments, ({ one }) => ({
	user: one(users, {
		fields: [comments.userId],
		references: [users.id]
	})
}));

export const mediaRelations = relations(media, ({ one }) => ({
	author: one(users, {
		fields: [media.authorId],
		references: [users.id]
	})
}));

export const heroImagesRelations = relations(heroImages, ({ one }) => ({
	author: one(users, {
		fields: [heroImages.authorId],
		references: [users.id]
	})
}));

export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
	adminUser: one(users, {
		fields: [auditLogs.adminUserId],
		references: [users.id],
		relationName: 'adminAuditLogs'
	}),
	targetUser: one(users, {
		fields: [auditLogs.targetUserId],
		references: [users.id],
		relationName: 'targetAuditLogs'
	})
}));

export const scheduledContentRelations = relations(scheduledContent, ({ one }) => ({
	asUser: one(users, {
		fields: [scheduledContent.asUserId],
		references: [users.id],
		relationName: 'userScheduledContent'
	}),
	createdByUser: one(users, {
		fields: [scheduledContent.createdByUserId],
		references: [users.id],
		relationName: 'creatorScheduledContent'
	})
}));

export const contentAuthorshipRelations = relations(contentAuthorship, ({ one }) => ({
	actualAuthor: one(users, {
		fields: [contentAuthorship.actualAuthorId],
		references: [users.id],
		relationName: 'actualAuthorContent'
	}),
	displayAuthor: one(users, {
		fields: [contentAuthorship.displayAuthorId],
		references: [users.id],
		relationName: 'displayAuthorContent'
	})
}));

export const aiContentReviewsRelations = relations(aiContentReviews, ({ one }) => ({
	targetUser: one(users, {
		fields: [aiContentReviews.targetUserId],
		references: [users.id],
		relationName: 'aiContentTargetUser'
	}),
	reviewedBy: one(users, {
		fields: [aiContentReviews.reviewedById],
		references: [users.id],
		relationName: 'aiContentReviewer'
	}),
	createdBy: one(users, {
		fields: [aiContentReviews.createdById],
		references: [users.id],
		relationName: 'aiContentCreator'
	})
}));

export const aiGenerationMetricsRelations = relations(aiGenerationMetrics, ({ one }) => ({
	user: one(users, {
		fields: [aiGenerationMetrics.userId],
		references: [users.id],
		relationName: 'aiMetricsUser'
	})
}));

export const user = sqliteTable('user', {
	id: integer('id').primaryKey(),
	age: integer('age')
});
