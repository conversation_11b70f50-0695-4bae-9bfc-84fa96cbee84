import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq, or } from 'drizzle-orm';
import { hashPassword } from '$lib/server/auth';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Username validation regex (alphanumeric, underscore, hyphen, 3-20 chars)
const USERNAME_REGEX = /^[a-zA-Z0-9_-]{3,20}$/;

// Password validation (at least 8 chars, 1 uppercase, 1 lowercase, 1 number)
const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;

// POST /api/auth/register - Register a new user
export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.username || !body.displayName || !body.email || !body.password || !body.confirmPassword) {
      return json({
        success: false,
        error: 'All required fields must be filled'
      }, { status: 400 });
    }

    // Validate field formats
    if (!USERNAME_REGEX.test(body.username)) {
      return json({
        success: false,
        error: 'Username must be 3-20 characters and contain only letters, numbers, underscores, and hyphens'
      }, { status: 400 });
    }

    if (!EMAIL_REGEX.test(body.email)) {
      return json({
        success: false,
        error: 'Please enter a valid email address'
      }, { status: 400 });
    }

    if (!PASSWORD_REGEX.test(body.password)) {
      return json({
        success: false,
        error: 'Password must be at least 8 characters with uppercase, lowercase, and number'
      }, { status: 400 });
    }

    if (body.password !== body.confirmPassword) {
      return json({
        success: false,
        error: 'Passwords do not match'
      }, { status: 400 });
    }

    // Validate display name length
    if (body.displayName.length < 2 || body.displayName.length > 50) {
      return json({
        success: false,
        error: 'Display name must be between 2 and 50 characters'
      }, { status: 400 });
    }

    // Validate terms acceptance
    if (!body.acceptTerms) {
      return json({
        success: false,
        error: 'You must accept the terms of service'
      }, { status: 400 });
    }

    // Validate CAPTCHA if provided
    if (body.captchaAnswer !== undefined && body.captchaExpected !== undefined) {
      if (parseInt(body.captchaAnswer) !== parseInt(body.captchaExpected)) {
        return json({
          success: false,
          error: 'CAPTCHA answer is incorrect'
        }, { status: 400 });
      }
    }

    // Check if username or email already exists
    const existingUser = await db.select()
      .from(users)
      .where(or(eq(users.username, body.username), eq(users.email, body.email)))
      .limit(1);

    if (existingUser.length > 0) {
      const existing = existingUser[0];
      if (existing.username === body.username) {
        return json({
          success: false,
          error: 'Username is already taken'
        }, { status: 409 });
      } else {
        return json({
          success: false,
          error: 'Email address is already registered'
        }, { status: 409 });
      }
    }

    // Hash the password properly
    const passwordHash = hashPassword(body.password);

    // Insert new user into the database
    const result = await db.insert(users).values({
      username: body.username,
      displayName: body.displayName,
      email: body.email,
      passwordHash,
      role: 'user', // Default role
      preferences: {
        highContrast: false,
        largeText: false,
        simplifiedInterface: false
      }
    }).returning();

    // Return user data (excluding sensitive information)
    const { passwordHash: _, ...userData } = result[0];

    logger.info('New user registered', {
      username: body.username,
      email: body.email,
      userId: userData.id
    });

    return json({
      success: true,
      data: userData,
      message: 'Registration successful! Welcome to FWFC!'
    }, { status: 201 });
  } catch (error) {
    logger.error('Error registering user:', error);
    return json({
      success: false,
      error: 'Failed to register user. Please try again.'
    }, { status: 500 });
  }
};
