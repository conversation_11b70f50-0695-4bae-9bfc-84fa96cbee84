<script>
  import { onMount, onDestroy } from 'svelte';
  import logger from '$lib/client/logger';
  
  // Props
  export let fallback = null;
  
  // State
  let error = null;
  let errorInfo = null;
  
  // Error handler
  function handleError(event) {
    error = event.error || new Error('Unknown error occurred');
    errorInfo = {
      componentStack: event.componentStack || '',
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    };
    
    // Log the error
    logger.error('Error caught by boundary', errorInfo);
    
    // Prevent the error from propagating
    event.preventDefault();
  }
  
  // Set up error handling
  onMount(() => {
    window.addEventListener('error', handleError);
    return () => {
      window.removeEventListener('error', handleError);
    };
  });
</script>

{#if error}
  {#if fallback}
    <svelte:component this={fallback} {error} {errorInfo} />
  {:else}
    <div class="error-boundary">
      <h2>Something went wrong</h2>
      <p>We're sorry, but an error occurred while rendering this component.</p>
      <details>
        <summary>Technical Details</summary>
        <pre>{error.message}</pre>
        {#if errorInfo?.componentStack}
          <pre>{errorInfo.componentStack}</pre>
        {/if}
      </details>
      <button on:click={() => window.location.reload()}>Reload Page</button>
    </div>
  {/if}
{:else}
  <slot />
{/if}

<style>
  .error-boundary {
    padding: 2rem;
    margin: 1rem 0;
    background-color: #ffebee;
    border: 1px solid #ffcdd2;
    border-radius: 4px;
  }
  
  h2 {
    color: #c62828;
    margin-top: 0;
  }
  
  details {
    margin: 1rem 0;
  }
  
  summary {
    cursor: pointer;
    color: #c62828;
    font-weight: bold;
  }
  
  pre {
    background-color: #f5f5f5;
    padding: 1rem;
    overflow: auto;
    border-radius: 4px;
    margin: 0.5rem 0;
  }
  
  button {
    background-color: #c62828;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
  }
</style>
