import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { messageOfTheDay } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/motd - Get a random active message of the day
export const GET: RequestHandler = async () => {
  try {
    // Fetch active messages of the day from the database
    const messages = await db.select()
      .from(messageOfTheDay)
      .where(eq(messageOfTheDay.active, true));
    
    if (messages.length === 0) {
      return json({
        success: false,
        error: 'No active messages found'
      }, { status: 404 });
    }
    
    // Select a random message
    const randomIndex = Math.floor(Math.random() * messages.length);
    const message = messages[randomIndex];
    
    return json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error fetching message of the day:', error);
    return json({
      success: false,
      error: 'Failed to fetch message of the day'
    }, { status: 500 });
  }
};

// POST /api/motd - Create a new message of the day (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.content) {
      return json({
        success: false,
        error: 'Content is required'
      }, { status: 400 });
    }
    
    // Insert new message into the database
    const result = await db.insert(messageOfTheDay).values({
      content: body.content,
      active: body.active !== undefined ? body.active : true
    }).returning();
    
    return json({
      success: true,
      data: result[0]
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating message of the day:', error);
    return json({
      success: false,
      error: 'Failed to create message of the day'
    }, { status: 500 });
  }
};
