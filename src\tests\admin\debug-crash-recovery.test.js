/**
 * Manual Test Script for Debug Crash Recovery System
 * 
 * This script can be run in the browser console to test the debug system
 * and simulate various error conditions.
 */

// Test 1: Verify debug logging persistence
function testDebugPersistence() {
    console.log('🧪 Testing debug persistence...');
    
    // Check if debug logs are being saved
    const stored = localStorage.getItem('simulate-interactions-debug-log');
    if (stored) {
        const data = JSON.parse(stored);
        console.log('✅ Debug logs found in localStorage:', data.logs.length, 'entries');
        console.log('📊 Last 5 entries:', data.logs.slice(-5));
    } else {
        console.log('❌ No debug logs found in localStorage');
    }
}

// Test 2: Simulate reactivity error
function simulateReactivityError() {
    console.log('🧪 Simulating reactivity error...');
    
    try {
        // Create a fake error that matches the reactivity error pattern
        const fakeError = new Error('effect_update_depth_exceeded: Maximum update depth exceeded. This can happen when a reactive block or effect repeatedly sets a new value.');
        fakeError.stack = `Error: effect_update_depth_exceeded
    at reactive_statement (simulate-interactions:123:45)
    at update_component (svelte:456:78)
    at flush (svelte:789:12)`;
        
        // Trigger the error handler
        window.dispatchEvent(new ErrorEvent('error', {
            error: fakeError,
            message: fakeError.message,
            filename: 'simulate-interactions',
            lineno: 123,
            colno: 45
        }));
        
        console.log('✅ Reactivity error simulated');
    } catch (e) {
        console.error('❌ Failed to simulate error:', e);
    }
}

// Test 3: Check error recovery data
function checkErrorRecovery() {
    console.log('🧪 Checking error recovery data...');
    
    const lastError = localStorage.getItem('simulate-interactions-last-error');
    if (lastError) {
        const errorData = JSON.parse(lastError);
        console.log('✅ Last error data found:', errorData);
        console.log('📊 Error context:', errorData.context);
        console.log('📊 Error time:', errorData.timestamp);
        console.log('📊 Debug logs at error:', errorData.debugLogs.length, 'entries');
    } else {
        console.log('❌ No error recovery data found');
    }
    
    const crashContext = localStorage.getItem('simulate-interactions-crash-context');
    if (crashContext) {
        const contextData = JSON.parse(crashContext);
        console.log('✅ Crash context found:', contextData);
        console.log('📊 State at crash:', contextData.state);
    } else {
        console.log('❌ No crash context found');
    }
}

// Test 4: Monitor reactive calls in real-time
function monitorReactiveCalls() {
    console.log('🧪 Starting reactive call monitoring...');
    
    let reactiveCallCount = 0;
    let lastCallTime = 0;
    const callHistory = [];
    
    // Override console.log to monitor reactive calls
    const originalLog = console.log;
    console.log = function(...args) {
        const now = performance.now();
        
        if (args[0] && args[0].includes('REACTIVE:')) {
            reactiveCallCount++;
            const timeDiff = now - lastCallTime;
            
            callHistory.push({
                call: args[0],
                time: now,
                timeDiff: timeDiff
            });
            
            // Keep only last 20 calls
            if (callHistory.length > 20) {
                callHistory.shift();
            }
            
            // Check for rapid calls
            if (timeDiff < 10 && reactiveCallCount > 5) {
                console.error('🚨 RAPID REACTIVE CALLS DETECTED:', {
                    count: reactiveCallCount,
                    timeDiff: timeDiff,
                    recentCalls: callHistory.slice(-5)
                });
            }
            
            lastCallTime = now;
            
            // Reset counter if calls are spaced out
            if (timeDiff > 1000) {
                reactiveCallCount = 0;
            }
        }
        
        originalLog.apply(console, args);
    };
    
    console.log('✅ Reactive call monitoring active');
    
    // Return function to stop monitoring
    return () => {
        console.log = originalLog;
        console.log('🛑 Reactive call monitoring stopped');
        console.log('📊 Final call history:', callHistory);
    };
}

// Test 5: Stress test the component
function stressTestComponent() {
    console.log('🧪 Starting component stress test...');
    
    // Rapidly change content type
    const contentTypeSelect = document.getElementById('content-type');
    if (contentTypeSelect) {
        let changeCount = 0;
        const maxChanges = 20;
        
        const rapidChange = () => {
            if (changeCount < maxChanges) {
                contentTypeSelect.value = changeCount % 2 === 0 ? 'gallery' : 'news';
                contentTypeSelect.dispatchEvent(new Event('change'));
                changeCount++;
                setTimeout(rapidChange, 50); // Change every 50ms
            } else {
                console.log('✅ Stress test completed:', maxChanges, 'changes');
            }
        };
        
        rapidChange();
    } else {
        console.log('❌ Content type select not found');
    }
}

// Test 6: Export all debug data
function exportAllDebugData() {
    console.log('🧪 Exporting all debug data...');
    
    const debugData = {
        timestamp: new Date().toISOString(),
        debugLogs: JSON.parse(localStorage.getItem('simulate-interactions-debug-log') || '{}'),
        lastError: JSON.parse(localStorage.getItem('simulate-interactions-last-error') || 'null'),
        crashContext: JSON.parse(localStorage.getItem('simulate-interactions-crash-context') || 'null'),
        currentState: {
            url: window.location.href,
            userAgent: navigator.userAgent,
            timestamp: Date.now()
        }
    };
    
    console.log('📊 Complete debug data export:', debugData);
    
    // Create downloadable file
    const blob = new Blob([JSON.stringify(debugData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `simulate-interactions-debug-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    console.log('✅ Debug data exported to file');
}

// Test 7: Clear all debug data
function clearAllDebugData() {
    console.log('🧪 Clearing all debug data...');
    
    localStorage.removeItem('simulate-interactions-debug-log');
    localStorage.removeItem('simulate-interactions-last-error');
    localStorage.removeItem('simulate-interactions-crash-context');
    
    console.log('✅ All debug data cleared');
}

// Main test runner
function runAllTests() {
    console.log('🚀 Running all debug system tests...');
    
    testDebugPersistence();
    checkErrorRecovery();
    
    // Start monitoring
    const stopMonitoring = monitorReactiveCalls();
    
    // Run stress test after a delay
    setTimeout(() => {
        stressTestComponent();
        
        // Stop monitoring after stress test
        setTimeout(() => {
            stopMonitoring();
            exportAllDebugData();
        }, 2000);
    }, 1000);
}

// Make functions available globally
window.debugTests = {
    testDebugPersistence,
    simulateReactivityError,
    checkErrorRecovery,
    monitorReactiveCalls,
    stressTestComponent,
    exportAllDebugData,
    clearAllDebugData,
    runAllTests
};

console.log('🧪 Debug test functions loaded. Run window.debugTests.runAllTests() to start all tests.');
console.log('Available functions:', Object.keys(window.debugTests));
