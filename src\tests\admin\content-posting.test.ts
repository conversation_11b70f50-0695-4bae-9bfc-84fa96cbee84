import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { db } from '$lib/server/db';
import { users, news, gallery, comments, contentAuthorship, auditLogs } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';

describe('Content Posting as User System', () => {
  let adminUserId: number;
  let simulatedUserId: number;
  let newsId: number;
  let galleryId: number;

  beforeEach(async () => {
    // Create test admin user
    const adminResult = await db.insert(users).values({
      username: 'testadmin',
      displayName: 'Test Admin',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
      role: 'admin',
      status: 'active',
      isSimulated: false
    }).returning();
    adminUserId = adminResult[0].id;

    // Create test simulated user
    const simulatedResult = await db.insert(users).values({
      username: 'simulateduser',
      displayName: 'Simulated User',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
      role: 'user',
      status: 'active',
      isSimulated: true,
      simulatedPersonality: JSON.stringify({
        traits: ['friendly', 'enthusiastic'],
        writingStyle: 'casual'
      })
    }).returning();
    simulatedUserId = simulatedResult[0].id;
  });

  afterEach(async () => {
    // Clean up test data
    await db.delete(contentAuthorship).where(eq(contentAuthorship.actualAuthorId, adminUserId));
    await db.delete(comments).where(eq(comments.userId, simulatedUserId));
    if (newsId) await db.delete(news).where(eq(news.id, newsId));
    if (galleryId) await db.delete(gallery).where(eq(gallery.id, galleryId));
    await db.delete(auditLogs).where(eq(auditLogs.adminUserId, adminUserId));
    await db.delete(users).where(eq(users.id, simulatedUserId));
    await db.delete(users).where(eq(users.id, adminUserId));
  });

  describe('News Article Posting', () => {
    it('should create news article as simulated user', async () => {
      const newsResult = await db.insert(news).values({
        title: 'Test News Article',
        content: 'This is a test news article content.',
        imageUrl: 'https://example.com/image.jpg',
        authorId: simulatedUserId,
        published: true
      }).returning();

      newsId = newsResult[0].id;

      // Record content authorship
      await db.insert(contentAuthorship).values({
        contentType: 'news',
        contentId: newsId,
        actualAuthorId: adminUserId,
        displayAuthorId: simulatedUserId,
        isSimulated: true
      });

      const article = newsResult[0];
      expect(article.title).toBe('Test News Article');
      expect(article.authorId).toBe(simulatedUserId);
      expect(article.published).toBe(true);

      // Verify authorship tracking
      const authorship = await db.select()
        .from(contentAuthorship)
        .where(eq(contentAuthorship.contentId, newsId))
        .limit(1);

      expect(authorship[0]).toBeDefined();
      expect(authorship[0].actualAuthorId).toBe(adminUserId);
      expect(authorship[0].displayAuthorId).toBe(simulatedUserId);
      expect(authorship[0].isSimulated).toBe(true);
    });

    it('should update user last active time when posting', async () => {
      const beforeTime = new Date().toISOString();

      const newsResult = await db.insert(news).values({
        title: 'Another Test Article',
        content: 'Content for testing activity tracking.',
        authorId: simulatedUserId,
        published: true
      }).returning();

      newsId = newsResult[0].id;

      // Update user's last active time
      const afterTime = new Date().toISOString();
      await db.update(users)
        .set({ lastActiveAt: afterTime })
        .where(eq(users.id, simulatedUserId));

      const user = await db.select()
        .from(users)
        .where(eq(users.id, simulatedUserId))
        .limit(1);

      expect(user[0].lastActiveAt).toBe(afterTime);
      expect(new Date(user[0].lastActiveAt!).getTime()).toBeGreaterThan(new Date(beforeTime).getTime());
    });
  });

  describe('Gallery Item Posting', () => {
    it('should create gallery item as simulated user', async () => {
      const galleryResult = await db.insert(gallery).values({
        title: 'Test Gallery Item',
        description: 'This is a test gallery item.',
        imageUrl: 'https://example.com/gallery.jpg',
        thumbnailUrl: 'https://example.com/thumb.jpg',
        authorId: simulatedUserId,
        published: true
      }).returning();

      galleryId = galleryResult[0].id;

      // Record content authorship
      await db.insert(contentAuthorship).values({
        contentType: 'gallery',
        contentId: galleryId,
        actualAuthorId: adminUserId,
        displayAuthorId: simulatedUserId,
        isSimulated: true
      });

      const item = galleryResult[0];
      expect(item.title).toBe('Test Gallery Item');
      expect(item.authorId).toBe(simulatedUserId);
      expect(item.published).toBe(true);
      expect(item.imageUrl).toBe('https://example.com/gallery.jpg');

      // Verify authorship tracking
      const authorship = await db.select()
        .from(contentAuthorship)
        .where(eq(contentAuthorship.contentId, galleryId))
        .limit(1);

      expect(authorship[0]).toBeDefined();
      expect(authorship[0].contentType).toBe('gallery');
      expect(authorship[0].isSimulated).toBe(true);
    });
  });

  describe('Comment Posting', () => {
    beforeEach(async () => {
      // Create a news article to comment on
      const newsResult = await db.insert(news).values({
        title: 'Article for Comments',
        content: 'Content to comment on.',
        authorId: adminUserId,
        published: true
      }).returning();
      newsId = newsResult[0].id;
    });

    it('should create comment as simulated user', async () => {
      const commentResult = await db.insert(comments).values({
        userId: simulatedUserId,
        content: 'This is a test comment from a simulated user.',
        itemType: 'news',
        itemId: newsId,
        approved: true
      }).returning();

      // Record content authorship
      await db.insert(contentAuthorship).values({
        contentType: 'comment',
        contentId: commentResult[0].id,
        actualAuthorId: adminUserId,
        displayAuthorId: simulatedUserId,
        isSimulated: true
      });

      const comment = commentResult[0];
      expect(comment.content).toBe('This is a test comment from a simulated user.');
      expect(comment.userId).toBe(simulatedUserId);
      expect(comment.itemType).toBe('news');
      expect(comment.itemId).toBe(newsId);
      expect(comment.approved).toBe(true);

      // Verify authorship tracking
      const authorship = await db.select()
        .from(contentAuthorship)
        .where(eq(contentAuthorship.contentId, comment.id))
        .limit(1);

      expect(authorship[0]).toBeDefined();
      expect(authorship[0].contentType).toBe('comment');
    });

    it('should auto-approve admin-created comments', async () => {
      const commentResult = await db.insert(comments).values({
        userId: simulatedUserId,
        content: 'Auto-approved comment.',
        itemType: 'news',
        itemId: newsId,
        approved: true // Admin-created comments are auto-approved
      }).returning();

      expect(commentResult[0].approved).toBe(true);
    });
  });

  describe('Content Authorship Tracking', () => {
    it('should track actual vs display author', async () => {
      const newsResult = await db.insert(news).values({
        title: 'Authorship Test Article',
        content: 'Testing authorship tracking.',
        authorId: simulatedUserId,
        published: true
      }).returning();

      newsId = newsResult[0].id;

      await db.insert(contentAuthorship).values({
        contentType: 'news',
        contentId: newsId,
        actualAuthorId: adminUserId,
        displayAuthorId: simulatedUserId,
        isSimulated: true
      });

      const authorship = await db.select()
        .from(contentAuthorship)
        .where(eq(contentAuthorship.contentId, newsId))
        .limit(1);

      expect(authorship[0].actualAuthorId).toBe(adminUserId);
      expect(authorship[0].displayAuthorId).toBe(simulatedUserId);
      expect(authorship[0].isSimulated).toBe(true);
    });

    it('should distinguish between real and simulated content', async () => {
      // Create real content
      const realNewsResult = await db.insert(news).values({
        title: 'Real User Article',
        content: 'Content by real user.',
        authorId: adminUserId,
        published: true
      }).returning();

      // Create simulated content
      const simulatedNewsResult = await db.insert(news).values({
        title: 'Simulated User Article',
        content: 'Content by simulated user.',
        authorId: simulatedUserId,
        published: true
      }).returning();

      newsId = simulatedNewsResult[0].id;

      // Only track authorship for simulated content
      await db.insert(contentAuthorship).values({
        contentType: 'news',
        contentId: simulatedNewsResult[0].id,
        actualAuthorId: adminUserId,
        displayAuthorId: simulatedUserId,
        isSimulated: true
      });

      const simulatedAuthorship = await db.select()
        .from(contentAuthorship)
        .where(eq(contentAuthorship.contentId, simulatedNewsResult[0].id))
        .limit(1);

      const realAuthorship = await db.select()
        .from(contentAuthorship)
        .where(eq(contentAuthorship.contentId, realNewsResult[0].id))
        .limit(1);

      expect(simulatedAuthorship.length).toBe(1);
      expect(realAuthorship.length).toBe(0); // No authorship tracking for real content

      // Clean up
      await db.delete(news).where(eq(news.id, realNewsResult[0].id));
    });
  });

  describe('Content Validation', () => {
    it('should validate required fields for news articles', async () => {
      // This would typically be handled by the API validation
      // Here we test that the database accepts valid data
      const validNews = {
        title: 'Valid Title',
        content: 'Valid content',
        authorId: simulatedUserId,
        published: true
      };

      const result = await db.insert(news).values(validNews).returning();
      newsId = result[0].id;

      expect(result[0].title).toBe(validNews.title);
      expect(result[0].content).toBe(validNews.content);
      expect(result[0].authorId).toBe(validNews.authorId);
    });

    it('should handle optional fields correctly', async () => {
      const newsWithOptionals = {
        title: 'Article with Optionals',
        content: 'Content with optional fields.',
        imageUrl: 'https://example.com/optional.jpg',
        authorId: simulatedUserId,
        published: false
      };

      const result = await db.insert(news).values(newsWithOptionals).returning();
      newsId = result[0].id;

      expect(result[0].imageUrl).toBe(newsWithOptionals.imageUrl);
      expect(result[0].published).toBe(false);
    });
  });
});
