const Database = require('better-sqlite3');
const path = require('path');

// Connect to the database
const dbPath = path.join(__dirname, '..', 'local.db');
const db = new Database(dbPath);

console.log('Seeding gallery with sample images...');

try {
  // Check if gallery table exists
  const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='gallery'").get();
  
  if (!tableExists) {
    console.log('Gallery table does not exist. Please run migrations first.');
    process.exit(1);
  }

  // Check if there are already published gallery images
  const existingPublished = db.prepare('SELECT COUNT(*) as count FROM gallery WHERE published = 1').get();
  
  if (existingPublished.count > 0) {
    console.log(`Found ${existingPublished.count} published gallery images. Skipping seed.`);
    process.exit(0);
  }

  // Get admin user ID
  const adminUser = db.prepare('SELECT id FROM users WHERE role = "admin" LIMIT 1').get();
  
  if (!adminUser) {
    console.log('No admin user found. Please create an admin user first.');
    process.exit(1);
  }

  const adminId = adminUser.id;

  // Sample gallery images
  const galleryImages = [
    {
      title: 'On the Set of Stranger Things',
      description: 'Finn Wolfhard with the cast of Stranger Things during filming of Season 4.',
      imageUrl: '/images/gallery-1.jpg',
      thumbnailUrl: '/images/gallery-1-thumb.jpg',
      published: true
    },
    {
      title: 'The Aubreys Live Performance',
      description: 'Finn performing with his band The Aubreys at a live concert in Los Angeles.',
      imageUrl: '/images/gallery-2.jpg',
      thumbnailUrl: '/images/gallery-2-thumb.jpg',
      published: true
    },
    {
      title: 'IT Chapter Two Premiere',
      description: 'Finn at the premiere of IT Chapter Two with the cast.',
      imageUrl: '/images/gallery-3.jpg',
      thumbnailUrl: '/images/gallery-3-thumb.jpg',
      published: true
    },
    {
      title: 'Ghostbusters: Afterlife Behind the Scenes',
      description: 'Behind the scenes photo from the filming of Ghostbusters: Afterlife.',
      imageUrl: '/images/gallery-4.jpg',
      thumbnailUrl: '/images/gallery-4-thumb.jpg',
      published: true
    },
    {
      title: 'Comic Con Panel',
      description: 'Finn speaking at a Comic Con panel about his upcoming projects.',
      imageUrl: '/images/gallery-5.jpg',
      thumbnailUrl: '/images/gallery-5-thumb.jpg',
      published: true
    },
    {
      title: 'Magazine Photoshoot',
      description: 'Finn during a photoshoot for GQ Magazine.',
      imageUrl: '/images/gallery-6.jpg',
      thumbnailUrl: '/images/gallery-6-thumb.jpg',
      published: true
    },
    {
      title: 'Directing Debut',
      description: 'Finn on the set of his directorial debut "Night Shifts".',
      imageUrl: '/images/gallery-7.jpg',
      thumbnailUrl: '/images/gallery-7-thumb.jpg',
      published: true
    },
    {
      title: 'Award Ceremony',
      description: 'Finn accepting an award with the cast of Stranger Things.',
      imageUrl: '/images/gallery-8.jpg',
      thumbnailUrl: '/images/gallery-8-thumb.jpg',
      published: true
    },
    {
      title: 'Draft Image - Studio Session',
      description: 'Finn in the recording studio working on new music. This is a draft image.',
      imageUrl: '/images/gallery-9.jpg',
      thumbnailUrl: '/images/gallery-9-thumb.jpg',
      published: false // This one is unpublished for testing
    }
  ];

  // Insert gallery images
  const insertStmt = db.prepare(`
    INSERT INTO gallery (title, description, image_url, thumbnail_url, author_id, published, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  `);

  let insertedCount = 0;
  
  for (const image of galleryImages) {
    try {
      insertStmt.run(
        image.title,
        image.description,
        image.imageUrl,
        image.thumbnailUrl,
        adminId,
        image.published ? 1 : 0
      );
      insertedCount++;
      console.log(`✓ Created gallery image: ${image.title} (${image.published ? 'published' : 'draft'})`);
    } catch (error) {
      console.error(`✗ Failed to create gallery image: ${image.title}`, error.message);
    }
  }

  console.log(`\n✅ Successfully created ${insertedCount} gallery images!`);

  // Show summary
  const totalImages = db.prepare('SELECT COUNT(*) as count FROM gallery').get();
  const publishedImages = db.prepare('SELECT COUNT(*) as count FROM gallery WHERE published = 1').get();
  const draftImages = db.prepare('SELECT COUNT(*) as count FROM gallery WHERE published = 0').get();

  console.log('\n📊 Gallery Summary:');
  console.log(`- Total images: ${totalImages.count}`);
  console.log(`- Published images: ${publishedImages.count}`);
  console.log(`- Draft images: ${draftImages.count}`);

  console.log('\n🔗 You can now access individual gallery images at:');
  console.log('- http://localhost:5173/gallery/1');
  console.log('- http://localhost:5173/gallery/2');
  console.log('- etc...');

} catch (error) {
  console.error('Error seeding gallery:', error);
} finally {
  db.close();
  console.log('\nGallery seeding complete.');
}
