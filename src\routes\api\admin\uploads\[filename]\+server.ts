import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import fs from 'fs';
import path from 'path';

// DELETE /api/admin/uploads/[filename] - Delete an uploaded file (admin only)
export const DELETE: RequestHandler = async ({ params, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    const filename = params.filename;
    
    if (!filename) {
      return json({
        success: false,
        error: 'Filename is required'
      }, { status: 400 });
    }

    // Validate filename to prevent directory traversal
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return json({
        success: false,
        error: 'Invalid filename'
      }, { status: 400 });
    }

    const UPLOAD_DIR = path.resolve('static/uploads/gallery');
    const THUMBNAIL_DIR = path.join(UPLOAD_DIR, 'thumbnails');
    
    const filePath = path.join(UPLOAD_DIR, filename);
    const thumbnailPath = path.join(THUMBNAIL_DIR, filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return json({
        success: false,
        error: 'File not found'
      }, { status: 404 });
    }

    // Delete the main file
    fs.unlinkSync(filePath);

    // Delete the thumbnail if it exists
    if (fs.existsSync(thumbnailPath)) {
      fs.unlinkSync(thumbnailPath);
    }

    return json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting file:', error);
    return json({
      success: false,
      error: 'Failed to delete file'
    }, { status: 500 });
  }
};
