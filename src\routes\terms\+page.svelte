<svelte:head>
	<title>Terms of Service - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Terms of Service for the Finn Wolfhard Fan Club website" />
</svelte:head>

<div class="terms-container">
	<div class="terms-content">
		<header class="terms-header">
			<h1>Terms of Service</h1>
			<p class="last-updated">Last updated: {new Date().toLocaleDateString()}</p>
		</header>

		<div class="terms-body">
			<section>
				<h2>1. Acceptance of Terms</h2>
				<p>
					By accessing and using the Finn Wolfhard Fan Club (FWFC) website, you accept and agree to be bound by the terms and provision of this agreement.
				</p>
			</section>

			<section>
				<h2>2. Community Guidelines</h2>
				<p>
					FWFC is a community for fans to connect and share their appreciation for Finn Wolfhard's work. We expect all members to:
				</p>
				<ul>
					<li>Be respectful and kind to other community members</li>
					<li>Share content that is appropriate and relevant to the community</li>
					<li>Respect privacy and not share personal information of others</li>
					<li>Follow all applicable laws and regulations</li>
					<li>Not engage in harassment, bullying, or discriminatory behavior</li>
				</ul>
			</section>

			<section>
				<h2>3. User Content</h2>
				<p>
					Users may post content including messages, images, and comments. By posting content, you grant FWFC a non-exclusive license to use, display, and distribute your content on the platform. You are responsible for ensuring your content does not violate any laws or third-party rights.
				</p>
			</section>

			<section>
				<h2>4. Privacy and Data Protection</h2>
				<p>
					We are committed to protecting your privacy. Please review our <a href="/privacy">Privacy Policy</a> to understand how we collect, use, and protect your information.
				</p>
			</section>

			<section>
				<h2>5. Account Responsibilities</h2>
				<p>
					You are responsible for:
				</p>
				<ul>
					<li>Maintaining the security of your account credentials</li>
					<li>All activities that occur under your account</li>
					<li>Notifying us immediately of any unauthorized use</li>
					<li>Providing accurate and up-to-date information</li>
				</ul>
			</section>

			<section>
				<h2>6. Prohibited Activities</h2>
				<p>
					The following activities are strictly prohibited:
				</p>
				<ul>
					<li>Posting spam, malicious content, or inappropriate material</li>
					<li>Attempting to hack, disrupt, or damage the website</li>
					<li>Impersonating others or creating fake accounts</li>
					<li>Violating intellectual property rights</li>
					<li>Engaging in commercial activities without permission</li>
				</ul>
			</section>

			<section>
				<h2>7. Moderation and Enforcement</h2>
				<p>
					FWFC reserves the right to moderate content and enforce these terms. We may remove content, suspend accounts, or take other actions as necessary to maintain a safe and welcoming community.
				</p>
			</section>

			<section>
				<h2>8. Disclaimer</h2>
				<p>
					FWFC is an unofficial fan community and is not affiliated with Finn Wolfhard, his representatives, or any official entities. The website is provided "as is" without warranties of any kind.
				</p>
			</section>

			<section>
				<h2>9. Changes to Terms</h2>
				<p>
					We may update these terms from time to time. Users will be notified of significant changes, and continued use of the website constitutes acceptance of the updated terms.
				</p>
			</section>

			<section>
				<h2>10. Contact Information</h2>
				<p>
					If you have questions about these terms, please contact us at:
					<a href="mailto:<EMAIL>"><EMAIL></a>
				</p>
			</section>
		</div>

		<div class="terms-footer">
			<p>
				<a href="/register">← Back to Registration</a> | 
				<a href="/privacy">Privacy Policy</a> | 
				<a href="/">Home</a>
			</p>
		</div>
	</div>
</div>

<style>
	.terms-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem 1rem;
	}

	.terms-content {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 12px;
		box-shadow: 0 4px 6px var(--theme-shadow);
		padding: 2.5rem;
		color: var(--theme-text-primary);
	}

	.terms-header {
		text-align: center;
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 2px solid var(--theme-border);
	}

	.terms-header h1 {
		margin: 0 0 0.5rem 0;
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--theme-text-primary);
	}

	.last-updated {
		margin: 0;
		font-size: 0.9rem;
		color: var(--theme-text-muted);
		font-style: italic;
	}

	.terms-body {
		line-height: 1.6;
	}

	.terms-body section {
		margin-bottom: 2rem;
	}

	.terms-body h2 {
		margin: 0 0 1rem 0;
		font-size: 1.5rem;
		font-weight: 600;
		color: var(--theme-text-primary);
		border-left: 4px solid var(--theme-accent-primary);
		padding-left: 1rem;
	}

	.terms-body p {
		margin: 0 0 1rem 0;
		color: var(--theme-text-secondary);
	}

	.terms-body ul {
		margin: 0 0 1rem 0;
		padding-left: 1.5rem;
	}

	.terms-body li {
		margin-bottom: 0.5rem;
		color: var(--theme-text-secondary);
	}

	.terms-body a {
		color: var(--theme-accent-primary);
		text-decoration: none;
		font-weight: 600;
	}

	.terms-body a:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	.terms-footer {
		margin-top: 2rem;
		padding-top: 1rem;
		border-top: 1px solid var(--theme-border);
		text-align: center;
	}

	.terms-footer p {
		margin: 0;
		color: var(--theme-text-secondary);
	}

	.terms-footer a {
		color: var(--theme-accent-primary);
		text-decoration: none;
		font-weight: 600;
	}

	.terms-footer a:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	/* Responsive Design */
	@media (max-width: 640px) {
		.terms-container {
			padding: 1rem;
		}

		.terms-content {
			padding: 1.5rem;
		}

		.terms-header h1 {
			font-size: 2rem;
		}

		.terms-body h2 {
			font-size: 1.3rem;
		}
	}

	/* High Contrast Mode */
	:global(.high-contrast) .terms-content {
		border-width: 3px;
	}

	:global(.high-contrast) .terms-body h2 {
		border-left-width: 6px;
	}

	/* Large Text Mode */
	:global(.large-text) .terms-header h1 {
		font-size: 3rem;
	}

	:global(.large-text) .terms-body h2 {
		font-size: 1.8rem;
	}

	:global(.large-text) .terms-body p,
	:global(.large-text) .terms-body li {
		font-size: 1.2rem;
	}

	/* Focus Management */
	.terms-body a:focus,
	.terms-footer a:focus {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
		border-radius: 4px;
	}
</style>
