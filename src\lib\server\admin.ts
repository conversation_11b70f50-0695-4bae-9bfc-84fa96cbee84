import AdminJS from 'adminjs';
import { Database, Resource } from '@adminjs/sql';
import { db } from './db';
import * as schema from './db/schema';
import { authenticateUser } from './auth';

// Register the SQLite adapter
AdminJS.registerAdapter({ Database, Resource });

// Create AdminJS instance
export const adminJs = new AdminJS({
  databases: [db],
  rootPath: '/admin',
  branding: {
    companyName: 'Finn Wolfhard Fan Club Admin',
    logo: '/images/logo.png',
    favicon: '/favicon.png',
    withMadeWithLove: false,
  },
  dashboard: {
    component: AdminJS.bundle('./admin/dashboard-component'),
  },
  pages: {
    'Message of the Day': {
      component: AdminJS.bundle('./admin/motd-component'),
    },
    'Site Settings': {
      component: AdminJS.bundle('./admin/settings-component'),
    },
  },
  locale: {
    translations: {
      labels: {
        navigation: 'Navigation',
        users: 'Users',
        news: 'News',
        gallery: 'Gallery',
        messages: 'Messages',
        replies: 'Replies',
        comments: 'Comments',
        siteSettings: 'Site Settings',
        messageOfTheDay: 'Message of the Day',
      },
    },
  },
});

// Authentication for AdminJS
export const adminJsAuth = {
  authenticate: async (email: string, password: string) => {
    const user = await authenticateUser(email, password);
    if (user && (user.role === 'admin' || user.role === 'moderator')) {
      return {
        email: user.email,
        role: user.role,
      };
    }
    return null;
  },
  cookieName: 'adminjs',
  cookiePassword: 'secret-password-for-cookies', // In production, use a secure value from env vars
};
