import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// GET /api/users/me - Get the current user's profile
export const GET: RequestHandler = async ({ locals }) => {
  try {
    // Check if user is authenticated
    if (!locals.user) {
      return json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }
    
    // Return user data (excluding sensitive information)
    const { passwordHash, ...userData } = locals.user;
    
    return json({
      success: true,
      data: userData
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return json({
      success: false,
      error: 'Failed to fetch user profile'
    }, { status: 500 });
  }
};

// PUT /api/users/me - Update the current user's profile
export const PUT: RequestHandler = async ({ request, locals }) => {
  try {
    // Check if user is authenticated
    if (!locals.user) {
      return json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }
    
    const body = await request.json();
    
    // Update user data in the database
    // In a real app, you'd update the user in the database here
    
    return json({
      success: true,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    return json({
      success: false,
      error: 'Failed to update user profile'
    }, { status: 500 });
  }
};
