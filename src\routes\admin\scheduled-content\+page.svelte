<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	// State
	let scheduledItems: any[] = [];
	let isLoading = false;
	let error = '';
	let successMessage = '';
	let selectedItems = new Set<number>();

	// Filters
	let statusFilter = '';
	let contentTypeFilter = '';
	let userFilter = '';
	let dateFromFilter = '';
	let dateToFilter = '';

	// Pagination
	let currentPage = 1;
	let itemsPerPage = 20;
	let totalItems = 0;
	let totalPages = 0;

	// Filter options
	const statusOptions = [
		{ value: '', label: 'All Statuses' },
		{ value: 'pending', label: 'Pending' },
		{ value: 'published', label: 'Published' },
		{ value: 'failed', label: 'Failed' },
		{ value: 'cancelled', label: 'Cancelled' }
	];

	const contentTypeOptions = [
		{ value: '', label: 'All Types' },
		{ value: 'news', label: 'News Articles' },
		{ value: 'gallery', label: 'Gallery Items' },
		{ value: 'comment', label: 'Comments' },
		{ value: 'message', label: 'Messages' }
	];

	// Load scheduled content
	async function loadScheduledContent() {
		isLoading = true;
		error = ''; // Clear error immediately without timeout

		try {
			const params = new URLSearchParams({
				page: currentPage.toString(),
				limit: itemsPerPage.toString(),
				status: statusFilter,
				contentType: contentTypeFilter,
				user: userFilter,
				...(dateFromFilter && { dateFrom: dateFromFilter }),
				...(dateToFilter && { dateTo: dateToFilter })
			});

			const response = await fetch(`/api/admin/scheduled-content?${params}`);
			const result = await response.json();

			if (response.ok && result.success) {
				scheduledItems = result.data.items;
				totalItems = result.data.total;
				totalPages = Math.ceil(totalItems / itemsPerPage);
			} else {
				setErrorMessage(result.error || 'Failed to load scheduled content');
			}
		} catch (err) {
			console.error('Error loading scheduled content:', err);
			setErrorMessage('Failed to load scheduled content');
		} finally {
			isLoading = false;
		}
	}

	// Handle filter changes
	function handleFilterChange() {
		currentPage = 1;
		selectedItems.clear();
		loadScheduledContent();
	}

	// Handle page change
	function changePage(page: number) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
			loadScheduledContent();
		}
	}

	// Toggle item selection
	function toggleItemSelection(itemId: number) {
		if (selectedItems.has(itemId)) {
			selectedItems.delete(itemId);
		} else {
			selectedItems.add(itemId);
		}
		selectedItems = selectedItems; // Trigger reactivity
	}

	// Select all items
	function selectAllItems(event: Event) {
		const target = event.target as HTMLInputElement;
		if (target.checked) {
			scheduledItems.forEach(item => selectedItems.add(item.id));
		} else {
			selectedItems.clear();
		}
		selectedItems = selectedItems; // Trigger reactivity
	}

	// Cancel scheduled item
	async function cancelScheduledItem(itemId: number) {
		if (!confirm('Are you sure you want to cancel this scheduled item?')) {
			return;
		}

		try {
			const response = await fetch(`/api/admin/scheduled-content/${itemId}`, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ status: 'cancelled' })
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccessMessage('Scheduled item cancelled successfully');
				loadScheduledContent();
			} else {
				setErrorMessage(result.error || 'Failed to cancel scheduled item');
			}
		} catch (err) {
			console.error('Error cancelling scheduled item:', err);
			setErrorMessage('Failed to cancel scheduled item');
		}
	}

	// Publish scheduled item immediately
	async function publishNow(itemId: number) {
		if (!confirm('Are you sure you want to publish this item immediately?')) {
			return;
		}

		try {
			const response = await fetch(`/api/admin/scheduled-content/${itemId}/publish`, {
				method: 'POST'
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccessMessage('Item published successfully');
				loadScheduledContent();
			} else {
				setErrorMessage(result.error || 'Failed to publish item');
			}
		} catch (err) {
			console.error('Error publishing item:', err);
			setErrorMessage('Failed to publish item');
		}
	}

	// Bulk operations
	async function bulkCancel() {
		if (selectedItems.size === 0) {
			setErrorMessage('Please select items to cancel');
			return;
		}

		if (!confirm(`Are you sure you want to cancel ${selectedItems.size} scheduled items?`)) {
			return;
		}

		try {
			const response = await fetch('/api/admin/scheduled-content/bulk', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					action: 'cancel',
					itemIds: Array.from(selectedItems)
				})
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccessMessage(`${selectedItems.size} items cancelled successfully`);
				selectedItems.clear();
				loadScheduledContent();
			} else {
				setErrorMessage(result.error || 'Failed to cancel items');
			}
		} catch (err) {
			console.error('Error cancelling items:', err);
			setErrorMessage('Failed to cancel items');
		}
	}

	// Format date for display
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// Get status badge class
	function getStatusBadgeClass(status: string): string {
		switch (status) {
			case 'pending': return 'status-pending';
			case 'published': return 'status-published';
			case 'failed': return 'status-failed';
			case 'cancelled': return 'status-cancelled';
			default: return 'status-pending';
		}
	}

	// Get content preview
	function getContentPreview(item: any): string {
		try {
			const contentData = JSON.parse(item.contentData);
			return contentData.title || contentData.content?.substring(0, 100) || 'No preview available';
		} catch {
			return 'Invalid content data';
		}
	}

	// Initialize
	onMount(() => {
		loadScheduledContent();
	});

	// Clear messages after delays - using proper effect management
	let successMessageTimeout: NodeJS.Timeout | null = null;
	let errorMessageTimeout: NodeJS.Timeout | null = null;

	// Function to set success message with auto-clear
	function setSuccessMessage(message: string) {
		successMessage = message;
		if (successMessageTimeout) {
			clearTimeout(successMessageTimeout);
		}
		successMessageTimeout = setTimeout(() => {
			successMessage = '';
			successMessageTimeout = null;
		}, 5000);
	}

	// Function to set error message with auto-clear
	function setErrorMessage(message: string) {
		error = message;
		if (errorMessageTimeout) {
			clearTimeout(errorMessageTimeout);
		}
		errorMessageTimeout = setTimeout(() => {
			error = '';
			errorMessageTimeout = null;
		}, 8000);
	}
</script>

<svelte:head>
	<title>Scheduled Content - FWFC Admin</title>
	<meta name="description" content="Manage scheduled content posts" />
</svelte:head>

<div class="scheduled-content">
	<header class="page-header">
		<div class="header-content">
			<h1>Scheduled Content</h1>
			<p class="header-subtitle">
				Manage and monitor scheduled content posts
			</p>
		</div>
		<div class="header-actions">
			<button
				class="btn primary"
				onclick={() => goto('/admin/post-as-user')}
				disabled={isLoading}
			>
				➕ Schedule New Content
			</button>
			<button
				class="btn secondary"
				onclick={() => goto('/admin')}
				disabled={isLoading}
			>
				← Back to Admin
			</button>
		</div>
	</header>

	<!-- Success/Error Messages -->
	{#if successMessage}
		<div class="message success" role="alert" aria-live="polite">
			✅ {successMessage}
		</div>
	{/if}

	{#if error}
		<ErrorMessage
			title="Error"
			message={error}
			type="error"
			dismissible={true}
			onDismiss={() => {
				error = '';
				if (errorMessageTimeout) {
					clearTimeout(errorMessageTimeout);
					errorMessageTimeout = null;
				}
			}}
		/>
	{/if}

	<!-- Filters -->
	<div class="filters-section">
		<div class="filters-grid">
			<div class="filter-group">
				<label for="status-filter">Status:</label>
				<select
					id="status-filter"
					bind:value={statusFilter}
					onchange={handleFilterChange}
					disabled={isLoading}
				>
					{#each statusOptions as option}
						<option value={option.value}>{option.label}</option>
					{/each}
				</select>
			</div>

			<div class="filter-group">
				<label for="content-type-filter">Content Type:</label>
				<select
					id="content-type-filter"
					bind:value={contentTypeFilter}
					onchange={handleFilterChange}
					disabled={isLoading}
				>
					{#each contentTypeOptions as option}
						<option value={option.value}>{option.label}</option>
					{/each}
				</select>
			</div>

			<div class="filter-group">
				<label for="date-from-filter">From Date:</label>
				<input
					type="datetime-local"
					id="date-from-filter"
					bind:value={dateFromFilter}
					onchange={handleFilterChange}
					disabled={isLoading}
				/>
			</div>

			<div class="filter-group">
				<label for="date-to-filter">To Date:</label>
				<input
					type="datetime-local"
					id="date-to-filter"
					bind:value={dateToFilter}
					onchange={handleFilterChange}
					disabled={isLoading}
				/>
			</div>
		</div>
	</div>

	<!-- Bulk Actions -->
	{#if selectedItems.size > 0}
		<div class="bulk-actions">
			<span class="selection-count">{selectedItems.size} items selected</span>
			<button
				class="btn danger"
				onclick={bulkCancel}
				disabled={isLoading}
			>
				Cancel Selected
			</button>
		</div>
	{/if}

	<!-- Content Table -->
	<div class="table-container">
		{#if isLoading}
			<div class="loading-container">
				<LoadingSpinner />
				<p>Loading scheduled content...</p>
			</div>
		{:else if scheduledItems.length === 0}
			<div class="empty-state">
				<h3>No scheduled content found</h3>
				<p>There are no scheduled items matching your current filters.</p>
				<button
					class="btn primary"
					onclick={() => goto('/admin/post-as-user')}
				>
					Schedule New Content
				</button>
			</div>
		{:else}
			<table class="scheduled-table">
				<thead>
					<tr>
						<th scope="col">
							<input
								type="checkbox"
								checked={selectedItems.size === scheduledItems.length && scheduledItems.length > 0}
								onchange={selectAllItems}
								aria-label="Select all items"
							/>
						</th>
						<th scope="col">Content</th>
						<th scope="col">Type</th>
						<th scope="col">User</th>
						<th scope="col">Scheduled For</th>
						<th scope="col">Status</th>
						<th scope="col">Created</th>
						<th scope="col">Actions</th>
					</tr>
				</thead>
				<tbody>
					{#each scheduledItems as item}
						<tr class="item-row" class:selected={selectedItems.has(item.id)}>
							<td>
								<input
									type="checkbox"
									checked={selectedItems.has(item.id)}
									onchange={() => toggleItemSelection(item.id)}
									aria-label="Select item {item.id}"
								/>
							</td>
							<td class="content-cell">
								<div class="content-preview">
									<strong>{item.contentType}</strong>
									<p>{getContentPreview(item)}</p>
								</div>
							</td>
							<td>
								<span class="type-badge type-{item.contentType}">
									{item.contentType}
								</span>
							</td>
							<td class="user-cell">
								<div class="user-info">
									<strong>{item.asUser?.displayName || 'Unknown'}</strong>
									<small>@{item.asUser?.username || 'unknown'}</small>
								</div>
							</td>
							<td class="date-cell">
								{formatDate(item.scheduledFor)}
							</td>
							<td>
								<span class="status-badge {getStatusBadgeClass(item.status)}">
									{item.status}
								</span>
							</td>
							<td class="date-cell">
								{formatDate(item.createdAt)}
							</td>
							<td class="actions-cell">
								<div class="action-buttons">
									{#if item.status === 'pending'}
										<button
											class="btn-small primary"
											onclick={() => publishNow(item.id)}
											title="Publish now"
										>
											📤
										</button>
										<button
											class="btn-small danger"
											onclick={() => cancelScheduledItem(item.id)}
											title="Cancel"
										>
											❌
										</button>
									{:else if item.status === 'failed'}
										<button
											class="btn-small primary"
											onclick={() => publishNow(item.id)}
											title="Retry publishing"
										>
											🔄
										</button>
									{/if}
								</div>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>

			<!-- Pagination -->
			{#if totalPages > 1}
				<div class="pagination">
					<button
						class="btn secondary"
						onclick={() => changePage(currentPage - 1)}
						disabled={currentPage === 1 || isLoading}
					>
						← Previous
					</button>

					<span class="page-info">
						Page {currentPage} of {totalPages} ({totalItems} total items)
					</span>

					<button
						class="btn secondary"
						onclick={() => changePage(currentPage + 1)}
						disabled={currentPage === totalPages || isLoading}
					>
						Next →
					</button>
				</div>
			{/if}
		{/if}
	</div>
</div>

<style>
	.scheduled-content {
		padding: 2rem;
		max-width: 1400px;
		margin: 0 auto;
		color: var(--theme-text-primary);
	}

	/* Page Header */
	.page-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 2px solid var(--theme-border);
	}

	.header-content h1 {
		margin: 0 0 0.5rem 0;
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--theme-text-primary);
	}

	.header-subtitle {
		margin: 0;
		font-size: 1.1rem;
		color: var(--theme-text-secondary);
		line-height: 1.5;
	}

	.header-actions {
		display: flex;
		gap: 1rem;
		flex-wrap: wrap;
	}

	/* Messages */
	.message {
		padding: 1rem;
		border-radius: 8px;
		margin-bottom: 1.5rem;
		font-weight: 600;
	}

	.message.success {
		background-color: var(--theme-accent-success, #d4edda);
		color: var(--theme-accent-success-text, #155724);
		border: 1px solid var(--theme-accent-success-border, #c3e6cb);
	}

	/* Filters */
	.filters-section {
		margin-bottom: 2rem;
		padding: 1.5rem;
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		background-color: var(--theme-card-bg);
	}

	.filters-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1rem;
	}

	.filter-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.filter-group label {
		font-weight: 600;
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.filter-group select,
	.filter-group input {
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 1rem;
	}

	/* Bulk Actions */
	.bulk-actions {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		margin-bottom: 1rem;
		background-color: var(--theme-accent-info-light, #e8f4fd);
		border: 1px solid var(--theme-accent-info, #17a2b8);
		border-radius: 6px;
	}

	.selection-count {
		font-weight: 600;
		color: var(--theme-accent-info, #17a2b8);
	}

	/* Buttons */
	.btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		border: 2px solid var(--theme-border);
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border-color: var(--theme-accent-primary);
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
		transform: translateY(-1px);
	}

	.btn.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border-color: var(--theme-border);
	}

	.btn.secondary:hover:not(:disabled) {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-1px);
	}

	.btn.danger {
		background-color: var(--theme-accent-danger);
		color: white;
		border-color: var(--theme-accent-danger);
	}

	.btn.danger:hover:not(:disabled) {
		background-color: var(--theme-accent-danger-hover);
		border-color: var(--theme-accent-danger-hover);
		transform: translateY(-1px);
	}

	/* Table Styles */
	.table-container {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		overflow: hidden;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 4rem 2rem;
		gap: 1rem;
	}

	.loading-container p {
		color: var(--theme-text-secondary);
		font-size: 1.1rem;
	}

	.empty-state {
		text-align: center;
		padding: 4rem 2rem;
	}

	.empty-state h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
	}

	.empty-state p {
		margin: 0 0 2rem 0;
		color: var(--theme-text-secondary);
		font-size: 1.1rem;
	}

	.scheduled-table {
		width: 100%;
		border-collapse: collapse;
		font-size: 0.9rem;
	}

	.scheduled-table th,
	.scheduled-table td {
		padding: 1rem;
		text-align: left;
		border-bottom: 1px solid var(--theme-border);
	}

	.scheduled-table th {
		background-color: var(--theme-bg-secondary);
		font-weight: 600;
		color: var(--theme-text-primary);
		position: sticky;
		top: 0;
		z-index: 1;
	}

	.scheduled-table tr:hover {
		background-color: var(--theme-bg-tertiary);
	}

	.item-row.selected {
		background-color: var(--theme-accent-primary-light, #e8f5e8);
	}

	.content-cell {
		max-width: 300px;
	}

	.content-preview strong {
		display: block;
		color: var(--theme-text-primary);
		margin-bottom: 0.25rem;
		text-transform: capitalize;
	}

	.content-preview p {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 0.85rem;
		line-height: 1.4;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.type-badge {
		display: inline-block;
		padding: 0.25rem 0.75rem;
		border-radius: 12px;
		font-size: 0.8rem;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.type-news {
		background-color: var(--theme-accent-info, #17a2b8);
		color: white;
	}

	.type-gallery {
		background-color: var(--theme-accent-success, #28a745);
		color: white;
	}

	.type-comment {
		background-color: var(--theme-accent-warning, #ffc107);
		color: #212529;
	}

	.type-message {
		background-color: var(--theme-accent-secondary, #6c757d);
		color: white;
	}

	.status-badge {
		display: inline-block;
		padding: 0.25rem 0.75rem;
		border-radius: 12px;
		font-size: 0.8rem;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.status-pending {
		background-color: var(--theme-accent-warning, #ffc107);
		color: #212529;
	}

	.status-published {
		background-color: var(--theme-accent-success, #28a745);
		color: white;
	}

	.status-failed {
		background-color: var(--theme-accent-danger, #dc3545);
		color: white;
	}

	.status-cancelled {
		background-color: var(--theme-accent-secondary, #6c757d);
		color: white;
	}

	.user-cell .user-info strong {
		display: block;
		color: var(--theme-text-primary);
	}

	.user-cell .user-info small {
		color: var(--theme-text-secondary);
		font-size: 0.8rem;
	}

	.date-cell {
		font-size: 0.85rem;
		color: var(--theme-text-secondary);
		white-space: nowrap;
	}

	.actions-cell {
		width: 120px;
	}

	.action-buttons {
		display: flex;
		gap: 0.5rem;
		justify-content: center;
	}

	.btn-small {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		width: 32px;
		height: 32px;
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		font-size: 0.8rem;
		cursor: pointer;
		transition: all 0.2s ease;
		background-color: var(--theme-button-bg);
	}

	.btn-small.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border-color: var(--theme-accent-primary);
	}

	.btn-small.danger {
		background-color: var(--theme-accent-danger);
		color: white;
		border-color: var(--theme-accent-danger);
	}

	.btn-small:hover {
		transform: scale(1.1);
	}

	/* Pagination */
	.pagination {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		border-top: 1px solid var(--theme-border);
		background-color: var(--theme-bg-secondary);
	}

	.page-info {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.scheduled-content {
			padding: 1rem;
		}

		.page-header {
			flex-direction: column;
			align-items: stretch;
			gap: 1rem;
		}

		.header-actions {
			justify-content: flex-start;
		}

		.filters-grid {
			grid-template-columns: 1fr;
		}

		.bulk-actions {
			flex-direction: column;
			gap: 1rem;
			text-align: center;
		}
	}
</style>
