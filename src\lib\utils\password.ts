// Password strength checking utilities

export interface PasswordStrength {
  score: number; // 0-4 (very weak to very strong)
  label: string;
  color: string;
  feedback: string[];
  requirements: PasswordRequirement[];
}

export interface PasswordRequirement {
  label: string;
  met: boolean;
  description: string;
}

/**
 * Calculate password strength score and provide feedback
 */
export function calculatePasswordStrength(password: string): PasswordStrength {
  if (!password) {
    return {
      score: 0,
      label: 'Enter a password',
      color: '#6c757d',
      feedback: ['Password is required'],
      requirements: getPasswordRequirements('')
    };
  }

  let score = 0;
  const feedback: string[] = [];
  const requirements = getPasswordRequirements(password);

  // Length check
  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Use at least 8 characters');
  }

  // Character variety checks
  const hasLowercase = /[a-z]/.test(password);
  const hasUppercase = /[A-Z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

  if (hasLowercase) score += 0.5;
  if (hasUppercase) score += 0.5;
  if (hasNumbers) score += 0.5;
  if (hasSpecialChars) score += 0.5;

  // Additional strength factors
  if (password.length >= 12) {
    score += 0.5;
  }

  if (password.length >= 16) {
    score += 0.5;
  }

  // Check for common patterns (reduce score)
  if (hasCommonPatterns(password)) {
    score = Math.max(0, score - 1);
    feedback.push('Avoid common patterns like "123" or "abc"');
  }

  // Check for repeated characters
  if (hasRepeatedCharacters(password)) {
    score = Math.max(0, score - 0.5);
    feedback.push('Avoid repeating characters');
  }

  // Normalize score to 0-4 range
  score = Math.min(4, Math.max(0, Math.round(score)));

  // Generate label and color based on score
  const { label, color } = getStrengthLabel(score);

  // Add positive feedback for strong passwords
  if (score >= 3) {
    if (feedback.length === 0) {
      feedback.push('Strong password!');
    }
  } else if (score >= 2) {
    if (feedback.length === 0) {
      feedback.push('Good password, but could be stronger');
    }
  }

  return {
    score,
    label,
    color,
    feedback,
    requirements
  };
}

/**
 * Get password requirements with their status
 */
function getPasswordRequirements(password: string): PasswordRequirement[] {
  return [
    {
      label: 'At least 8 characters',
      met: password.length >= 8,
      description: 'Minimum length requirement'
    },
    {
      label: 'Contains lowercase letter',
      met: /[a-z]/.test(password),
      description: 'Include at least one lowercase letter (a-z)'
    },
    {
      label: 'Contains uppercase letter',
      met: /[A-Z]/.test(password),
      description: 'Include at least one uppercase letter (A-Z)'
    },
    {
      label: 'Contains number',
      met: /\d/.test(password),
      description: 'Include at least one number (0-9)'
    },
    {
      label: 'Contains special character (optional)',
      met: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
      description: 'Special characters make your password stronger'
    }
  ];
}

/**
 * Get strength label and color based on score
 */
function getStrengthLabel(score: number): { label: string; color: string } {
  switch (score) {
    case 0:
      return { label: 'Very Weak', color: '#dc3545' };
    case 1:
      return { label: 'Weak', color: '#fd7e14' };
    case 2:
      return { label: 'Fair', color: '#ffc107' };
    case 3:
      return { label: 'Good', color: '#20c997' };
    case 4:
      return { label: 'Strong', color: '#28a745' };
    default:
      return { label: 'Unknown', color: '#6c757d' };
  }
}

/**
 * Check for common patterns in password
 */
function hasCommonPatterns(password: string): boolean {
  const commonPatterns = [
    /123/,
    /abc/,
    /qwe/,
    /asd/,
    /zxc/,
    /password/i,
    /admin/i,
    /user/i,
    /login/i,
    /000/,
    /111/,
    /222/,
    /333/,
    /444/,
    /555/,
    /666/,
    /777/,
    /888/,
    /999/
  ];

  return commonPatterns.some(pattern => pattern.test(password));
}

/**
 * Check for repeated characters (3 or more in a row)
 */
function hasRepeatedCharacters(password: string): boolean {
  return /(.)\1{2,}/.test(password);
}

/**
 * Generate a random secure password
 */
export function generateSecurePassword(length: number = 12): string {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const special = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  const allChars = lowercase + uppercase + numbers + special;
  
  let password = '';
  
  // Ensure at least one character from each category
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += special[Math.floor(Math.random() * special.length)];
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * Check if password meets minimum requirements
 */
export function meetsMinimumRequirements(password: string): boolean {
  const requirements = getPasswordRequirements(password);
  // Check first 4 requirements (excluding optional special characters)
  return requirements.slice(0, 4).every(req => req.met);
}

/**
 * Get password strength percentage for progress bars
 */
export function getPasswordStrengthPercentage(score: number): number {
  return (score / 4) * 100;
}
