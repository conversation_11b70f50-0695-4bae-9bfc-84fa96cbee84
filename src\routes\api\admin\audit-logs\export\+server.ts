import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { auditLogs, users } from '$lib/server/db/schema';
import { eq, and, gte, lte, desc, like, or } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// GET /api/admin/audit-logs/export - Export audit logs as CSV or JSON
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    // Parse query parameters
    const format = url.searchParams.get('format') || 'csv';
    const action = url.searchParams.get('action') || '';
    const adminUser = url.searchParams.get('adminUser') || '';
    const targetType = url.searchParams.get('targetType') || '';
    const dateFrom = url.searchParams.get('dateFrom');
    const dateTo = url.searchParams.get('dateTo');

    if (!['csv', 'json'].includes(format)) {
      return json({
        success: false,
        error: 'Invalid format. Must be csv or json'
      }, { status: 400 });
    }

    // Build conditions (same as main endpoint)
    const conditions: any[] = [];

    if (action) {
      conditions.push(like(auditLogs.action, `%${action}%`));
    }

    if (targetType) {
      conditions.push(eq(auditLogs.targetType, targetType));
    }

    if (dateFrom) {
      conditions.push(gte(auditLogs.createdAt, dateFrom));
    }
    if (dateTo) {
      conditions.push(lte(auditLogs.createdAt, dateTo));
    }

    // Build the query
    let query = db.select({
      id: auditLogs.id,
      adminUserId: auditLogs.adminUserId,
      action: auditLogs.action,
      targetType: auditLogs.targetType,
      targetId: auditLogs.targetId,
      targetUserId: auditLogs.targetUserId,
      details: auditLogs.details,
      ipAddress: auditLogs.ipAddress,
      userAgent: auditLogs.userAgent,
      createdAt: auditLogs.createdAt,
      adminUser: {
        id: users.id,
        username: users.username,
        displayName: users.displayName
      }
    })
    .from(auditLogs)
    .leftJoin(users, eq(auditLogs.adminUserId, users.id));

    // Apply conditions
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Apply admin user filter if specified
    if (adminUser) {
      query = query.where(
        or(
          like(users.username, `%${adminUser}%`),
          like(users.displayName, `%${adminUser}%`)
        )
      );
    }

    // Execute query (no pagination for export)
    const logs = await query.orderBy(desc(auditLogs.createdAt));

    // Get target user info for each log
    const logsWithTargetUsers = await Promise.all(
      logs.map(async (log) => {
        if (log.targetUserId) {
          const targetUser = await db.select({
            id: users.id,
            username: users.username,
            displayName: users.displayName
          })
          .from(users)
          .where(eq(users.id, log.targetUserId))
          .limit(1);

          return {
            ...log,
            targetUser: targetUser[0] || null
          };
        }
        return {
          ...log,
          targetUser: null
        };
      })
    );

    // Log the export action
    await db.insert(auditLogs).values({
      adminUserId: locals.user.id,
      action: 'export_audit_logs',
      targetType: 'audit_logs',
      targetId: null,
      targetUserId: null,
      details: JSON.stringify({
        format,
        filters: { action, adminUser, targetType, dateFrom, dateTo },
        recordCount: logsWithTargetUsers.length
      }),
      ipAddress: locals.clientAddress || 'unknown'
    });

    if (format === 'json') {
      // Return JSON format
      const jsonData = {
        exportedAt: new Date().toISOString(),
        exportedBy: {
          id: locals.user.id,
          username: locals.user.username,
          displayName: locals.user.displayName
        },
        filters: { action, adminUser, targetType, dateFrom, dateTo },
        totalRecords: logsWithTargetUsers.length,
        logs: logsWithTargetUsers.map(log => ({
          id: log.id,
          timestamp: log.createdAt,
          adminUser: log.adminUser ? {
            username: log.adminUser.username,
            displayName: log.adminUser.displayName
          } : null,
          action: log.action,
          targetType: log.targetType,
          targetId: log.targetId,
          targetUser: log.targetUser ? {
            username: log.targetUser.username,
            displayName: log.targetUser.displayName
          } : null,
          ipAddress: log.ipAddress,
          userAgent: log.userAgent,
          details: log.details ? JSON.parse(log.details) : null
        }))
      };

      return new Response(JSON.stringify(jsonData, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.json"`
        }
      });

    } else {
      // Return CSV format
      const csvHeaders = [
        'ID',
        'Timestamp',
        'Admin User',
        'Admin Display Name',
        'Action',
        'Target Type',
        'Target ID',
        'Target User',
        'Target Display Name',
        'IP Address',
        'User Agent',
        'Details'
      ];

      const csvRows = logsWithTargetUsers.map(log => [
        log.id,
        log.createdAt,
        log.adminUser?.username || '',
        log.adminUser?.displayName || '',
        log.action,
        log.targetType,
        log.targetId || '',
        log.targetUser?.username || '',
        log.targetUser?.displayName || '',
        log.ipAddress || '',
        log.userAgent || '',
        log.details ? JSON.stringify(JSON.parse(log.details)) : ''
      ]);

      const csvContent = [
        csvHeaders.join(','),
        ...csvRows.map(row => 
          row.map(field => 
            typeof field === 'string' && field.includes(',') 
              ? `"${field.replace(/"/g, '""')}"` 
              : field
          ).join(',')
        )
      ].join('\n');

      return new Response(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    }

  } catch (error) {
    logger.error('Error exporting audit logs:', error);
    return json({
      success: false,
      error: 'Failed to export audit logs'
    }, { status: 500 });
  }
};
