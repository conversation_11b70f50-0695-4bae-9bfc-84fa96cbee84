// Define log levels
const LogLevel = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
} as const;

type LogLevelType = keyof typeof LogLevel;

// Get current log level from environment or localStorage
const getCurrentLogLevel = (): number => {
  try {
    // Check if we're in the browser
    if (typeof window !== 'undefined' && window.localStorage) {
      const storedLevel = localStorage.getItem('logLevel');
      if (storedLevel && LogLevel[storedLevel as LogLevelType] !== undefined) {
        return LogLevel[storedLevel as LogLevelType];
      }
    }

    // In development, use DEBUG level by default
    if (import.meta.env.DEV) {
      return LogLevel.DEBUG;
    }

    // In production, use INFO level by default
    return LogLevel.INFO;
  } catch (e) {
    // If localStorage is not available or any other error occurs
    return import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.INFO;
  }
};

let currentLogLevel = getCurrentLogLevel();

// Format log message with timestamp
const formatMessage = (message: string): string => {
  const now = new Date();
  const timestamp = now.toISOString();
  return `[${timestamp}] ${message}`;
};

// Report errors to server
async function reportErrorToServer(message: string, data?: any): Promise<void> {
  try {
    // Only report in production or if explicitly enabled
    if (!import.meta.env.DEV || localStorage.getItem('enableErrorReporting') === 'true') {
      await fetch('/api/logs/error', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          data,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
        }),
      });
    }
  } catch (e) {
    // Silently fail if we can't report the error
    console.error('Failed to report error to server:', e);
  }
}

// Logger functions
const logger = {
  error: (message: string, data?: any): void => {
    if (currentLogLevel >= LogLevel.ERROR) {
      console.error(`[ERROR] ${formatMessage(message)}`, data || '');

      // Send to server if it's an error
      reportErrorToServer(message, data);
    }
  },

  warn: (message: string, data?: any): void => {
    if (currentLogLevel >= LogLevel.WARN) {
      console.warn(`[WARN] ${formatMessage(message)}`, data || '');
    }
  },

  info: (message: string, data?: any): void => {
    if (currentLogLevel >= LogLevel.INFO) {
      console.info(`[INFO] ${formatMessage(message)}`, data || '');
    }
  },

  debug: (message: string, data?: any): void => {
    if (currentLogLevel >= LogLevel.DEBUG) {
      console.debug(`[DEBUG] ${formatMessage(message)}`, data || '');
    }
  },

  // Set log level
  setLevel: (level: LogLevelType): void => {
    currentLogLevel = LogLevel[level];

    // Store in localStorage if available
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('logLevel', level);
      }
    } catch (e) {
      // Ignore localStorage errors
    }
  },

  // Get current log level
  getLevel: (): LogLevelType => {
    return Object.keys(LogLevel).find(
      key => LogLevel[key as LogLevelType] === currentLogLevel
    ) as LogLevelType;
  },

  // Create a logger with context
  withContext: (context: string) => ({
    error: (message: string, data?: any): void => {
      logger.error(`[${context}] ${message}`, data);
    },

    warn: (message: string, data?: any): void => {
      logger.warn(`[${context}] ${message}`, data);
    },

    info: (message: string, data?: any): void => {
      logger.info(`[${context}] ${message}`, data);
    },

    debug: (message: string, data?: any): void => {
      logger.debug(`[${context}] ${message}`, data);
    },
  }),
};

export default logger;