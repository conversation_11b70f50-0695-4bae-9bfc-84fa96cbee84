import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { scheduledContent, users, auditLogs } from '$lib/server/db/schema';
import { eq, and, gte, lte, desc, asc, like, or } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// GET /api/admin/scheduled-content - Get scheduled content with filters and pagination
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    // Parse query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const status = url.searchParams.get('status') || '';
    const contentType = url.searchParams.get('contentType') || '';
    const user = url.searchParams.get('user') || '';
    const dateFrom = url.searchParams.get('dateFrom');
    const dateTo = url.searchParams.get('dateTo');

    const offset = (page - 1) * limit;

    // Build conditions
    const conditions: any[] = [];

    // Filter by status
    if (status && ['pending', 'published', 'failed', 'cancelled'].includes(status)) {
      conditions.push(eq(scheduledContent.status, status as any));
    }

    // Filter by content type
    if (contentType && ['news', 'gallery', 'comment', 'message'].includes(contentType)) {
      conditions.push(eq(scheduledContent.contentType, contentType as any));
    }

    // Filter by date range
    if (dateFrom) {
      conditions.push(gte(scheduledContent.scheduledFor, dateFrom));
    }
    if (dateTo) {
      conditions.push(lte(scheduledContent.scheduledFor, dateTo));
    }

    // Build the query with user join
    let query = db.select({
      id: scheduledContent.id,
      contentType: scheduledContent.contentType,
      contentData: scheduledContent.contentData,
      asUserId: scheduledContent.asUserId,
      scheduledFor: scheduledContent.scheduledFor,
      status: scheduledContent.status,
      publishedAt: scheduledContent.publishedAt,
      errorMessage: scheduledContent.errorMessage,
      createdAt: scheduledContent.createdAt,
      updatedAt: scheduledContent.updatedAt,
      asUser: {
        id: users.id,
        username: users.username,
        displayName: users.displayName,
        isSimulated: users.isSimulated
      }
    })
    .from(scheduledContent)
    .leftJoin(users, eq(scheduledContent.asUserId, users.id));

    // Apply conditions
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Apply user filter if specified
    if (user) {
      query = query.where(
        or(
          like(users.username, `%${user}%`),
          like(users.displayName, `%${user}%`)
        )
      );
    }

    // Get total count for pagination
    const countQuery = db.select({ count: scheduledContent.id })
      .from(scheduledContent)
      .leftJoin(users, eq(scheduledContent.asUserId, users.id));

    if (conditions.length > 0) {
      countQuery.where(and(...conditions));
    }

    if (user) {
      countQuery.where(
        or(
          like(users.username, `%${user}%`),
          like(users.displayName, `%${user}%`)
        )
      );
    }

    // Execute queries
    const [items, totalResult] = await Promise.all([
      query
        .orderBy(desc(scheduledContent.scheduledFor))
        .limit(limit)
        .offset(offset),
      countQuery
    ]);

    const total = totalResult.length;

    return json({
      success: true,
      data: {
        items,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    logger.error('Error fetching scheduled content:', error);
    return json({
      success: false,
      error: 'Failed to fetch scheduled content'
    }, { status: 500 });
  }
};

// PATCH /api/admin/scheduled-content - Bulk operations
export const PATCH: RequestHandler = async ({ request, locals, getClientAddress }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action, itemIds } = body;

    if (!action || !itemIds || !Array.isArray(itemIds)) {
      return json({
        success: false,
        error: 'Action and item IDs are required'
      }, { status: 400 });
    }

    let updateData: any = {};
    let actionDescription = '';

    switch (action) {
      case 'cancel':
        updateData = { 
          status: 'cancelled',
          updatedAt: new Date().toISOString()
        };
        actionDescription = 'cancelled';
        break;
      
      case 'retry':
        updateData = { 
          status: 'pending',
          errorMessage: null,
          updatedAt: new Date().toISOString()
        };
        actionDescription = 'retried';
        break;

      default:
        return json({
          success: false,
          error: 'Invalid action'
        }, { status: 400 });
    }

    // Update items
    const results = await Promise.all(
      itemIds.map(async (itemId: number) => {
        try {
          const result = await db.update(scheduledContent)
            .set(updateData)
            .where(eq(scheduledContent.id, itemId))
            .returning();

          // Log admin action
          await logAdminAction(
            locals.user.id,
            `bulk_${action}_scheduled_content`,
            'scheduled_content',
            itemId,
            null,
            { action, itemId },
            getClientAddress()
          );

          return result[0];
        } catch (error) {
          logger.error(`Error updating scheduled content item ${itemId}:`, error);
          return null;
        }
      })
    );

    const successCount = results.filter(r => r !== null).length;

    logger.info('Bulk scheduled content operation', {
      adminUser: locals.user.username,
      action,
      itemIds,
      successCount,
      totalCount: itemIds.length
    });

    return json({
      success: true,
      data: {
        successCount,
        totalCount: itemIds.length,
        results: results.filter(r => r !== null)
      },
      message: `${successCount} items ${actionDescription} successfully`
    });

  } catch (error) {
    logger.error('Error performing bulk scheduled content operation:', error);
    return json({
      success: false,
      error: 'Failed to perform bulk operation'
    }, { status: 500 });
  }
};

// Helper function to log admin actions
async function logAdminAction(
  adminUserId: number,
  action: string,
  targetType: string,
  targetId: number | null,
  targetUserId: number | null,
  details: any,
  ipAddress: string
) {
  try {
    await db.insert(auditLogs).values({
      adminUserId,
      action,
      targetType,
      targetId,
      targetUserId,
      details: JSON.stringify(details),
      ipAddress
    });
  } catch (error) {
    logger.error('Failed to log admin action:', error);
  }
}
