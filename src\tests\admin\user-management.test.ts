import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { db } from '$lib/server/db';
import { users, auditLogs } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';

describe('Enhanced User Management System', () => {
  let testUserId: number;
  let adminUserId: number;

  beforeEach(async () => {
    // Create test admin user
    const adminResult = await db.insert(users).values({
      username: 'testadmin',
      displayName: 'Test Admin',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
      role: 'admin',
      status: 'active',
      isSimulated: false
    }).returning();
    adminUserId = adminResult[0].id;

    // Create test user
    const userResult = await db.insert(users).values({
      username: 'testuser',
      displayName: 'Test User',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
      role: 'user',
      status: 'active',
      isSimulated: false,
      bio: 'Test user bio',
      location: 'Test City',
      interests: ['testing', 'development']
    }).returning();
    testUserId = userResult[0].id;
  });

  afterEach(async () => {
    // Clean up test data
    await db.delete(auditLogs).where(eq(auditLogs.adminUserId, adminUserId));
    await db.delete(users).where(eq(users.id, testUserId));
    await db.delete(users).where(eq(users.id, adminUserId));
  });

  describe('User Profile Management', () => {
    it('should create user with enhanced profile fields', async () => {
      const user = await db.select()
        .from(users)
        .where(eq(users.id, testUserId))
        .limit(1);

      expect(user[0]).toBeDefined();
      expect(user[0].bio).toBe('Test user bio');
      expect(user[0].location).toBe('Test City');
      expect(user[0].interests).toEqual(['testing', 'development']);
      expect(user[0].status).toBe('active');
      expect(user[0].isSimulated).toBe(false);
    });

    it('should update user profile fields', async () => {
      await db.update(users)
        .set({
          bio: 'Updated bio',
          location: 'New City',
          website: 'https://example.com',
          interests: ['updated', 'interests']
        })
        .where(eq(users.id, testUserId));

      const updatedUser = await db.select()
        .from(users)
        .where(eq(users.id, testUserId))
        .limit(1);

      expect(updatedUser[0].bio).toBe('Updated bio');
      expect(updatedUser[0].location).toBe('New City');
      expect(updatedUser[0].website).toBe('https://example.com');
      expect(updatedUser[0].interests).toEqual(['updated', 'interests']);
    });

    it('should handle user status changes', async () => {
      await db.update(users)
        .set({ status: 'suspended' })
        .where(eq(users.id, testUserId));

      const suspendedUser = await db.select()
        .from(users)
        .where(eq(users.id, testUserId))
        .limit(1);

      expect(suspendedUser[0].status).toBe('suspended');
    });
  });

  describe('Simulated User Management', () => {
    it('should create simulated user with personality', async () => {
      const simulatedUserResult = await db.insert(users).values({
        username: 'simulateduser',
        displayName: 'Simulated User',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword',
        role: 'user',
        status: 'active',
        isSimulated: true,
        simulatedPersonality: JSON.stringify({
          traits: ['friendly', 'enthusiastic'],
          interests: ['music', 'movies'],
          writingStyle: 'casual',
          activityLevel: 'medium'
        })
      }).returning();

      const simulatedUser = simulatedUserResult[0];
      expect(simulatedUser.isSimulated).toBe(true);
      expect(simulatedUser.simulatedPersonality).toBeDefined();

      const personality = JSON.parse(simulatedUser.simulatedPersonality!);
      expect(personality.traits).toEqual(['friendly', 'enthusiastic']);
      expect(personality.writingStyle).toBe('casual');

      // Clean up
      await db.delete(users).where(eq(users.id, simulatedUser.id));
    });

    it('should filter users by type (real vs simulated)', async () => {
      // Create a simulated user
      const simulatedUserResult = await db.insert(users).values({
        username: 'simulateduser2',
        displayName: 'Simulated User 2',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword',
        role: 'user',
        status: 'active',
        isSimulated: true
      }).returning();

      // Get real users
      const realUsers = await db.select()
        .from(users)
        .where(eq(users.isSimulated, false));

      // Get simulated users
      const simulatedUsers = await db.select()
        .from(users)
        .where(eq(users.isSimulated, true));

      expect(realUsers.length).toBeGreaterThan(0);
      expect(simulatedUsers.length).toBeGreaterThan(0);
      expect(realUsers.every(user => !user.isSimulated)).toBe(true);
      expect(simulatedUsers.every(user => user.isSimulated)).toBe(true);

      // Clean up
      await db.delete(users).where(eq(users.id, simulatedUserResult[0].id));
    });
  });

  describe('User Activity Tracking', () => {
    it('should update last active timestamp', async () => {
      const now = new Date().toISOString();
      
      await db.update(users)
        .set({ lastActiveAt: now })
        .where(eq(users.id, testUserId));

      const user = await db.select()
        .from(users)
        .where(eq(users.id, testUserId))
        .limit(1);

      expect(user[0].lastActiveAt).toBe(now);
    });

    it('should track user activity patterns', async () => {
      const timestamps = [
        new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
        new Date().toISOString() // now
      ];

      for (const timestamp of timestamps) {
        await db.update(users)
          .set({ lastActiveAt: timestamp })
          .where(eq(users.id, testUserId));

        const user = await db.select()
          .from(users)
          .where(eq(users.id, testUserId))
          .limit(1);

        expect(user[0].lastActiveAt).toBe(timestamp);
      }
    });
  });

  describe('User Search and Filtering', () => {
    it('should search users by username and display name', async () => {
      // This would typically be tested with the API endpoint
      // Here we test the basic database query functionality
      const usersByUsername = await db.select()
        .from(users)
        .where(eq(users.username, 'testuser'));

      const usersByDisplayName = await db.select()
        .from(users)
        .where(eq(users.displayName, 'Test User'));

      expect(usersByUsername.length).toBe(1);
      expect(usersByDisplayName.length).toBe(1);
      expect(usersByUsername[0].id).toBe(testUserId);
      expect(usersByDisplayName[0].id).toBe(testUserId);
    });

    it('should filter users by status', async () => {
      // Create users with different statuses
      const inactiveUserResult = await db.insert(users).values({
        username: 'inactiveuser',
        displayName: 'Inactive User',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword',
        role: 'user',
        status: 'inactive',
        isSimulated: false
      }).returning();

      const activeUsers = await db.select()
        .from(users)
        .where(eq(users.status, 'active'));

      const inactiveUsers = await db.select()
        .from(users)
        .where(eq(users.status, 'inactive'));

      expect(activeUsers.length).toBeGreaterThan(0);
      expect(inactiveUsers.length).toBe(1);
      expect(inactiveUsers[0].id).toBe(inactiveUserResult[0].id);

      // Clean up
      await db.delete(users).where(eq(users.id, inactiveUserResult[0].id));
    });
  });

  describe('Data Validation', () => {
    it('should validate user interests as JSON array', async () => {
      const validInterests = ['coding', 'music', 'sports'];
      
      await db.update(users)
        .set({ interests: validInterests })
        .where(eq(users.id, testUserId));

      const user = await db.select()
        .from(users)
        .where(eq(users.id, testUserId))
        .limit(1);

      expect(Array.isArray(user[0].interests)).toBe(true);
      expect(user[0].interests).toEqual(validInterests);
    });

    it('should validate simulated personality as JSON', async () => {
      const personality = {
        traits: ['friendly'],
        writingStyle: 'formal',
        activityLevel: 'high'
      };

      await db.update(users)
        .set({ 
          isSimulated: true,
          simulatedPersonality: JSON.stringify(personality)
        })
        .where(eq(users.id, testUserId));

      const user = await db.select()
        .from(users)
        .where(eq(users.id, testUserId))
        .limit(1);

      expect(user[0].isSimulated).toBe(true);
      expect(user[0].simulatedPersonality).toBeDefined();
      
      const parsedPersonality = JSON.parse(user[0].simulatedPersonality!);
      expect(parsedPersonality).toEqual(personality);
    });
  });
});
