<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import LoadingSpinner from '../LoadingSpinner.svelte';
	import ErrorMessage from '../ErrorMessage.svelte';

	const dispatch = createEventDispatcher();

	export let isOpen = false;
	export let user: any = null;

	// State
	let isLoading = false;
	let error = '';
	let successMessage = '';
	let resetType: 'link' | 'temp' = 'link';
	let resetResult: any = null;

	// Reset password
	async function resetPassword() {
		if (!user) return;

		isLoading = true;
		error = '';
		successMessage = '';
		resetResult = null;

		try {
			const response = await fetch(`/api/admin/users/${user.id}/reset-password`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ type: resetType })
			});

			const result = await response.json();

			if (response.ok && result.success) {
				resetResult = result.data;
				successMessage = result.data.message;
				dispatch('success', result.data);
			} else {
				error = result.error || 'Failed to reset password';
			}
		} catch (err) {
			console.error('Error resetting password:', err);
			error = 'An error occurred while resetting password';
		} finally {
			isLoading = false;
		}
	}

	// Copy to clipboard
	async function copyToClipboard(text: string) {
		try {
			await navigator.clipboard.writeText(text);
			successMessage = 'Copied to clipboard!';
		} catch (err) {
			console.error('Failed to copy to clipboard:', err);
			error = 'Failed to copy to clipboard';
		}
	}

	// Close modal
	function closeModal() {
		isOpen = false;
		error = '';
		successMessage = '';
		resetResult = null;
		dispatch('close');
	}

	// Handle escape key
	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			closeModal();
		}
	}
</script>

{#if isOpen && user}
	<div 
		class="modal-overlay" 
		onclick={closeModal}
		onkeydown={handleKeyDown}
		role="button"
		tabindex="0"
		aria-label="Close password reset modal"
	>
		<div 
			class="modal-content" 
			onclick={(e) => e.stopPropagation()}
			onkeydown={(e) => e.stopPropagation()}
			role="dialog" 
			aria-modal="true" 
			aria-labelledby="reset-modal-title"
			tabindex="-1"
		>
			<div class="modal-header">
				<h2 id="reset-modal-title">Reset Password for {user.username}</h2>
				<button 
					class="close-button"
					onclick={closeModal}
					aria-label="Close password reset modal"
					type="button"
				>
					×
				</button>
			</div>

			<div class="modal-body">
				{#if error}
					<ErrorMessage 
						title="Reset Error"
						message={error}
						type="error"
						dismissible={true}
						onDismiss={() => error = ''}
					/>
				{/if}

				{#if successMessage && !resetResult}
					<div class="success-message" role="alert" aria-live="polite">
						✅ {successMessage}
					</div>
				{/if}

				{#if !resetResult}
					<div class="reset-options">
						<h3>Choose Reset Method</h3>
						<p class="reset-description">
							Select how you want to reset the password for <strong>{user.displayName}</strong> ({user.email}):
						</p>

						<div class="option-group">
							<label class="option-label">
								<input 
									type="radio" 
									bind:group={resetType} 
									value="link"
									name="resetType"
								/>
								<div class="option-content">
									<h4>Send Reset Link</h4>
									<p>Generate a secure password reset link that expires in 24 hours. The user can use this link to set their own new password.</p>
									<div class="option-benefits">
										<span class="benefit">✓ More secure</span>
										<span class="benefit">✓ User controls new password</span>
										<span class="benefit">✓ Expires automatically</span>
									</div>
								</div>
							</label>

							<label class="option-label">
								<input 
									type="radio" 
									bind:group={resetType} 
									value="temp"
									name="resetType"
								/>
								<div class="option-content">
									<h4>Generate Temporary Password</h4>
									<p>Create a secure temporary password that you can share with the user. They should change it immediately after logging in.</p>
									<div class="option-benefits">
										<span class="benefit">✓ Immediate access</span>
										<span class="benefit">✓ No email required</span>
										<span class="benefit">✓ Admin controlled</span>
									</div>
								</div>
							</label>
						</div>
					</div>
				{:else}
					<div class="reset-result">
						{#if resetResult.type === 'link'}
							<div class="result-section">
								<h3>🔗 Password Reset Link Generated</h3>
								<p>A password reset link has been generated for <strong>{user.displayName}</strong>.</p>
								
								<div class="link-container">
									<label for="reset-link">Reset Link:</label>
									<div class="link-input-group">
										<input 
											type="text" 
											id="reset-link"
											value={resetResult.resetLink} 
											readonly 
											class="link-input"
										/>
										<button 
											class="copy-button"
											onclick={() => copyToClipboard(resetResult.resetLink)}
											type="button"
											aria-label="Copy reset link to clipboard"
										>
											📋 Copy
										</button>
									</div>
								</div>

								<div class="link-info">
									<p><strong>Expires:</strong> {new Date(resetResult.expires).toLocaleString()}</p>
									<p><strong>Instructions:</strong> Send this link to the user via secure communication. They can use it to set a new password.</p>
								</div>
							</div>
						{:else if resetResult.type === 'temporary'}
							<div class="result-section">
								<h3>🔑 Temporary Password Generated</h3>
								<p>A temporary password has been generated for <strong>{user.displayName}</strong>.</p>
								
								<div class="password-container">
									<label for="temp-password">Temporary Password:</label>
									<div class="password-input-group">
										<input 
											type="text" 
											id="temp-password"
											value={resetResult.tempPassword} 
											readonly 
											class="password-input"
										/>
										<button 
											class="copy-button"
											onclick={() => copyToClipboard(resetResult.tempPassword)}
											type="button"
											aria-label="Copy temporary password to clipboard"
										>
											📋 Copy
										</button>
									</div>
								</div>

								<div class="password-info">
									<p><strong>⚠️ Important:</strong> Share this password securely with the user and instruct them to change it immediately after logging in.</p>
									<p><strong>Security Note:</strong> This password has been set in the database and the user's old password is no longer valid.</p>
								</div>
							</div>
						{/if}

						{#if successMessage}
							<div class="success-message" role="alert" aria-live="polite">
								✅ {successMessage}
							</div>
						{/if}
					</div>
				{/if}
			</div>

			<div class="modal-footer">
				{#if !resetResult}
					<button 
						class="btn primary"
						onclick={resetPassword}
						disabled={isLoading}
						type="button"
					>
						{#if isLoading}
							<LoadingSpinner size="small" inline={true} message="Resetting..." />
						{:else}
							{resetType === 'link' ? 'Generate Reset Link' : 'Generate Temporary Password'}
						{/if}
					</button>
				{/if}
				<button 
					class="btn secondary"
					onclick={closeModal}
					type="button"
				>
					{resetResult ? 'Done' : 'Cancel'}
				</button>
			</div>
		</div>
	</div>
{/if}

<style>
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 1rem;
	}

	.modal-content {
		background: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 12px;
		width: 100%;
		max-width: 700px;
		max-height: 90vh;
		overflow: hidden;
		color: var(--theme-text-primary);
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1.5rem;
		border-bottom: 1px solid var(--theme-border);
		background-color: var(--theme-bg-secondary);
	}

	.modal-header h2 {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
	}

	.close-button {
		background: none;
		border: none;
		font-size: 2rem;
		cursor: pointer;
		color: var(--theme-text-secondary);
		padding: 0;
		width: 2rem;
		height: 2rem;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: color 0.2s ease;
		border-radius: 4px;
	}

	.close-button:hover {
		color: var(--theme-text-primary);
		background-color: var(--theme-bg-tertiary);
	}

	.modal-body {
		padding: 1.5rem;
		max-height: 60vh;
		overflow-y: auto;
	}

	.modal-footer {
		display: flex;
		justify-content: flex-end;
		gap: 1rem;
		padding: 1.5rem;
		border-top: 1px solid var(--theme-border);
		background-color: var(--theme-bg-secondary);
	}

	.success-message {
		padding: 1rem;
		background-color: var(--theme-accent-success, #d4edda);
		color: var(--theme-accent-success-text, #155724);
		border: 1px solid var(--theme-accent-success-border, #c3e6cb);
		border-radius: 8px;
		margin-bottom: 1.5rem;
		font-weight: 600;
	}

	.reset-options h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
	}

	.reset-description {
		margin: 0 0 1.5rem 0;
		color: var(--theme-text-secondary);
		line-height: 1.5;
	}

	.option-group {
		display: grid;
		gap: 1rem;
	}

	.option-label {
		display: flex;
		gap: 1rem;
		padding: 1.5rem;
		border: 2px solid var(--theme-border);
		border-radius: 8px;
		cursor: pointer;
		transition: all 0.2s ease;
		background-color: var(--theme-card-bg);
	}

	.option-label:hover {
		border-color: var(--theme-accent-primary);
		background-color: var(--theme-bg-tertiary);
	}

	.option-label:has(input:checked) {
		border-color: var(--theme-accent-primary);
		background-color: var(--theme-accent-primary-light, #e8f5e8);
	}

	.option-content {
		flex: 1;
	}

	.option-content h4 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
	}

	.option-content p {
		margin: 0 0 1rem 0;
		color: var(--theme-text-secondary);
		line-height: 1.4;
	}

	.option-benefits {
		display: flex;
		flex-wrap: wrap;
		gap: 0.5rem;
	}

	.benefit {
		font-size: 0.8rem;
		color: var(--theme-accent-success, #28a745);
		font-weight: 600;
	}

	.result-section h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.3rem;
	}

	.result-section p {
		margin: 0 0 1.5rem 0;
		color: var(--theme-text-secondary);
		line-height: 1.5;
	}

	.link-container,
	.password-container {
		margin-bottom: 1.5rem;
	}

	.link-container label,
	.password-container label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 600;
		color: var(--theme-text-primary);
	}

	.link-input-group,
	.password-input-group {
		display: flex;
		gap: 0.5rem;
	}

	.link-input,
	.password-input {
		flex: 1;
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-bg-tertiary);
		color: var(--theme-text-primary);
		font-family: monospace;
		font-size: 0.9rem;
	}

	.copy-button {
		padding: 0.75rem 1rem;
		background-color: var(--theme-accent-primary);
		color: white;
		border: none;
		border-radius: 6px;
		cursor: pointer;
		font-weight: 600;
		transition: background-color 0.2s ease;
		white-space: nowrap;
	}

	.copy-button:hover {
		background-color: var(--theme-accent-primary-hover);
	}

	.link-info,
	.password-info {
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 6px;
		padding: 1rem;
		font-size: 0.9rem;
	}

	.link-info p,
	.password-info p {
		margin: 0 0 0.5rem 0;
		line-height: 1.4;
	}

	.link-info p:last-child,
	.password-info p:last-child {
		margin-bottom: 0;
	}

	.btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		border: 2px solid var(--theme-border);
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border-color: var(--theme-accent-primary);
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
		transform: translateY(-1px);
	}

	.btn.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border-color: var(--theme-border);
	}

	.btn.secondary:hover:not(:disabled) {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-1px);
	}

	/* Responsive Design */
	@media (max-width: 640px) {
		.modal-content {
			margin: 0.5rem;
		}

		.modal-header,
		.modal-body,
		.modal-footer {
			padding: 1rem;
		}

		.option-label {
			flex-direction: column;
			gap: 0.5rem;
		}

		.link-input-group,
		.password-input-group {
			flex-direction: column;
		}

		.modal-footer {
			flex-direction: column;
		}
	}

	/* Focus Management */
	.close-button:focus,
	.copy-button:focus,
	.btn:focus {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
	}
</style>
