import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import fs from 'fs';
import path from 'path';
import { writeFile } from 'fs/promises';

// POST /api/admin/hero-images/upload - Upload image for hero images (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return json({
        success: false,
        error: 'No file provided'
      }, { status: 400 });
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return json({
        success: false,
        error: 'File must be an image'
      }, { status: 400 });
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return json({
        success: false,
        error: 'File size must be less than 10MB'
      }, { status: 400 });
    }

    // Create upload directories if they don't exist
    const HERO_UPLOAD_DIR = path.resolve('static/uploads/hero-images');
    const HERO_THUMBNAIL_DIR = path.join(HERO_UPLOAD_DIR, 'thumbnails');

    if (!fs.existsSync(HERO_UPLOAD_DIR)) {
      fs.mkdirSync(HERO_UPLOAD_DIR, { recursive: true });
    }

    if (!fs.existsSync(HERO_THUMBNAIL_DIR)) {
      fs.mkdirSync(HERO_THUMBNAIL_DIR, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(file.name);
    const filename = `hero-${timestamp}-${randomString}${fileExtension}`;

    // Save the file
    const filePath = path.join(HERO_UPLOAD_DIR, filename);
    const thumbnailPath = path.join(HERO_THUMBNAIL_DIR, filename);

    // Convert file to buffer and save
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    await writeFile(filePath, buffer);

    // For now, copy the same image as thumbnail
    // In a production app, you'd want to resize the image for thumbnails
    await writeFile(thumbnailPath, buffer);

    // Generate URLs
    const imageUrl = `/uploads/hero-images/${filename}`;
    const thumbnailUrl = `/uploads/hero-images/thumbnails/${filename}`;

    return json({
      success: true,
      data: {
        filename,
        imageUrl,
        thumbnailUrl,
        originalName: file.name,
        size: file.size,
        mimeType: file.type
      }
    });
  } catch (error) {
    console.error('Error uploading hero image:', error);
    return json({
      success: false,
      error: 'Failed to upload image'
    }, { status: 500 });
  }
};
