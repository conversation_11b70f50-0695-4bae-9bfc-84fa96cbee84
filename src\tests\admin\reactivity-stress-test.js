/**
 * Comprehensive Reactivity Stress Test Suite
 * 
 * This script systematically tests the simulate interactions page to identify
 * and capture Svelte 5 reactivity issues, particularly effect_update_depth_exceeded errors.
 */

// Test Configuration
const TEST_CONFIG = {
    RAPID_CLICK_DELAY: 50,      // 50ms between rapid clicks
    STRESS_ITERATIONS: 20,      // Number of rapid iterations
    MONITORING_DURATION: 5000,  // 5 seconds of monitoring
    ERROR_THRESHOLD: 10         // Max rapid calls before flagging as potential issue
};

// Test Results Storage
let testResults = {
    timestamp: new Date().toISOString(),
    tests: [],
    errors: [],
    reactivityPatterns: [],
    recommendations: []
};

// Utility Functions
function log(message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry, data || '');
    
    testResults.tests.push({
        timestamp,
        message,
        data
    });
}

function captureError(error, context) {
    const errorData = {
        timestamp: new Date().toISOString(),
        error: error.message,
        stack: error.stack,
        context,
        debugLogs: window.debugTests ? 'Available' : 'Not Available'
    };
    
    testResults.errors.push(errorData);
    console.error('🚨 ERROR CAPTURED:', errorData);
}

// Test 1: Baseline System Check
function testBaselineSystem() {
    log('🔍 TEST 1: Baseline System Check');
    
    try {
        // Check if debug system is available
        if (typeof window.debugTests === 'object') {
            log('✅ Debug system available');
            window.debugTests.testDebugPersistence();
        } else {
            log('❌ Debug system not available');
        }
        
        // Check page elements
        const contentTypeSelect = document.getElementById('content-type');
        const debugPanel = document.querySelector('.debug-panel');
        
        log('✅ Page elements check', {
            contentTypeSelect: !!contentTypeSelect,
            debugPanel: !!debugPanel
        });
        
        return true;
    } catch (error) {
        captureError(error, 'Baseline System Check');
        return false;
    }
}

// Test 2: Normal Interaction Testing
function testNormalInteractions() {
    log('🔍 TEST 2: Normal Interaction Testing');
    
    return new Promise((resolve) => {
        try {
            const contentTypeSelect = document.getElementById('content-type');
            if (!contentTypeSelect) {
                log('❌ Content type select not found');
                resolve(false);
                return;
            }
            
            let interactionCount = 0;
            const interactions = ['gallery', 'news', 'gallery', 'news'];
            
            function performNextInteraction() {
                if (interactionCount >= interactions.length) {
                    log('✅ Normal interactions completed');
                    resolve(true);
                    return;
                }
                
                const value = interactions[interactionCount];
                log(`📝 Setting content type to: ${value}`);
                
                contentTypeSelect.value = value;
                contentTypeSelect.dispatchEvent(new Event('change'));
                
                interactionCount++;
                setTimeout(performNextInteraction, 1000); // 1 second between changes
            }
            
            performNextInteraction();
            
        } catch (error) {
            captureError(error, 'Normal Interaction Testing');
            resolve(false);
        }
    });
}

// Test 3: Rapid Content Type Switching
function testRapidContentTypeSwitching() {
    log('🔍 TEST 3: Rapid Content Type Switching');
    
    return new Promise((resolve) => {
        try {
            const contentTypeSelect = document.getElementById('content-type');
            if (!contentTypeSelect) {
                log('❌ Content type select not found');
                resolve(false);
                return;
            }
            
            let switchCount = 0;
            const maxSwitches = TEST_CONFIG.STRESS_ITERATIONS;
            
            function rapidSwitch() {
                if (switchCount >= maxSwitches) {
                    log('✅ Rapid switching test completed', { totalSwitches: switchCount });
                    resolve(true);
                    return;
                }
                
                const value = switchCount % 2 === 0 ? 'gallery' : 'news';
                contentTypeSelect.value = value;
                contentTypeSelect.dispatchEvent(new Event('change'));
                
                switchCount++;
                setTimeout(rapidSwitch, TEST_CONFIG.RAPID_CLICK_DELAY);
            }
            
            log(`🚀 Starting rapid switching: ${maxSwitches} switches at ${TEST_CONFIG.RAPID_CLICK_DELAY}ms intervals`);
            rapidSwitch();
            
        } catch (error) {
            captureError(error, 'Rapid Content Type Switching');
            resolve(false);
        }
    });
}

// Test 4: Form Input Stress Testing
function testFormInputStress() {
    log('🔍 TEST 4: Form Input Stress Testing');
    
    return new Promise((resolve) => {
        try {
            const inputs = [
                document.querySelector('input[type="number"]'), // Max interactions
                document.querySelector('textarea'),             // Custom message
                document.querySelector('input[type="datetime-local"]') // Scheduled for
            ].filter(Boolean);
            
            if (inputs.length === 0) {
                log('❌ No form inputs found');
                resolve(false);
                return;
            }
            
            log(`📝 Found ${inputs.length} form inputs to test`);
            
            let inputIndex = 0;
            let changeCount = 0;
            const maxChanges = TEST_CONFIG.STRESS_ITERATIONS;
            
            function rapidInputChange() {
                if (changeCount >= maxChanges) {
                    log('✅ Form input stress test completed', { totalChanges: changeCount });
                    resolve(true);
                    return;
                }
                
                const input = inputs[inputIndex % inputs.length];
                const testValue = `test-${changeCount}-${Date.now()}`;
                
                input.value = testValue;
                input.dispatchEvent(new Event('input'));
                input.dispatchEvent(new Event('change'));
                
                changeCount++;
                inputIndex++;
                setTimeout(rapidInputChange, TEST_CONFIG.RAPID_CLICK_DELAY);
            }
            
            rapidInputChange();
            
        } catch (error) {
            captureError(error, 'Form Input Stress Testing');
            resolve(false);
        }
    });
}

// Test 5: Monitor Reactive Call Patterns
function testReactiveCallPatterns() {
    log('🔍 TEST 5: Reactive Call Pattern Monitoring');
    
    return new Promise((resolve) => {
        try {
            // Start monitoring reactive calls
            const stopMonitoring = window.debugTests ? window.debugTests.monitorReactiveCalls() : null;
            
            if (!stopMonitoring) {
                log('❌ Reactive monitoring not available');
                resolve(false);
                return;
            }
            
            log(`⏱️ Monitoring reactive patterns for ${TEST_CONFIG.MONITORING_DURATION}ms`);
            
            // Perform various interactions during monitoring
            setTimeout(() => {
                const contentTypeSelect = document.getElementById('content-type');
                if (contentTypeSelect) {
                    contentTypeSelect.value = 'gallery';
                    contentTypeSelect.dispatchEvent(new Event('change'));
                }
            }, 1000);
            
            setTimeout(() => {
                const contentTypeSelect = document.getElementById('content-type');
                if (contentTypeSelect) {
                    contentTypeSelect.value = 'news';
                    contentTypeSelect.dispatchEvent(new Event('change'));
                }
            }, 2000);
            
            setTimeout(() => {
                stopMonitoring();
                log('✅ Reactive pattern monitoring completed');
                resolve(true);
            }, TEST_CONFIG.MONITORING_DURATION);
            
        } catch (error) {
            captureError(error, 'Reactive Call Pattern Monitoring');
            resolve(false);
        }
    });
}

// Test 6: Error Boundary Testing
function testErrorBoundary() {
    log('🔍 TEST 6: Error Boundary Testing');
    
    try {
        // Check if error boundary is present
        const errorBoundary = document.querySelector('.error-boundary');
        
        if (errorBoundary && errorBoundary.style.display !== 'none') {
            log('⚠️ Error boundary is currently active');
            log('📊 Error boundary content:', errorBoundary.textContent.substring(0, 200));
            return true;
        } else {
            log('✅ No active error boundary detected');
            return true;
        }
        
    } catch (error) {
        captureError(error, 'Error Boundary Testing');
        return false;
    }
}

// Main Test Runner
async function runComprehensiveReactivityTests() {
    console.log('🚀 Starting Comprehensive Reactivity Stress Tests');
    console.log('=' .repeat(60));
    
    try {
        // Test 1: Baseline
        const baselineOk = testBaselineSystem();
        if (!baselineOk) {
            log('❌ Baseline test failed, aborting');
            return testResults;
        }
        
        // Test 2: Normal Interactions
        await testNormalInteractions();
        
        // Test 3: Rapid Content Type Switching
        await testRapidContentTypeSwitching();
        
        // Test 4: Form Input Stress
        await testFormInputStress();
        
        // Test 5: Reactive Pattern Monitoring
        await testReactiveCallPatterns();
        
        // Test 6: Error Boundary Check
        testErrorBoundary();
        
        // Generate Summary
        generateTestSummary();
        
    } catch (error) {
        captureError(error, 'Main Test Runner');
    }
    
    console.log('🏁 Comprehensive Reactivity Tests Completed');
    console.log('=' .repeat(60));
    
    return testResults;
}

// Generate Test Summary
function generateTestSummary() {
    log('📊 GENERATING TEST SUMMARY');
    
    const summary = {
        totalTests: testResults.tests.length,
        totalErrors: testResults.errors.length,
        hasReactivityIssues: testResults.errors.some(e => e.error.includes('effect_update_depth_exceeded')),
        recommendations: []
    };
    
    if (summary.totalErrors === 0) {
        summary.recommendations.push('✅ No errors detected - system appears stable');
    } else {
        summary.recommendations.push(`⚠️ ${summary.totalErrors} errors detected - review error details`);
    }
    
    if (summary.hasReactivityIssues) {
        summary.recommendations.push('🚨 Reactivity issues detected - investigate infinite loops');
    }
    
    testResults.summary = summary;
    log('📋 Test Summary Generated', summary);
}

// Export test results
function exportTestResults() {
    const blob = new Blob([JSON.stringify(testResults, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `reactivity-stress-test-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    log('💾 Test results exported to file');
}

// Make functions available globally
window.reactivityStressTest = {
    runComprehensiveReactivityTests,
    exportTestResults,
    testResults,
    TEST_CONFIG
};

console.log('🧪 Reactivity Stress Test Suite Loaded');
console.log('Run: window.reactivityStressTest.runComprehensiveReactivityTests()');
