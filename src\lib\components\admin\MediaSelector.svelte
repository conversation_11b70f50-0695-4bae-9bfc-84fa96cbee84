<script>
  import { onMount } from 'svelte';
  
  // Props
  export let selectedMedia = null;
  export let multiple = false;
  export let onSelect = (media) => {};
  export let mediaType = null; // Optional filter by media type
  
  // State
  let mediaItems = [];
  let loading = true;
  let error = null;
  let showModal = false;
  
  // Fetch media items
  async function fetchMedia() {
    try {
      loading = true;
      error = null;
      
      // Build URL with optional type filter
      let url = '/api/media?limit=100';
      if (mediaType) {
        url += `&type=${encodeURIComponent(mediaType)}`;
      }
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch media');
      }
      
      const data = await response.json();
      mediaItems = data.data || [];
    } catch (err) {
      console.error('Error fetching media:', err);
      error = err.message || 'Failed to load media';
    } finally {
      loading = false;
    }
  }
  
  // Handle media selection
  function handleSelect(media) {
    if (multiple) {
      if (!selectedMedia) selectedMedia = [];
      // Check if already selected
      if (!selectedMedia.some(m => m.id === media.id)) {
        selectedMedia = [...selectedMedia, media];
      }
    } else {
      selectedMedia = media;
    }
    
    onSelect(selectedMedia);
    
    if (!multiple) {
      showModal = false;
    }
  }
  
  // Remove a selected item (for multiple selection)
  function removeSelected(media) {
    if (multiple && Array.isArray(selectedMedia)) {
      selectedMedia = selectedMedia.filter(m => m.id !== media.id);
      onSelect(selectedMedia);
    } else {
      selectedMedia = null;
      onSelect(null);
    }
  }
  
  // Initialize
  onMount(fetchMedia);
</script>

<div class="media-selector">
  <div class="preview">
    {#if selectedMedia}
      {#if Array.isArray(selectedMedia) && selectedMedia.length > 0}
        {#each selectedMedia as media}
          <div class="media-item">
            <img 
              src={media.thumbnailPath || media.path || '/images/placeholder.jpg'} 
              alt={media.alt || media.originalName} 
              onerror="this.src='/images/placeholder.jpg'"
            />
            <button class="remove-btn" on:click={() => removeSelected(media)}>×</button>
          </div>
        {/each}
      {:else if !Array.isArray(selectedMedia)}
        <div class="media-item">
          <img 
            src={selectedMedia.thumbnailPath || selectedMedia.path || '/images/placeholder.jpg'} 
            alt={selectedMedia.alt || selectedMedia.originalName}
            onerror="this.src='/images/placeholder.jpg'"
          />
          <button class="remove-btn" on:click={() => removeSelected(selectedMedia)}>×</button>
        </div>
      {:else}
        <div class="empty-state">No media selected</div>
      {/if}
    {:else}
      <div class="empty-state">No media selected</div>
    {/if}
  </div>
  
  <button class="select-btn" on:click={() => { showModal = true; fetchMedia(); }}>
    Select Media
  </button>
  
  {#if showModal}
    <div class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Media Library</h2>
          <button class="close-btn" on:click={() => showModal = false}>×</button>
        </div>
        
        <div class="modal-body">
          {#if loading}
            <div class="loading">Loading media...</div>
          {:else if error}
            <div class="error">
              <p>{error}</p>
              <button on:click={fetchMedia}>Try Again</button>
            </div>
          {:else if mediaItems.length === 0}
            <div class="empty">
              <p>No media found</p>
              <a href="/admin/media/upload" target="_blank" class="btn">Upload New</a>
            </div>
          {:else}
            <div class="media-grid">
              {#each mediaItems as media}
                <div 
                  class="media-item" 
                  class:selected={
                    selectedMedia === media || 
                    (Array.isArray(selectedMedia) && selectedMedia.some(m => m.id === media.id))
                  }
                  on:click={() => handleSelect(media)}
                  on:keypress={(e) => e.key === 'Enter' && handleSelect(media)}
                  tabindex="0"
                  role="button"
                  aria-label={`Select ${media.originalName}`}
                >
                  <img 
                    src={media.thumbnailPath || media.path || '/images/placeholder.jpg'} 
                    alt={media.alt || media.originalName}
                    onerror="this.src='/images/placeholder.jpg'"
                  />
                  <div class="media-info">
                    <span class="filename">{media.originalName}</span>
                  </div>
                </div>
              {/each}
            </div>
          {/if}
        </div>
        
        <div class="modal-footer">
          <button class="btn secondary" on:click={() => showModal = false}>Cancel</button>
          <a href="/admin/media/upload" target="_blank" class="btn primary">Upload New</a>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .media-selector {
    margin-bottom: 1rem;
  }
  
  .preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }
  
  .media-item {
    width: 100px;
    height: 100px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
  }
  
  .media-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .remove-btn {
    position: absolute;
    top: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 20px;
    height: 20px;
    font-size: 14px;
    line-height: 1;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .empty-state {
    width: 100px;
    height: 100px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 0.8rem;
    text-align: center;
    padding: 0.5rem;
  }
  
  .select-btn {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 8px;
    width: 80%;
    max-width: 900px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }
  
  .modal-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .modal-header h2 {
    margin: 0;
  }
  
  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
  }
  
  .modal-body {
    padding: 1rem;
    overflow-y: auto;
    flex: 1;
  }
  
  .modal-footer {
    padding: 1rem;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
  }
  
  .media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
  }
  
  .media-grid .media-item {
    cursor: pointer;
    width: 100%;
    height: 120px;
    position: relative;
    transition: transform 0.2s;
  }
  
  .media-grid .media-item:hover {
    transform: scale(1.05);
  }
  
  .media-grid .media-item.selected {
    border: 2px solid #4caf50;
  }
  
  .media-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.25rem;
    font-size: 0.7rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .loading, .empty, .error {
    text-align: center;
    padding: 2rem;
  }
  
  .error {
    color: #c62828;
  }
  
  .btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    border: none;
  }
  
  .primary {
    background-color: #4caf50;
    color: white;
  }
  
  .secondary {
    background-color: #f0f0f0;
    color: #333;
  }
</style>
