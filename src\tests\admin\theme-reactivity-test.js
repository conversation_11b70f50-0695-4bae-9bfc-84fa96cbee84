// Add to your existing reactivity-stress-test.js
function testThemeReactivity() {
  const themeToggle = document.querySelector('.theme-toggle');
  
  // Rapid theme toggling to test reactivity
  for (let i = 0; i < TEST_CONFIG.STRESS_ITERATIONS; i++) {
    setTimeout(() => {
      themeToggle.click();
    }, i * TEST_CONFIG.RAPID_CLICK_DELAY);
  }
  
  // Monitor for errors
  const errors = [];
  const originalConsoleError = console.error;
  console.error = (...args) => {
    errors.push(args.join(' '));
    originalConsoleError(...args);
  };
  
  // Check results
  setTimeout(() => {
    console.error = originalConsoleError;
    testResults.tests.push({
      name: 'Theme Toggle Reactivity',
      errors: errors.length,
      passed: errors.length === 0
    });
  }, TEST_CONFIG.MONITORING_DURATION);
}