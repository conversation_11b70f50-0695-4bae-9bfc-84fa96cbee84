import AdminJS from 'adminjs';
import express from 'express';
import AdminJSExpress from '@adminjs/express';
import session from 'express-session';
import formidable from 'express-formidable';
import { ComponentLoader } from 'adminjs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { Database, Resource } from '@adminjs/sql';
import Database_SQLite from 'better-sqlite3';

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Register the SQL adapter
AdminJS.registerAdapter({ Database, Resource });

// Connect to the database
const databaseUrl = process.env.DATABASE_URL || 'local.db';
const sqlite = new Database_SQLite(databaseUrl);

// Create component loader
const componentLoader = new ComponentLoader();
const Components = {
  Dashboard: componentLoader.add('Dashboard', path.join(__dirname, 'src/lib/server/admin/components/dashboard.jsx')),
};

// Simple authentication function
const authenticate = async (email, password) => {
  // In a real app, you'd check against the database
  if (email === '<EMAIL>' && password === 'admin') {
    return {
      email: '<EMAIL>',
      role: 'admin',
    };
  }
  return null;
};

// Create AdminJS instance
const admin = new AdminJS({
  rootPath: '/admin',
  componentLoader,
  branding: {
    companyName: 'Finn Wolfhard Fan Club Admin',
    logo: '/images/logo.png',
    favicon: '/favicon.png',
    withMadeWithLove: false,
  },
  dashboard: {
    handler: async () => {
      return { message: 'Welcome to Finn Wolfhard Fan Club Admin Panel' };
    },
    component: Components.Dashboard,
  },
  // Define custom pages instead of database resources
  pages: {
    'Gallery Management': {
      handler: async () => {
        return { redirectUrl: 'http://localhost:5173/admin/gallery' };
      },
    },
    'News Management': {
      handler: async () => {
        return { redirectUrl: 'http://localhost:5173/admin/news' };
      },
    },
    'User Management': {
      handler: async () => {
        return { redirectUrl: 'http://localhost:5173/admin/users' };
      },
    },
    'Media Library': {
      handler: async () => {
        return { redirectUrl: 'http://localhost:5173/admin/media' };
      },
    },
  },
});

// Enable development mode
admin.watch();

// Create Express app
const app = express();
app.use(formidable());

// Create AdminJS router with authentication
const router = AdminJSExpress.buildAuthenticatedRouter(
  admin,
  {
    authenticate,
    cookieName: 'adminjs',
    cookiePassword: 'some-secret-password-used-to-secure-cookie',
  },
  null,
  {
    resave: false,
    saveUninitialized: true,
    secret: 'session-secret',
    cookie: {
      httpOnly: process.env.NODE_ENV === 'production',
      secure: process.env.NODE_ENV === 'production',
    },
    name: 'adminjs',
  }
);

// Use the router
app.use(admin.options.rootPath, router);

// Start the server
const PORT = process.env.ADMIN_PORT || 3001;
app.listen(PORT, () => {
  console.log(`AdminJS started on http://localhost:${PORT}${admin.options.rootPath}`);
});
