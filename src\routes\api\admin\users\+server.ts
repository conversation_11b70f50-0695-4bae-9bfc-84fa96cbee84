import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq, like, and, gte, lte, count, desc, asc } from 'drizzle-orm';
import type { RequestHand<PERSON> } from './$types';
import logger from '$lib/server/services/logger';

// GET /api/admin/users - Get paginated list of users with search and filters
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || (locals.user.role !== 'admin' && locals.user.role !== 'moderator')) {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    // Parse query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const search = url.searchParams.get('search') || '';
    const role = url.searchParams.get('role') || '';
    const status = url.searchParams.get('status') || '';
    const type = url.searchParams.get('type') || '';
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = url.searchParams.get('sortOrder') || 'desc';
    const dateFrom = url.searchParams.get('dateFrom');
    const dateTo = url.searchParams.get('dateTo');

    // Build where conditions
    const conditions = [];

    // Search by username, display name, or email
    if (search) {
      conditions.push(
        like(users.username, `%${search}%`),
        like(users.displayName, `%${search}%`),
        like(users.email, `%${search}%`)
      );
    }

    // Filter by role
    if (role && ['admin', 'moderator', 'user'].includes(role)) {
      conditions.push(eq(users.role, role as 'admin' | 'moderator' | 'user'));
    }

    // Filter by status
    if (status && ['active', 'inactive', 'suspended'].includes(status)) {
      conditions.push(eq(users.status, status as 'active' | 'inactive' | 'suspended'));
    }

    // Filter by type (real vs simulated)
    if (type === 'real') {
      conditions.push(eq(users.isSimulated, false));
    } else if (type === 'simulated') {
      conditions.push(eq(users.isSimulated, true));
    }

    // Filter by date range
    if (dateFrom) {
      conditions.push(gte(users.createdAt, dateFrom));
    }
    if (dateTo) {
      conditions.push(lte(users.createdAt, dateTo));
    }

    // Calculate offset
    const offset = (page - 1) * limit;

    // Build the query
    let query = db.select({
      id: users.id,
      username: users.username,
      displayName: users.displayName,
      email: users.email,
      role: users.role,
      status: users.status,
      isSimulated: users.isSimulated,
      bio: users.bio,
      avatarUrl: users.avatarUrl,
      location: users.location,
      website: users.website,
      birthDate: users.birthDate,
      interests: users.interests,
      simulatedPersonality: users.simulatedPersonality,
      lastActiveAt: users.lastActiveAt,
      createdAt: users.createdAt,
      updatedAt: users.updatedAt,
      preferences: users.preferences
    }).from(users);

    // Apply where conditions
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Apply sorting
    const sortColumn = sortBy === 'username' ? users.username :
                      sortBy === 'displayName' ? users.displayName :
                      sortBy === 'email' ? users.email :
                      sortBy === 'role' ? users.role :
                      sortBy === 'status' ? users.status :
                      sortBy === 'lastActiveAt' ? users.lastActiveAt :
                      sortBy === 'updatedAt' ? users.updatedAt :
                      users.createdAt;

    query = query.orderBy(sortOrder === 'asc' ? asc(sortColumn) : desc(sortColumn));

    // Apply pagination
    query = query.limit(limit).offset(offset);

    // Execute query
    const userList = await query;

    // Get total count for pagination
    let countQuery = db.select({ count: count() }).from(users);
    if (conditions.length > 0) {
      countQuery = countQuery.where(and(...conditions));
    }
    const totalResult = await countQuery;
    const total = totalResult[0].count;

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Log admin action
    logger.info('Admin users list accessed', {
      adminUser: locals.user.username,
      page,
      limit,
      search,
      role,
      total
    });

    return json({
      success: true,
      data: {
        users: userList,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage,
          hasPrevPage
        },
        filters: {
          search,
          role,
          status,
          dateFrom,
          dateTo,
          sortBy,
          sortOrder
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching users list:', error);
    return json({
      success: false,
      error: 'Failed to fetch users list'
    }, { status: 500 });
  }
};

// POST /api/admin/users - Create a new user (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.username || !body.displayName || !body.email || !body.role) {
      return json({
        success: false,
        error: 'Username, display name, email, and role are required'
      }, { status: 400 });
    }

    // Validate role
    if (!['admin', 'moderator', 'user'].includes(body.role)) {
      return json({
        success: false,
        error: 'Invalid role specified'
      }, { status: 400 });
    }

    // Check if username or email already exists
    const existingUser = await db.select()
      .from(users)
      .where(
        and(
          eq(users.username, body.username),
          eq(users.email, body.email)
        )
      )
      .limit(1);

    if (existingUser.length > 0) {
      return json({
        success: false,
        error: 'Username or email already exists'
      }, { status: 409 });
    }

    // Create new user with temporary password
    const tempPassword = 'TempPass123!'; // In production, generate a secure temporary password
    const result = await db.insert(users).values({
      username: body.username,
      displayName: body.displayName,
      email: body.email,
      passwordHash: tempPassword, // In production, hash this properly
      role: body.role,
      status: body.status || 'active',
      isSimulated: body.isSimulated || false,
      bio: body.bio || null,
      avatarUrl: body.avatarUrl || null,
      location: body.location || null,
      website: body.website || null,
      birthDate: body.birthDate || null,
      interests: body.interests || [],
      simulatedPersonality: body.simulatedPersonality || null,
      preferences: {
        highContrast: false,
        largeText: false,
        simplifiedInterface: false
      }
    }).returning();

    // Return user data (excluding password)
    const { passwordHash, ...userData } = result[0];

    // Log admin action
    logger.info('User created by admin', {
      adminUser: locals.user.username,
      createdUser: userData.username,
      role: userData.role
    });

    return json({
      success: true,
      data: userData,
      message: 'User created successfully. Temporary password: TempPass123!'
    }, { status: 201 });
  } catch (error) {
    logger.error('Error creating user:', error);
    return json({
      success: false,
      error: 'Failed to create user'
    }, { status: 500 });
  }
};
