<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import LoadingSpinner from '../LoadingSpinner.svelte';
	import ErrorMessage from '../ErrorMessage.svelte';
	import { api } from '$lib/utils/api';

	const dispatch = createEventDispatcher();

	// Props
	export let selectedUserId: number | null = null;
	export let contentType: string = 'news';
	export let disabled = false;
	export let existingContent = {
		title: '',
		content: '',
		description: ''
	};

	// AI Generation State
	let isGenerating = false;
	let generationError = '';
	let showAIPanel = false;
	let generatedContent: any = null;
	let authenticityScore = 0;
	let reviewId: number | null = null;
	let requiresReview = false;
	let moderationResult: any = null;

	// AI Configuration
	let aiConfig = {
		prompt: '',
		tone: 'casual',
		length: 'medium',
		includePersonality: true,
		focusAreas: [] as string[]
	};

	// Tone options
	const toneOptions = [
		{ value: 'casual', label: 'Casual & Friendly' },
		{ value: 'enthusiastic', label: 'Enthusiastic Fan' },
		{ value: 'informative', label: 'Informative' },
		{ value: 'conversational', label: 'Conversational' },
		{ value: 'excited', label: 'Excited' }
	];

	// Length options
	const lengthOptions = [
		{ value: 'short', label: 'Short (1-2 paragraphs)' },
		{ value: 'medium', label: 'Medium (3-4 paragraphs)' },
		{ value: 'long', label: 'Long (5+ paragraphs)' }
	];

	// Focus areas for Finn Wolfhard content
	const focusAreaOptions = [
		{ value: 'stranger_things', label: 'Stranger Things' },
		{ value: 'it_movies', label: 'IT Movies' },
		{ value: 'music', label: 'Music & The Aubreys' },
		{ value: 'acting', label: 'Acting Career' },
		{ value: 'interviews', label: 'Interviews & Press' },
		{ value: 'fan_interactions', label: 'Fan Interactions' },
		{ value: 'behind_scenes', label: 'Behind the Scenes' },
		{ value: 'upcoming_projects', label: 'Upcoming Projects' }
	];

	// Content type specific prompts
	const promptSuggestions = {
		news: [
			'Latest Finn Wolfhard interview highlights',
			'Behind the scenes from recent project',
			'Fan convention appearance recap',
			'New music release announcement',
			'Upcoming movie/show news'
		],
		gallery: [
			'Recent photoshoot images',
			'Behind the scenes photos',
			'Fan art showcase',
			'Convention photos',
			'Candid moments'
		],
		comment: [
			'Reaction to latest news',
			'Thoughts on fan theories',
			'Appreciation post',
			'Discussion starter',
			'Fan community interaction'
		],
		message: [
			'Daily fan chat',
			'Sharing excitement',
			'Community discussion',
			'Fan theory sharing',
			'General conversation'
		]
	};

	/**
	 * Toggle AI panel visibility
	 */
	function toggleAIPanel() {
		showAIPanel = !showAIPanel;
		if (showAIPanel && !aiConfig.prompt) {
			// Set default prompt based on content type
			const suggestions = promptSuggestions[contentType] || [];
			if (suggestions.length > 0) {
				aiConfig.prompt = suggestions[0];
			}
		}
	}

	/**
	 * Handle focus area selection
	 */
	function toggleFocusArea(area: string) {
		const index = aiConfig.focusAreas.indexOf(area);
		if (index > -1) {
			aiConfig.focusAreas = aiConfig.focusAreas.filter(a => a !== area);
		} else {
			aiConfig.focusAreas = [...aiConfig.focusAreas, area];
		}
	}

	/**
	 * Generate content using AI
	 */
	async function generateContent() {
		if (!selectedUserId || !aiConfig.prompt.trim()) {
			generationError = 'Please select a user and enter a prompt';
			return;
		}

		isGenerating = true;
		generationError = '';
		generatedContent = null;

		try {
			const response = await api.post('/api/admin/generate-content', {
				contentType,
				asUserId: selectedUserId,
				prompt: aiConfig.prompt,
				tone: aiConfig.tone,
				length: aiConfig.length,
				focusAreas: aiConfig.focusAreas,
				includePersonality: aiConfig.includePersonality
			});

			if (response.success && response.data) {
				generatedContent = response.data.content;
				authenticityScore = response.data.authenticityScore || 0;
				reviewId = response.data.reviewId || null;
				requiresReview = response.data.requiresReview || false;
				moderationResult = response.data.moderationResult || null;

				// Dispatch event with generated content
				dispatch('generated', {
					content: generatedContent,
					authenticityScore,
					reviewId,
					requiresReview,
					moderationResult,
					aiConfig: { ...aiConfig }
				});
			} else {
				generationError = response.error || 'Failed to generate content';
			}
		} catch (error) {
			console.error('Error generating content:', error);
			generationError = 'An error occurred while generating content';
		} finally {
			isGenerating = false;
		}
	}

	/**
	 * Apply generated content to form
	 */
	function applyGeneratedContent() {
		if (generatedContent) {
			dispatch('apply', {
				content: generatedContent,
				authenticityScore,
				aiConfig: { ...aiConfig }
			});
			generatedContent = null;
			showAIPanel = false;
		}
	}

	/**
	 * Regenerate content with same settings
	 */
	function regenerateContent() {
		generateContent();
	}

	/**
	 * Use a suggested prompt
	 */
	function useSuggestedPrompt(prompt: string) {
		aiConfig.prompt = prompt;
	}
</script>

<div class="ai-content-generator">
	<!-- AI Toggle Button -->
	<button
		class="ai-toggle-btn"
		class:active={showAIPanel}
		onclick={toggleAIPanel}
		{disabled}
		aria-expanded={showAIPanel}
		aria-controls="ai-panel"
	>
		<span class="ai-icon" aria-hidden="true">🤖</span>
		AI Content Assistant
		<span class="toggle-indicator" aria-hidden="true">
			{showAIPanel ? '▼' : '▶'}
		</span>
	</button>

	<!-- AI Configuration Panel -->
	{#if showAIPanel}
		<div id="ai-panel" class="ai-panel" role="region" aria-label="AI Content Generation Settings">
			<div class="ai-panel-header">
				<h4>AI Content Generation</h4>
				<p class="ai-description">
					Generate authentic {contentType} content that matches the selected user's personality and writing style.
				</p>
			</div>

			{#if generationError}
				<ErrorMessage
					title="Generation Error"
					message={generationError}
					type="error"
					dismissible={true}
					onDismiss={() => generationError = ''}
				/>
			{/if}

			<div class="ai-config-form">
				<!-- Prompt Input -->
				<div class="form-group">
					<label for="ai-prompt">Content Prompt *</label>
					<textarea
						id="ai-prompt"
						bind:value={aiConfig.prompt}
						placeholder="Describe what you want to generate..."
						rows="3"
						disabled={isGenerating}
						required
					></textarea>
					
					<!-- Suggested Prompts -->
					{#if promptSuggestions[contentType]}
						<div class="prompt-suggestions">
							<span class="suggestions-label">Suggestions:</span>
							{#each promptSuggestions[contentType] as suggestion}
								<button
									class="suggestion-btn"
									onclick={() => useSuggestedPrompt(suggestion)}
									disabled={isGenerating}
									type="button"
								>
									{suggestion}
								</button>
							{/each}
						</div>
					{/if}
				</div>

				<!-- Tone Selection -->
				<div class="form-group">
					<label for="ai-tone">Tone</label>
					<select id="ai-tone" bind:value={aiConfig.tone} disabled={isGenerating}>
						{#each toneOptions as option}
							<option value={option.value}>{option.label}</option>
						{/each}
					</select>
				</div>

				<!-- Length Selection -->
				<div class="form-group">
					<label for="ai-length">Length</label>
					<select id="ai-length" bind:value={aiConfig.length} disabled={isGenerating}>
						{#each lengthOptions as option}
							<option value={option.value}>{option.label}</option>
						{/each}
					</select>
				</div>

				<!-- Focus Areas -->
				<div class="form-group">
					<label>Focus Areas (Optional)</label>
					<div class="focus-areas">
						{#each focusAreaOptions as area}
							<label class="checkbox-option">
								<input
									type="checkbox"
									checked={aiConfig.focusAreas.includes(area.value)}
									onchange={() => toggleFocusArea(area.value)}
									disabled={isGenerating}
								/>
								<span class="checkbox-label">{area.label}</span>
							</label>
						{/each}
					</div>
				</div>

				<!-- Personality Integration -->
				<div class="form-group">
					<label class="checkbox-option">
						<input
							type="checkbox"
							bind:checked={aiConfig.includePersonality}
							disabled={isGenerating}
						/>
						<span class="checkbox-label">
							Include user's personality traits in generation
						</span>
					</label>
				</div>

				<!-- Generate Button -->
				<div class="form-actions">
					<button
						class="btn primary"
						onclick={generateContent}
						disabled={isGenerating || !aiConfig.prompt.trim()}
					>
						{#if isGenerating}
							<LoadingSpinner size="small" inline={true} message="Generating..." />
						{:else}
							<span class="btn-icon" aria-hidden="true">✨</span>
							Generate Content
						{/if}
					</button>
				</div>
			</div>

			<!-- Generated Content Preview -->
			{#if generatedContent}
				<div class="generated-content-preview">
					<div class="preview-header">
						<h5>Generated Content</h5>
						<div class="preview-meta">
							{#if authenticityScore > 0}
								<div class="authenticity-score">
									<span class="score-label">Authenticity Score:</span>
									<span
										class="score-value"
										class:high={authenticityScore >= 80}
										class:medium={authenticityScore >= 60 && authenticityScore < 80}
										class:low={authenticityScore < 60}
									>
										{authenticityScore}%
									</span>
								</div>
							{/if}

							{#if requiresReview}
								<div class="review-status">
									<span class="review-badge">⏳ Pending Review</span>
								</div>
							{/if}

							{#if moderationResult}
								<div class="moderation-status">
									<span class="moderation-badge {moderationResult.isAppropriate ? 'safe' : 'flagged'}">
										{moderationResult.isAppropriate ? '✅ Safe' : '⚠️ Flagged'}
									</span>
									{#if moderationResult.flags && moderationResult.flags.length > 0}
										<div class="moderation-flags">
											{#each moderationResult.flags as flag}
												<span class="flag-badge">{flag}</span>
											{/each}
										</div>
									{/if}
								</div>
							{/if}
						</div>
					</div>

					<div class="preview-content">
						{#if generatedContent.title}
							<div class="preview-title">
								<strong>Title:</strong> {generatedContent.title}
							</div>
						{/if}

						<div class="preview-text">
							{@html generatedContent.content || generatedContent}
						</div>

						{#if generatedContent.description}
							<div class="preview-description">
								<strong>Description:</strong> {generatedContent.description}
							</div>
						{/if}
					</div>

					<div class="preview-actions">
						<button
							class="btn primary"
							onclick={applyGeneratedContent}
						>
							<span class="btn-icon" aria-hidden="true">✓</span>
							Apply to Form
						</button>

						<button
							class="btn secondary"
							onclick={regenerateContent}
							disabled={isGenerating}
						>
							<span class="btn-icon" aria-hidden="true">🔄</span>
							Regenerate
						</button>
					</div>
				</div>
			{/if}
		</div>
	{/if}
</div>

<style>
	.ai-content-generator {
		margin-bottom: 1.5rem;
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		overflow: hidden;
	}

	.ai-toggle-btn {
		width: 100%;
		display: flex;
		align-items: center;
		gap: 0.75rem;
		padding: 1rem;
		background: var(--theme-bg-secondary);
		border: none;
		cursor: pointer;
		font-size: 1rem;
		font-weight: 500;
		color: var(--theme-text-primary);
		transition: all 0.2s ease;
	}

	.ai-toggle-btn:hover {
		background: var(--theme-bg-tertiary);
	}

	.ai-toggle-btn.active {
		background: var(--theme-accent-primary);
		color: white;
	}

	.ai-icon {
		font-size: 1.25rem;
	}

	.toggle-indicator {
		margin-left: auto;
		font-size: 0.875rem;
	}

	.ai-panel {
		padding: 1.5rem;
		background: var(--theme-bg-primary);
		border-top: 1px solid var(--theme-border);
	}

	.ai-panel-header {
		margin-bottom: 1.5rem;
	}

	.ai-panel-header h4 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 1.25rem;
	}

	.ai-description {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	.ai-config-form {
		display: flex;
		flex-direction: column;
		gap: 1.25rem;
	}

	.form-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.form-group label {
		font-weight: 500;
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.form-group textarea,
	.form-group select {
		padding: 0.75rem;
		border: 1px solid var(--theme-border);
		border-radius: 6px;
		background: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 0.9rem;
		transition: border-color 0.2s ease;
	}

	.form-group textarea:focus,
	.form-group select:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
		box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
	}

	.prompt-suggestions {
		display: flex;
		flex-wrap: wrap;
		gap: 0.5rem;
		margin-top: 0.5rem;
	}

	.suggestions-label {
		font-size: 0.8rem;
		color: var(--theme-text-secondary);
		align-self: center;
	}

	.suggestion-btn {
		padding: 0.25rem 0.5rem;
		background: var(--theme-bg-tertiary);
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		font-size: 0.8rem;
		color: var(--theme-text-secondary);
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.suggestion-btn:hover {
		background: var(--theme-accent-primary);
		color: white;
		border-color: var(--theme-accent-primary);
	}

	.focus-areas {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 0.5rem;
	}

	.checkbox-option {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		cursor: pointer;
		padding: 0.5rem;
		border-radius: 4px;
		transition: background-color 0.2s ease;
	}

	.checkbox-option:hover {
		background: var(--theme-bg-secondary);
	}

	.checkbox-label {
		font-size: 0.9rem;
		color: var(--theme-text-primary);
	}

	.form-actions {
		display: flex;
		gap: 1rem;
		margin-top: 0.5rem;
	}

	.btn {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
	}

	.btn.primary {
		background: var(--theme-accent-primary);
		color: white;
	}

	.btn.primary:hover:not(:disabled) {
		background: var(--theme-accent-primary-hover);
		transform: translateY(-1px);
	}

	.btn.secondary {
		background: var(--theme-bg-tertiary);
		color: var(--theme-text-primary);
		border: 1px solid var(--theme-border);
	}

	.btn.secondary:hover:not(:disabled) {
		background: var(--theme-bg-secondary);
		border-color: var(--theme-accent-primary);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;
	}

	.btn-icon {
		font-size: 1rem;
	}

	.generated-content-preview {
		margin-top: 1.5rem;
		padding: 1.5rem;
		background: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
	}

	.preview-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 1rem;
	}

	.preview-meta {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
		align-items: flex-end;
	}

	.preview-header h5 {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
	}

	.authenticity-score {
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.score-label {
		font-size: 0.9rem;
		color: var(--theme-text-secondary);
	}

	.score-value {
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-weight: 600;
		font-size: 0.9rem;
	}

	.score-value.high {
		background: var(--theme-accent-success);
		color: white;
	}

	.score-value.medium {
		background: var(--theme-accent-warning);
		color: white;
	}

	.score-value.low {
		background: var(--theme-accent-danger);
		color: white;
	}

	.review-status {
		margin-top: 0.25rem;
	}

	.review-badge {
		padding: 0.25rem 0.5rem;
		background: var(--theme-accent-warning);
		color: white;
		border-radius: 4px;
		font-size: 0.8rem;
		font-weight: 600;
		display: inline-flex;
		align-items: center;
		gap: 0.25rem;
	}

	.moderation-status {
		margin-top: 0.5rem;
	}

	.moderation-badge {
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-size: 0.8rem;
		font-weight: 600;
		display: inline-flex;
		align-items: center;
		gap: 0.25rem;
	}

	.moderation-badge.safe {
		background: var(--theme-accent-success);
		color: white;
	}

	.moderation-badge.flagged {
		background: var(--theme-accent-warning);
		color: white;
	}

	.moderation-flags {
		display: flex;
		flex-wrap: wrap;
		gap: 0.25rem;
		margin-top: 0.5rem;
	}

	.flag-badge {
		padding: 0.125rem 0.375rem;
		background: var(--theme-bg-tertiary);
		color: var(--theme-text-secondary);
		border-radius: 3px;
		font-size: 0.7rem;
		font-weight: 500;
		text-transform: uppercase;
	}

	.preview-content {
		margin-bottom: 1.5rem;
	}

	.preview-title,
	.preview-description {
		margin-bottom: 1rem;
		padding: 0.75rem;
		background: var(--theme-bg-primary);
		border-radius: 6px;
		font-size: 0.9rem;
	}

	.preview-text {
		padding: 1rem;
		background: var(--theme-bg-primary);
		border-radius: 6px;
		line-height: 1.6;
		font-size: 0.9rem;
		color: var(--theme-text-primary);
	}

	.preview-actions {
		display: flex;
		gap: 1rem;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.ai-panel {
			padding: 1rem;
		}

		.focus-areas {
			grid-template-columns: 1fr;
		}

		.preview-header {
			flex-direction: column;
			align-items: flex-start;
			gap: 0.5rem;
		}

		.preview-actions {
			flex-direction: column;
		}

		.form-actions {
			flex-direction: column;
		}
	}

	/* High Contrast Mode */
	@media (prefers-contrast: high) {
		.ai-toggle-btn,
		.suggestion-btn,
		.btn {
			border: 2px solid currentColor;
		}

		.score-value {
			border: 1px solid currentColor;
		}
	}

	/* Reduced Motion */
	@media (prefers-reduced-motion: reduce) {
		.ai-toggle-btn,
		.suggestion-btn,
		.btn,
		.checkbox-option {
			transition: none;
		}
	}
</style>
