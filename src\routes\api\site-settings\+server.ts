import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import Database from 'better-sqlite3';
import { authenticateUser } from '$lib/server/auth';

// Initialize database connection
const db = new Database('local.db');

// GET /api/site-settings - Get all site settings or specific setting
export const GET: RequestHandler = async ({ request, url }) => {
  try {
    const settingKey = url.searchParams.get('key');
    const category = url.searchParams.get('category');
    const publicOnly = url.searchParams.get('public') === 'true';
    
    let query = 'SELECT * FROM site_settings WHERE 1=1';
    const params: any[] = [];
    
    if (settingKey) {
      query += ' AND setting_key = ?';
      params.push(settingKey);
    }
    
    if (category) {
      query += ' AND category = ?';
      params.push(category);
    }
    
    if (publicOnly) {
      query += ' AND is_public = 1';
    }
    
    query += ' ORDER BY category, display_name';
    
    const stmt = db.prepare(query);
    const settings = stmt.all(...params);
    
    // If requesting a specific setting, return just the value
    if (settingKey && settings.length === 1) {
      return json({
        success: true,
        data: settings[0].setting_value
      });
    }
    
    // Group settings by category for easier frontend handling
    const groupedSettings = settings.reduce((acc: any, setting: any) => {
      if (!acc[setting.category]) {
        acc[setting.category] = [];
      }
      acc[setting.category].push(setting);
      return acc;
    }, {});
    
    return json({
      success: true,
      data: groupedSettings
    });
    
  } catch (error) {
    console.error('Error fetching site settings:', error);
    return json({
      success: false,
      error: 'Failed to fetch site settings'
    }, { status: 500 });
  }
};

// PUT /api/site-settings - Update site settings (admin only)
export const PUT: RequestHandler = async ({ request }) => {
  try {
    // Authenticate user
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user?.isAdmin) {
      return json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }
    
    const { settings } = await request.json();
    
    if (!settings || !Array.isArray(settings)) {
      return json({
        success: false,
        error: 'Invalid settings data'
      }, { status: 400 });
    }
    
    // Begin transaction
    const updateStmt = db.prepare(`
      UPDATE site_settings 
      SET setting_value = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE setting_key = ?
    `);
    
    const transaction = db.transaction((settingsToUpdate: any[]) => {
      for (const setting of settingsToUpdate) {
        updateStmt.run(setting.value, setting.key);
      }
    });
    
    transaction(settings);
    
    return json({
      success: true,
      message: 'Site settings updated successfully'
    });
    
  } catch (error) {
    console.error('Error updating site settings:', error);
    return json({
      success: false,
      error: 'Failed to update site settings'
    }, { status: 500 });
  }
};

// POST /api/site-settings - Create new site setting (admin only)
export const POST: RequestHandler = async ({ request }) => {
  try {
    // Authenticate user
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user?.isAdmin) {
      return json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }
    
    const { 
      setting_key, 
      setting_value, 
      setting_type = 'text', 
      category = 'general', 
      display_name, 
      description = '', 
      is_public = 1 
    } = await request.json();
    
    if (!setting_key || !display_name) {
      return json({
        success: false,
        error: 'Setting key and display name are required'
      }, { status: 400 });
    }
    
    const stmt = db.prepare(`
      INSERT INTO site_settings (
        setting_key, setting_value, setting_type, category, 
        display_name, description, is_public
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      setting_key, 
      setting_value, 
      setting_type, 
      category, 
      display_name, 
      description, 
      is_public
    );
    
    return json({
      success: true,
      data: { id: result.lastInsertRowid },
      message: 'Site setting created successfully'
    });
    
  } catch (error) {
    console.error('Error creating site setting:', error);
    return json({
      success: false,
      error: 'Failed to create site setting'
    }, { status: 500 });
  }
};

// DELETE /api/site-settings/[key] - Delete site setting (admin only)
export const DELETE: RequestHandler = async ({ request, url }) => {
  try {
    // Authenticate user
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user?.isAdmin) {
      return json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }
    
    const settingKey = url.searchParams.get('key');
    
    if (!settingKey) {
      return json({
        success: false,
        error: 'Setting key is required'
      }, { status: 400 });
    }
    
    const stmt = db.prepare('DELETE FROM site_settings WHERE setting_key = ?');
    const result = stmt.run(settingKey);
    
    if (result.changes === 0) {
      return json({
        success: false,
        error: 'Setting not found'
      }, { status: 404 });
    }
    
    return json({
      success: true,
      message: 'Site setting deleted successfully'
    });
    
  } catch (error) {
    console.error('Error deleting site setting:', error);
    return json({
      success: false,
      error: 'Failed to delete site setting'
    }, { status: 500 });
  }
};
