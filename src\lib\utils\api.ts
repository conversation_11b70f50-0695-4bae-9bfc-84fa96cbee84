import logger from '$lib/client/logger';

// API client with error handling
export async function apiClient<T = any>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<{ data?: T, error?: string, success: boolean }> {
  try {
    // Set default headers
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };
    
    // Make the request
    const response = await fetch(endpoint, {
      ...options,
      headers
    });
    
    // Parse the response
    const result = await response.json();
    
    // If the request was not successful
    if (!response.ok) {
      // Log the error
      logger.error(`API Error: ${endpoint}`, {
        status: response.status,
        error: result.error,
        type: result.type
      });
      
      return {
        success: false,
        error: result.error || 'An error occurred'
      };
    }
    
    // Return the successful response
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    // Log network or parsing errors
    logger.error(`API Request Failed: ${endpoint}`, error);
    
    return {
      success: false,
      error: error.message || 'Failed to complete request'
    };
  }
}

// Helper methods for common API operations
export const api = {
  get: <T = any>(endpoint: string, options?: RequestInit) => 
    apiClient<T>(endpoint, { method: 'GET', ...options }),
    
  post: <T = any>(endpoint: string, data: any, options?: RequestInit) => 
    apiClient<T>(endpoint, { 
      method: 'POST', 
      body: JSON.stringify(data),
      ...options 
    }),
    
  put: <T = any>(endpoint: string, data: any, options?: RequestInit) => 
    apiClient<T>(endpoint, { 
      method: 'PUT', 
      body: JSON.stringify(data),
      ...options 
    }),
    
  delete: <T = any>(endpoint: string, options?: RequestInit) => 
    apiClient<T>(endpoint, { method: 'DELETE', ...options }),
};
