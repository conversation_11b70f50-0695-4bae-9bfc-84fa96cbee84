import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
  // Check if user is logged in and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    // Redirect to login page if not authenticated as admin
    throw redirect(302, '/login');
  }
  
  // Get the media item ID from the URL
  const id = params.id;
  
  // Return the ID to the page
  return { id };
};
