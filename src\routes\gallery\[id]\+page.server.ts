import { db } from '$lib/server/db';
import { gallery } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      throw error(404, 'Gallery image not found');
    }

    // Fetch the specific gallery image
    const imageResult = await db.select()
      .from(gallery)
      .where(eq(gallery.id, id))
      .limit(1);

    if (imageResult.length === 0) {
      throw error(404, 'Gallery image not found');
    }

    const image = imageResult[0];

    // Only show published images to public users
    // (Admin users would access through admin panel, not public routes)
    if (!image.published) {
      throw error(404, 'Gallery image not found');
    }

    // Get previous and next images for navigation
    // Get all published images ordered by ID to find neighbors
    const allPublishedImages = await db.select({ id: gallery.id, title: gallery.title })
      .from(gallery)
      .where(eq(gallery.published, true))
      .orderBy(gallery.id);

    // Find current image index and get neighbors
    const currentIndex = allPublishedImages.findIndex(img => img.id === id);
    const previousImage = currentIndex > 0 ? allPublishedImages[currentIndex - 1] : null;
    const nextImage = currentIndex < allPublishedImages.length - 1 ? allPublishedImages[currentIndex + 1] : null;

    const totalImages = allPublishedImages.length;
    const currentPosition = currentIndex + 1;

    return {
      image,
      navigation: {
        previous: previousImage,
        next: nextImage,
        currentPosition,
        totalImages
      }
    };
  } catch (err) {
    console.error('Error loading gallery image:', err);
    
    // If it's already a SvelteKit error, re-throw it
    if (err && typeof err === 'object' && 'status' in err) {
      throw err;
    }
    
    // Otherwise, throw a generic 500 error
    throw error(500, 'Failed to load gallery image');
  }
};
