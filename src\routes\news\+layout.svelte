<script>
  import { theme } from '$lib/stores/theme';
  import ThemeToggle from '$lib/components/ThemeToggle.svelte';
</script>

<div class="news-container" class:dark-news={$theme === 'dark'}>
  <header>
    <div class="theme-control">
      <ThemeToggle />
    </div>
    <!-- Other header content -->
  </header>
  
  <main>
    <slot />
  </main>
</div>

<style>
  .news-container {
    background-color: var(--theme-bg-primary);
    color: var(--theme-text-primary);
  }
  
  /* News-specific theme overrides */
  .dark-news :global(.news-card) {
    background-color: var(--theme-bg-secondary);
    border-color: var(--theme-border);
  }
</style>