<script>
  import { theme } from '$lib/stores/theme';
  import ThemeToggle from '$lib/components/ThemeToggle.svelte';
</script>

<div class="news-container" class:dark-news={$theme.current === 'dark'}>
  <header>
    <div class="theme-control">
      <ThemeToggle />
    </div>
    <!-- Other header content -->
  </header>
  
  <main>
    <slot />
  </main>
</div>

<style>
  .news-container {
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    transition: var(--transition-theme);
  }

  /* News-specific theme overrides */
  .dark-news :global(.news-card) {
    background-color: var(--color-surface-secondary);
    border-color: var(--color-border-primary);
  }
</style>