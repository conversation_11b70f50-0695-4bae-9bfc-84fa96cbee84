const Database = require('better-sqlite3');
const path = require('path');

// Connect to the database
const dbPath = path.join(__dirname, '..', 'database.db');
const db = new Database(dbPath);

console.log('Testing JSON parsing fix...');

try {
  // First, let's create a user with problematic personality data
  console.log('1. Creating user with problematic personality data...');
  
  const insertUser = db.prepare(`
    INSERT INTO users (username, display_name, email, password_hash, role, status, is_simulated, simulated_personality)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);
  
  // This is the type of data that was causing the JSON parsing error
  const problematicPersonality = 'enthusiastic, friendly, helpful';
  
  const result = insertUser.run(
    'test_json_user',
    'Test JSON User',
    '<EMAIL>',
    'temppass',
    'user',
    'active',
    1, // is_simulated = true
    problematicPersonality
  );
  
  console.log('✓ User created with ID:', result.lastInsertRowid);
  
  // Now let's retrieve the user and test our safe parsing
  console.log('2. Retrieving user and testing safe JSON parsing...');
  
  const getUser = db.prepare('SELECT * FROM users WHERE id = ?');
  const user = getUser.get(result.lastInsertRowid);
  
  console.log('Raw personality data:', user.simulated_personality);
  
  // Test the safe parsing function (same as in our component)
  function safeJsonParse(jsonString, fallback = []) {
    if (!jsonString) return fallback;
    
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.warn('Failed to parse JSON:', jsonString, 'Error:', error.message);
      return fallback;
    }
  }
  
  // Test parsing the problematic data
  const parsed = safeJsonParse(user.simulated_personality, []);
  console.log('✓ Safe parsing result:', parsed);
  console.log('✓ Type of result:', typeof parsed);
  console.log('✓ Is array:', Array.isArray(parsed));
  
  // Test the validation function
  function validatePersonalityJson(personalityString) {
    if (!personalityString || personalityString.trim() === '') {
      return null;
    }

    try {
      const parsed = JSON.parse(personalityString);
      return JSON.stringify(parsed);
    } catch (error) {
      console.log('Invalid JSON detected, auto-correcting...');
      
      if (personalityString.includes(',') && !personalityString.includes('{')) {
        const traits = personalityString.split(',').map(trait => trait.trim().replace(/['"]/g, ''));
        return JSON.stringify({
          traits: traits,
          writingStyle: 'casual',
          activityLevel: 'medium'
        });
      }
      
      return JSON.stringify({
        traits: [personalityString.trim()],
        writingStyle: 'casual',
        activityLevel: 'medium'
      });
    }
  }
  
  console.log('3. Testing personality validation...');
  const validatedPersonality = validatePersonalityJson(user.simulated_personality);
  console.log('✓ Validated personality:', validatedPersonality);
  
  // Update the user with the corrected personality data
  console.log('4. Updating user with corrected personality data...');
  const updateUser = db.prepare('UPDATE users SET simulated_personality = ? WHERE id = ?');
  updateUser.run(validatedPersonality, result.lastInsertRowid);
  
  // Verify the update
  const updatedUser = getUser.get(result.lastInsertRowid);
  console.log('✓ Updated personality data:', updatedUser.simulated_personality);
  
  // Test parsing the corrected data
  const finalParsed = safeJsonParse(updatedUser.simulated_personality, {});
  console.log('✓ Final parsed result:', finalParsed);
  console.log('✓ Traits:', finalParsed.traits);
  
  // Clean up - remove the test user
  console.log('5. Cleaning up test user...');
  const deleteUser = db.prepare('DELETE FROM users WHERE id = ?');
  deleteUser.run(result.lastInsertRowid);
  console.log('✓ Test user deleted');
  
  console.log('\n🎉 JSON parsing fix test completed successfully!');
  console.log('The application should now handle malformed personality JSON gracefully.');
  
} catch (error) {
  console.error('❌ Test failed:', error);
} finally {
  db.close();
}
