<script lang="ts">
	import { onMount } from 'svelte';
	import AIUsageMetrics from '$lib/components/admin/AIUsageMetrics.svelte';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import { api } from '$lib/utils/api';

	// State
	let systemHealth: any = null;
	let isLoadingHealth = false;
	let healthError = '';

	/**
	 * Load system health information
	 */
	async function loadSystemHealth() {
		isLoadingHealth = true;
		healthError = '';

		try {
			const response = await api.get('/api/admin/system-health');

			if (response.success && response.data) {
				systemHealth = response.data;
			} else {
				healthError = response.error || 'Failed to load system health';
			}
		} catch (err) {
			console.error('Error loading system health:', err);
			healthError = 'An error occurred while loading system health';
		} finally {
			isLoadingHealth = false;
		}
	}

	/**
	 * Get health status color
	 */
	function getHealthColor(status: string): string {
		switch (status) {
			case 'healthy': return 'status-success';
			case 'warning': return 'status-warning';
			case 'critical': return 'status-danger';
			default: return 'status-secondary';
		}
	}

	/**
	 * Format uptime
	 */
	function formatUptime(seconds: number): string {
		const days = Math.floor(seconds / 86400);
		const hours = Math.floor((seconds % 86400) / 3600);
		const minutes = Math.floor((seconds % 3600) / 60);
		
		if (days > 0) {
			return `${days}d ${hours}h ${minutes}m`;
		} else if (hours > 0) {
			return `${hours}h ${minutes}m`;
		} else {
			return `${minutes}m`;
		}
	}

	// Load data on mount
	onMount(() => {
		loadSystemHealth();
		
		// Auto-refresh system health every minute
		const interval = setInterval(loadSystemHealth, 60000);
		return () => clearInterval(interval);
	});
</script>

<svelte:head>
	<title>AI Dashboard - FWFC Admin</title>
</svelte:head>

<div class="admin-page">
	<div class="page-header">
		<h1>AI Content Generation Dashboard</h1>
		<p class="page-description">
			Monitor AI usage, performance, and system health
		</p>
	</div>

	<!-- System Health Overview -->
	<div class="health-section">
		<h2>System Health</h2>
		
		{#if healthError}
			<ErrorMessage
				title="Health Check Error"
				message={healthError}
				type="error"
				dismissible={true}
				onDismiss={() => healthError = ''}
			/>
		{/if}

		{#if isLoadingHealth}
			<LoadingSpinner message="Checking system health..." />
		{:else if systemHealth}
			<div class="health-grid">
				<div class="health-card">
					<div class="health-header">
						<h3>Overall Status</h3>
						<span class="health-status {getHealthColor(systemHealth.overall.status)}">
							{systemHealth.overall.status.toUpperCase()}
						</span>
					</div>
					<div class="health-details">
						<p>{systemHealth.overall.message}</p>
						{#if systemHealth.overall.uptime}
							<div class="uptime">
								<strong>Uptime:</strong> {formatUptime(systemHealth.overall.uptime)}
							</div>
						{/if}
					</div>
				</div>

				<div class="health-card">
					<div class="health-header">
						<h3>AI Service</h3>
						<span class="health-status {getHealthColor(systemHealth.aiService.status)}">
							{systemHealth.aiService.status.toUpperCase()}
						</span>
					</div>
					<div class="health-details">
						<p>{systemHealth.aiService.message}</p>
						{#if systemHealth.aiService.responseTime}
							<div class="response-time">
								<strong>Response Time:</strong> {systemHealth.aiService.responseTime}ms
							</div>
						{/if}
					</div>
				</div>

				<div class="health-card">
					<div class="health-header">
						<h3>Database</h3>
						<span class="health-status {getHealthColor(systemHealth.database.status)}">
							{systemHealth.database.status.toUpperCase()}
						</span>
					</div>
					<div class="health-details">
						<p>{systemHealth.database.message}</p>
						{#if systemHealth.database.connectionCount !== undefined}
							<div class="connections">
								<strong>Active Connections:</strong> {systemHealth.database.connectionCount}
							</div>
						{/if}
					</div>
				</div>

				<div class="health-card">
					<div class="health-header">
						<h3>Rate Limiting</h3>
						<span class="health-status {getHealthColor(systemHealth.rateLimiting.status)}">
							{systemHealth.rateLimiting.status.toUpperCase()}
						</span>
					</div>
					<div class="health-details">
						<p>{systemHealth.rateLimiting.message}</p>
						{#if systemHealth.rateLimiting.activeWindows !== undefined}
							<div class="active-limits">
								<strong>Active Limits:</strong> {systemHealth.rateLimiting.activeWindows}
							</div>
						{/if}
					</div>
				</div>
			</div>
		{/if}
	</div>

	<!-- AI Usage Metrics -->
	<div class="metrics-section">
		<h2>Usage Metrics</h2>
		<AIUsageMetrics />
	</div>

	<!-- Quick Actions -->
	<div class="actions-section">
		<h2>Quick Actions</h2>
		<div class="actions-grid">
			<a href="/admin/ai-content-reviews" class="action-card">
				<div class="action-icon">📋</div>
				<div class="action-content">
					<h3>Review Content</h3>
					<p>Review and approve AI-generated content</p>
				</div>
			</a>

			<a href="/admin/post-as-user" class="action-card">
				<div class="action-icon">✨</div>
				<div class="action-content">
					<h3>Generate Content</h3>
					<p>Create new AI-generated content</p>
				</div>
			</a>

			<a href="/admin/users" class="action-card">
				<div class="action-icon">👥</div>
				<div class="action-content">
					<h3>Manage Users</h3>
					<p>Manage user accounts and permissions</p>
				</div>
			</a>

			<a href="/admin/audit-logs" class="action-card">
				<div class="action-icon">📊</div>
				<div class="action-content">
					<h3>Audit Logs</h3>
					<p>View system activity and logs</p>
				</div>
			</a>
		</div>
	</div>
</div>

<style>
	.admin-page {
		padding: 2rem;
		max-width: 1400px;
		margin: 0 auto;
	}

	.page-header {
		margin-bottom: 2rem;
	}

	.page-header h1 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 2rem;
	}

	.page-description {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 1.1rem;
	}

	.health-section,
	.metrics-section,
	.actions-section {
		margin-bottom: 3rem;
	}

	.health-section h2,
	.metrics-section h2,
	.actions-section h2 {
		margin: 0 0 1.5rem 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
		border-bottom: 2px solid var(--theme-accent-primary);
		padding-bottom: 0.5rem;
	}

	.health-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 1.5rem;
	}

	.health-card {
		background: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		padding: 1.5rem;
	}

	.health-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1rem;
	}

	.health-header h3 {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
	}

	.health-status {
		padding: 0.25rem 0.75rem;
		border-radius: 4px;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.status-success {
		background: var(--theme-accent-success);
		color: white;
	}

	.status-warning {
		background: var(--theme-accent-warning);
		color: white;
	}

	.status-danger {
		background: var(--theme-accent-danger);
		color: white;
	}

	.status-secondary {
		background: var(--theme-bg-tertiary);
		color: var(--theme-text-secondary);
	}

	.health-details p {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-secondary);
		line-height: 1.4;
	}

	.uptime,
	.response-time,
	.connections,
	.active-limits {
		font-size: 0.9rem;
		color: var(--theme-text-muted);
	}

	.actions-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1.5rem;
	}

	.action-card {
		display: flex;
		align-items: center;
		gap: 1rem;
		background: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		padding: 1.5rem;
		text-decoration: none;
		color: inherit;
		transition: all 0.2s ease;
	}

	.action-card:hover {
		border-color: var(--theme-accent-primary);
		transform: translateY(-2px);
		box-shadow: 0 4px 12px var(--theme-shadow-hover);
	}

	.action-icon {
		font-size: 2rem;
		flex-shrink: 0;
	}

	.action-content h3 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
	}

	.action-content p {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
		line-height: 1.4;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.admin-page {
			padding: 1rem;
		}

		.health-grid,
		.actions-grid {
			grid-template-columns: 1fr;
		}

		.action-card {
			flex-direction: column;
			text-align: center;
		}

		.action-icon {
			font-size: 2.5rem;
		}
	}

	/* High Contrast Mode */
	@media (prefers-contrast: high) {
		.health-card,
		.action-card {
			border: 2px solid currentColor;
		}

		.health-status {
			border: 1px solid currentColor;
		}
	}

	/* Reduced Motion */
	@media (prefers-reduced-motion: reduce) {
		.action-card {
			transition: none;
		}
	}
</style>
