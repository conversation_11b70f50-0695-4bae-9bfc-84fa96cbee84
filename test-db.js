import Database from 'better-sqlite3';

// Connect to the database
const db = new Database('local.db');

// Query to get all tables
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
console.log('Tables in database:', tables.map(t => t.name).join(', '));

// Query to get all users
const users = db.prepare('SELECT * FROM users').all();
console.log('Users in database:', users);

// Query to get all message of the day entries
const motd = db.prepare('SELECT * FROM message_of_the_day').all();
console.log('Message of the day entries:', motd);

// Close the database connection
db.close();
