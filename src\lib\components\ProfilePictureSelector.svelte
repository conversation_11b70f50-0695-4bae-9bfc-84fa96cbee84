<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import GalleryPicker from './admin/GalleryPicker.svelte';
	import ImageUploader from './admin/ImageUploader.svelte';

	const dispatch = createEventDispatcher();

	export let selectedImageUrl: string = '';
	export let disabled: boolean = false;
	export let required: boolean = false;

	// State
	let showGalleryPicker = false;
	let showUploader = false;
	let uploadMode: 'gallery' | 'upload' | null = null;

	// Handle image selection from gallery
	function handleGallerySelect(event: CustomEvent) {
		const { imageUrl, thumbnailUrl, title } = event.detail;
		selectedImageUrl = thumbnailUrl || imageUrl;
		
		dispatch('select', {
			imageUrl,
			thumbnailUrl,
			title,
			source: 'gallery'
		});
		
		showGalleryPicker = false;
		uploadMode = null;
	}

	// Handle image upload
	function handleImageUpload(event: CustomEvent) {
		const { imageUrl, thumbnailUrl, filename } = event.detail;
		selectedImageUrl = thumbnailUrl || imageUrl;
		
		dispatch('select', {
			imageUrl,
			thumbnailUrl,
			filename,
			source: 'upload'
		});
		
		showUploader = false;
		uploadMode = null;
	}

	// Open gallery picker
	function openGalleryPicker() {
		if (disabled) return;
		uploadMode = 'gallery';
		showGalleryPicker = true;
	}

	// Open uploader
	function openUploader() {
		if (disabled) return;
		uploadMode = 'upload';
		showUploader = true;
	}

	// Remove selected image
	function removeImage() {
		if (disabled) return;
		selectedImageUrl = '';
		dispatch('select', {
			imageUrl: '',
			thumbnailUrl: '',
			source: 'remove'
		});
	}

	// Close modals
	function closeGalleryPicker() {
		showGalleryPicker = false;
		uploadMode = null;
	}

	function closeUploader() {
		showUploader = false;
		uploadMode = null;
	}
</script>

<div class="profile-picture-selector" class:disabled>
	<div class="selector-label" role="group" aria-labelledby="profile-picture-label">
		<span id="profile-picture-label">Profile Picture {required ? '*' : '(Optional)'}</span>
	</div>
	
	<div class="selector-content">
		{#if selectedImageUrl}
			<!-- Selected Image Preview -->
			<div class="selected-image">
				<div class="image-preview">
					<img
						src={selectedImageUrl}
						alt=""
						loading="lazy"
					/>
					<div class="image-overlay">
						<button 
							class="overlay-button change-button"
							onclick={openGalleryPicker}
							disabled={disabled}
							type="button"
							aria-label="Change profile picture"
						>
							Change
						</button>
						<button 
							class="overlay-button remove-button"
							onclick={removeImage}
							disabled={disabled}
							type="button"
							aria-label="Remove profile picture"
						>
							Remove
						</button>
					</div>
				</div>
				<p class="image-info">
					Click "Change" to select a different image or "Remove" to clear selection
				</p>
			</div>
		{:else}
			<!-- Selection Options -->
			<div class="selection-options">
				<div class="option-buttons">
					<button 
						class="option-button gallery-button"
						onclick={openGalleryPicker}
						disabled={disabled}
						type="button"
						aria-describedby="gallery-description"
					>
						<span class="button-icon" aria-hidden="true">🖼️</span>
						<span class="button-text">Choose from Gallery</span>
					</button>
					
					<button 
						class="option-button upload-button"
						onclick={openUploader}
						disabled={disabled}
						type="button"
						aria-describedby="upload-description"
					>
						<span class="button-icon" aria-hidden="true">📁</span>
						<span class="button-text">Upload New Image</span>
					</button>
				</div>
				
				<div class="option-descriptions">
					<p id="gallery-description" class="option-description">
						Select from existing images in the FWFC gallery
					</p>
					<p id="upload-description" class="option-description">
						Upload a new image from your device (JPG, PNG, GIF, WebP - Max 10MB)
					</p>
				</div>
			</div>
		{/if}
	</div>
</div>

<!-- Gallery Picker Modal -->
<GalleryPicker 
	bind:isOpen={showGalleryPicker}
	{selectedImageUrl}
	on:select={handleGallerySelect}
	on:close={closeGalleryPicker}
/>

<!-- Image Uploader Modal -->
{#if showUploader}
	<div class="uploader-modal">
		<div
			class="modal-overlay"
			onclick={closeUploader}
			onkeydown={(e) => e.key === 'Escape' && closeUploader()}
			role="button"
			tabindex="0"
			aria-label="Close upload dialog"
		>
			<div
				class="modal-content"
				onclick={(e) => e.stopPropagation()}
				onkeydown={(e) => e.stopPropagation()}
				role="dialog"
				aria-modal="true"
				aria-labelledby="upload-title"
				tabindex="-1"
			>
				<div class="modal-header">
					<h3 id="upload-title">Upload Profile Picture</h3>
					<button 
						class="close-button"
						onclick={closeUploader}
						type="button"
						aria-label="Close upload dialog"
					>
						×
					</button>
				</div>
				<div class="modal-body">
					<ImageUploader 
						uploadEndpoint="/api/upload"
						buttonText="Select Profile Picture"
						on:upload={handleImageUpload}
					/>
				</div>
			</div>
		</div>
	</div>
{/if}

<style>
	.profile-picture-selector {
		margin-bottom: 1.5rem;
	}

	.profile-picture-selector.disabled {
		opacity: 0.6;
		pointer-events: none;
	}

	.selector-label {
		display: block;
		margin-bottom: 0.75rem;
		font-weight: 600;
		color: var(--theme-text-primary);
		font-size: 1rem;
	}

	.selector-content {
		border: 2px dashed var(--theme-border);
		border-radius: 8px;
		padding: 1.5rem;
		background-color: var(--theme-bg-secondary);
		transition: border-color 0.2s ease;
	}

	.selected-image {
		text-align: center;
	}

	.image-preview {
		position: relative;
		display: inline-block;
		width: 120px;
		height: 120px;
		border-radius: 50%;
		overflow: hidden;
		border: 3px solid var(--theme-border);
		background-color: var(--theme-bg-primary);
	}

	.image-preview img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.image-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		opacity: 0;
		transition: opacity 0.2s ease;
	}

	.image-preview:hover .image-overlay,
	.image-preview:focus-within .image-overlay {
		opacity: 1;
	}

	.overlay-button {
		background-color: var(--theme-accent-primary);
		color: white;
		border: none;
		border-radius: 4px;
		padding: 0.4rem 0.8rem;
		font-size: 0.8rem;
		font-weight: 600;
		cursor: pointer;
		transition: background-color 0.2s ease;
	}

	.overlay-button:hover {
		background-color: var(--theme-accent-primary-hover);
	}

	.remove-button {
		background-color: var(--theme-accent-danger);
	}

	.remove-button:hover {
		background-color: #c82333;
	}

	.image-info {
		margin-top: 1rem;
		font-size: 0.9rem;
		color: var(--theme-text-secondary);
		line-height: 1.4;
	}

	.selection-options {
		text-align: center;
	}

	.option-buttons {
		display: flex;
		gap: 1rem;
		justify-content: center;
		margin-bottom: 1rem;
	}

	.option-button {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 0.5rem;
		padding: 1.5rem 1rem;
		border: 2px solid var(--theme-border);
		border-radius: 8px;
		background-color: var(--theme-card-bg);
		color: var(--theme-text-primary);
		cursor: pointer;
		transition: all 0.2s ease;
		min-width: 140px;
	}

	.option-button:hover:not(:disabled) {
		border-color: var(--theme-accent-primary);
		background-color: var(--theme-bg-tertiary);
		transform: translateY(-2px);
		box-shadow: 0 4px 8px var(--theme-shadow-hover);
	}

	.option-button:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.button-icon {
		font-size: 2rem;
	}

	.button-text {
		font-weight: 600;
		font-size: 0.9rem;
	}

	.option-descriptions {
		display: grid;
		gap: 0.5rem;
	}

	.option-description {
		margin: 0;
		font-size: 0.85rem;
		color: var(--theme-text-muted);
		line-height: 1.4;
	}

	/* Uploader Modal Styles */
	.uploader-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1000;
	}

	.modal-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: var(--theme-gallery-overlay);
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 1rem;
	}

	.modal-content {
		background: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 8px;
		width: 100%;
		max-width: 600px;
		max-height: 90vh;
		overflow: hidden;
		color: var(--theme-text-primary);
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1.5rem;
		border-bottom: 1px solid var(--theme-border);
		background-color: var(--theme-bg-secondary);
	}

	.modal-header h3 {
		margin: 0;
		color: var(--theme-text-primary);
	}

	.close-button {
		background: none;
		border: none;
		font-size: 2rem;
		cursor: pointer;
		color: var(--theme-text-secondary);
		padding: 0;
		width: 2rem;
		height: 2rem;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: color 0.2s ease;
	}

	.close-button:hover {
		color: var(--theme-text-primary);
	}

	.modal-body {
		padding: 1.5rem;
	}

	/* Responsive Design */
	@media (max-width: 640px) {
		.option-buttons {
			flex-direction: column;
			align-items: center;
		}

		.option-button {
			width: 100%;
			max-width: 200px;
		}

		.image-preview {
			width: 100px;
			height: 100px;
		}

		.selector-content {
			padding: 1rem;
		}
	}

	/* Accessibility Enhancements */
	.option-button:focus {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
	}

	.overlay-button:focus {
		outline: 2px solid white;
		outline-offset: 1px;
	}

	/* High Contrast Mode */
	:global(.high-contrast) .image-preview {
		border-width: 4px;
	}

	:global(.high-contrast) .option-button {
		border-width: 3px;
	}

	:global(.high-contrast) .option-button:hover:not(:disabled) {
		border-width: 4px;
	}
</style>
