import { BaseResource, BaseProperty, BaseRecord, BaseDatabase } from 'adminjs';

/**
 * Simple SQLite adapter for AdminJS
 */

class SQLiteDatabase extends BaseDatabase {
  constructor(database) {
    super(database);
    this.database = database;
  }

  static isAdapterFor(database) {
    return database && typeof database.prepare === 'function';
  }

  resources() {
    try {
      // Get all tables from SQLite
      const tables = this.database.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").all();
      return tables.map(table => new SQLiteResource(this.database, table.name));
    } catch (error) {
      console.error('Error getting resources:', error);
      return [];
    }
  }
}
class SQLiteResource extends BaseResource {
  constructor(database, tableName, properties = {}) {
    super(database);
    this.database = database;
    this.tableName = tableName;
    this.customProperties = properties;
  }

  static isAdapterFor(resource) {
    return resource && resource.database && typeof resource.database.prepare === 'function';
  }

  databaseName() {
    return 'sqlite';
  }

  databaseType() {
    return 'sqlite';
  }

  name() {
    return this.tableName;
  }

  id() {
    return this.tableName;
  }

  properties() {
    // If custom properties are provided, use them
    if (Object.keys(this.customProperties).length > 0) {
      return Object.entries(this.customProperties).map(([key, options]) => {
        return new BaseProperty({
          path: key,
          type: options.type || 'string',
          isId: options.isId || false,
          isTitle: options.isTitle || false,
          isRequired: options.isRequired || false,
          isVisible: options.isVisible || {},
          availableValues: options.availableValues || null,
          reference: options.reference || null,
          components: options.components || {},
        });
      });
    }

    // Otherwise, try to infer properties from the database
    try {
      // Get table info from SQLite
      const tableInfo = this.database.prepare(`PRAGMA table_info(${this.tableName})`).all();

      return tableInfo.map(column => {
        const type = this._mapSqliteTypeToAdminJS(column.type);
        return new BaseProperty({
          path: column.name,
          type,
          isId: column.pk === 1,
          isRequired: column.notnull === 1,
        });
      });
    } catch (error) {
      console.error(`Error getting properties for table ${this.tableName}:`, error);
      return [];
    }
  }

  _mapSqliteTypeToAdminJS(sqliteType) {
    const type = sqliteType.toLowerCase();
    if (type.includes('int')) return 'number';
    if (type.includes('char') || type.includes('text')) return 'string';
    if (type.includes('real') || type.includes('floa') || type.includes('doub')) return 'float';
    if (type.includes('bool')) return 'boolean';
    if (type.includes('date')) return 'datetime';
    if (type.includes('blob')) return 'mixed';
    return 'string';
  }

  async count(filter) {
    try {
      const query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
      const result = this.database.prepare(query).get();
      return result.count;
    } catch (error) {
      console.error(`Error counting records in ${this.tableName}:`, error);
      return 0;
    }
  }

  async find(filter, { limit = 10, offset = 0, sort = {} } = {}) {
    try {
      const query = `SELECT * FROM ${this.tableName} LIMIT ? OFFSET ?`;
      const records = this.database.prepare(query).all(limit, offset);
      return records.map(record => new BaseRecord(record, this));
    } catch (error) {
      console.error(`Error finding records in ${this.tableName}:`, error);
      return [];
    }
  }

  async findOne(id) {
    try {
      const query = `SELECT * FROM ${this.tableName} WHERE id = ?`;
      const record = this.database.prepare(query).get(id);
      return record ? new BaseRecord(record, this) : null;
    } catch (error) {
      console.error(`Error finding record in ${this.tableName}:`, error);
      return null;
    }
  }

  async findMany(ids) {
    try {
      const placeholders = ids.map(() => '?').join(',');
      const query = `SELECT * FROM ${this.tableName} WHERE id IN (${placeholders})`;
      const records = this.database.prepare(query).all(...ids);
      return records.map(record => new BaseRecord(record, this));
    } catch (error) {
      console.error(`Error finding records in ${this.tableName}:`, error);
      return [];
    }
  }

  async create(params) {
    try {
      const keys = Object.keys(params);
      const values = Object.values(params);
      const placeholders = keys.map(() => '?').join(',');
      const query = `INSERT INTO ${this.tableName} (${keys.join(',')}) VALUES (${placeholders})`;
      const result = this.database.prepare(query).run(...values);

      if (result.lastInsertRowid) {
        return this.findOne(result.lastInsertRowid);
      }
      return null;
    } catch (error) {
      console.error(`Error creating record in ${this.tableName}:`, error);
      throw error;
    }
  }

  async update(id, params) {
    try {
      const keys = Object.keys(params);
      const values = Object.values(params);
      const setClause = keys.map(key => `${key} = ?`).join(',');
      const query = `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`;
      this.database.prepare(query).run(...values, id);
      return this.findOne(id);
    } catch (error) {
      console.error(`Error updating record in ${this.tableName}:`, error);
      throw error;
    }
  }

  async delete(id) {
    try {
      const query = `DELETE FROM ${this.tableName} WHERE id = ?`;
      this.database.prepare(query).run(id);
      return true;
    } catch (error) {
      console.error(`Error deleting record in ${this.tableName}:`, error);
      throw error;
    }
  }
}

export { SQLiteDatabase, SQLiteResource };
