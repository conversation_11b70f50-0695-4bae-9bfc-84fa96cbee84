import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users, news, gallery, comments, messages, interactionTemplates, scheduledContent, contentAuthorship, auditLogs } from '$lib/server/db/schema';
import { eq, and, inArray } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// POST /api/admin/simulate-interactions - Generate simulated user interactions
export const POST: RequestHandler = async ({ request, locals, getClientAddress }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const body = await request.json();
    const { 
      templateId, 
      customMessage, 
      userIds, 
      contentType, 
      contentId, 
      generateVariations, 
      maxInteractions, 
      scheduledFor 
    } = body;

    // Validate required fields
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return json({
        success: false,
        error: 'User IDs are required'
      }, { status: 400 });
    }

    if (!contentType || !contentId) {
      return json({
        success: false,
        error: 'Content type and ID are required'
      }, { status: 400 });
    }

    if (!templateId && !customMessage) {
      return json({
        success: false,
        error: 'Either template ID or custom message is required'
      }, { status: 400 });
    }

    // Validate content type
    if (!['news', 'gallery'].includes(contentType)) {
      return json({
        success: false,
        error: 'Invalid content type'
      }, { status: 400 });
    }

    // Get template if specified
    let template = null;
    if (templateId) {
      const templateResult = await db.select()
        .from(interactionTemplates)
        .where(eq(interactionTemplates.id, templateId))
        .limit(1);

      if (templateResult.length === 0) {
        return json({
          success: false,
          error: 'Template not found'
        }, { status: 404 });
      }

      template = templateResult[0];
    }

    // Verify users exist and are simulated
    const targetUsers = await db.select()
      .from(users)
      .where(and(
        inArray(users.id, userIds),
        eq(users.isSimulated, true),
        eq(users.status, 'active')
      ));

    if (targetUsers.length === 0) {
      return json({
        success: false,
        error: 'No valid simulated users found'
      }, { status: 404 });
    }

    // Verify content exists
    let contentItem = null;
    if (contentType === 'news') {
      const newsResult = await db.select()
        .from(news)
        .where(eq(news.id, contentId))
        .limit(1);
      
      if (newsResult.length === 0) {
        return json({
          success: false,
          error: 'News article not found'
        }, { status: 404 });
      }
      contentItem = newsResult[0];
    } else if (contentType === 'gallery') {
      const galleryResult = await db.select()
        .from(gallery)
        .where(eq(gallery.id, contentId))
        .limit(1);
      
      if (galleryResult.length === 0) {
        return json({
          success: false,
          error: 'Gallery item not found'
        }, { status: 404 });
      }
      contentItem = galleryResult[0];
    }

    const interactions = [];
    const scheduledItems = [];

    // Generate interactions for each user
    for (const user of targetUsers) {
      const userInteractionCount = Math.min(
        maxInteractions || 1,
        Math.floor(Math.random() * (maxInteractions || 1)) + 1
      );

      for (let i = 0; i < userInteractionCount; i++) {
        // Generate message content
        let messageContent = '';
        
        if (template) {
          messageContent = await generateMessageFromTemplate(template, user, contentItem);
        } else {
          messageContent = await personalizeCustomMessage(customMessage, user, contentItem);
        }

        // Add variations if enabled
        if (generateVariations && i > 0) {
          messageContent = await generateVariation(messageContent);
        }

        const interactionData = {
          userId: user.id,
          content: messageContent,
          itemType: contentType as 'news' | 'gallery',
          itemId: contentId,
          approved: true
        };

        if (scheduledFor) {
          // Create scheduled interaction
          const scheduledData = {
            contentType: 'comment',
            contentData: JSON.stringify(interactionData),
            asUserId: user.id,
            scheduledFor,
            createdByUserId: locals.user.id
          };

          const scheduledResult = await db.insert(scheduledContent)
            .values(scheduledData)
            .returning();

          scheduledItems.push(scheduledResult[0]);
        } else {
          // Create immediate interaction
          const commentResult = await db.insert(comments)
            .values(interactionData)
            .returning();

          // Record content authorship
          await db.insert(contentAuthorship).values({
            contentType: 'comment',
            contentId: commentResult[0].id,
            actualAuthorId: locals.user.id,
            displayAuthorId: user.id,
            isSimulated: true
          });

          // Update user's last active time
          await db.update(users)
            .set({ lastActiveAt: new Date().toISOString() })
            .where(eq(users.id, user.id));

          interactions.push(commentResult[0]);
        }
      }
    }

    // Log admin action
    await logAdminAction(
      locals.user.id,
      'simulate_interactions',
      contentType,
      contentId,
      null,
      {
        templateId,
        customMessage: customMessage ? 'Used custom message' : null,
        userCount: targetUsers.length,
        interactionCount: interactions.length + scheduledItems.length,
        scheduled: !!scheduledFor,
        generateVariations
      },
      getClientAddress()
    );

    logger.info('Interactions simulated', {
      adminUser: locals.user.username,
      userCount: targetUsers.length,
      interactionCount: interactions.length + scheduledItems.length,
      contentType,
      contentId,
      scheduled: !!scheduledFor
    });

    return json({
      success: true,
      data: {
        interactions,
        scheduledItems,
        userCount: targetUsers.length,
        totalInteractions: interactions.length + scheduledItems.length
      },
      message: scheduledFor 
        ? `${interactions.length + scheduledItems.length} interactions scheduled successfully`
        : `${interactions.length} interactions generated successfully`
    });

  } catch (error) {
    logger.error('Error simulating interactions:', error);
    return json({
      success: false,
      error: 'Failed to simulate interactions'
    }, { status: 500 });
  }
};

// Helper function to generate message from template
async function generateMessageFromTemplate(template: any, user: any, contentItem: any): Promise<string> {
  let message = template.template;
  const variables = JSON.parse(template.variables || '[]');

  // Replace variables
  variables.forEach((variable: string) => {
    const placeholder = `{${variable}}`;
    switch (variable) {
      case 'username':
        message = message.replace(placeholder, user.displayName);
        break;
      case 'topic':
        message = message.replace(placeholder, contentItem.title || 'this content');
        break;
      default:
        message = message.replace(placeholder, `[${variable}]`);
    }
  });

  return message;
}

// Helper function to personalize custom message
async function personalizeCustomMessage(customMessage: string, user: any, contentItem: any): Promise<string> {
  let message = customMessage;
  
  // Replace common variables
  message = message.replace(/{username}/g, user.displayName);
  message = message.replace(/{topic}/g, contentItem.title || 'this content');
  
  return message;
}

// Helper function to generate variations
async function generateVariation(originalMessage: string): Promise<string> {
  // Simple variation generation - in a real implementation, you might use AI
  const variations = [
    (msg: string) => msg.replace(/!/g, '.'),
    (msg: string) => msg.replace(/amazing/g, 'fantastic'),
    (msg: string) => msg.replace(/love/g, 'really like'),
    (msg: string) => msg.replace(/great/g, 'awesome'),
    (msg: string) => msg + ' 👍',
    (msg: string) => msg.replace(/this/g, 'this really')
  ];

  const randomVariation = variations[Math.floor(Math.random() * variations.length)];
  return randomVariation(originalMessage);
}

// Helper function to log admin actions
async function logAdminAction(
  adminUserId: number,
  action: string,
  targetType: string,
  targetId: number | null,
  targetUserId: number | null,
  details: any,
  ipAddress: string
) {
  try {
    await db.insert(auditLogs).values({
      adminUserId,
      action,
      targetType,
      targetId,
      targetUserId,
      details: JSON.stringify(details),
      ipAddress
    });
  } catch (error) {
    logger.error('Failed to log admin action:', error);
  }
}
