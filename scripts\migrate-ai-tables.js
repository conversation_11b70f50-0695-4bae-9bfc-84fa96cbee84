import Database from 'better-sqlite3';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Read the SQL migration file
const migrationSQL = readFileSync(join(__dirname, '../migrations/add_ai_tables.sql'), 'utf8');

// Connect to the database
const db = new Database('local.db');

try {
  console.log('Running AI tables migration...');
  
  // Split the SQL into individual statements and execute them
  const statements = migrationSQL
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);
  
  for (const statement of statements) {
    try {
      db.exec(statement);
      console.log('✓ Executed:', statement.substring(0, 50) + '...');
    } catch (error) {
      console.log('⚠ Skipped (already exists):', statement.substring(0, 50) + '...');
    }
  }
  
  console.log('✅ AI tables migration completed successfully!');
} catch (error) {
  console.error('❌ Migration failed:', error);
  process.exit(1);
} finally {
  db.close();
}
