import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { aiContentReviews, users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// GET /api/admin/ai-content-reviews/[id] - Get specific AI content review
export const GET: RequestHandler = async ({ params, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const reviewId = parseInt(params.id);
    if (isNaN(reviewId)) {
      return json({
        success: false,
        error: 'Invalid review ID'
      }, { status: 400 });
    }

    // Get review with user information
    const review = await db
      .select({
        id: aiContentReviews.id,
        contentType: aiContentReviews.contentType,
        originalPrompt: aiContentReviews.originalPrompt,
        generatedContent: aiContentReviews.generatedContent,
        authenticityScore: aiContentReviews.authenticityScore,
        aiConfig: aiContentReviews.aiConfig,
        reviewStatus: aiContentReviews.reviewStatus,
        reviewNotes: aiContentReviews.reviewNotes,
        reviewedAt: aiContentReviews.reviewedAt,
        createdAt: aiContentReviews.createdAt,
        updatedAt: aiContentReviews.updatedAt,
        targetUser: {
          id: users.id,
          displayName: users.displayName,
          username: users.username,
          avatarUrl: users.avatarUrl,
          isSimulated: users.isSimulated,
          simulatedPersonality: users.simulatedPersonality
        }
      })
      .from(aiContentReviews)
      .leftJoin(users, eq(aiContentReviews.targetUserId, users.id))
      .where(eq(aiContentReviews.id, reviewId))
      .limit(1);

    if (review.length === 0) {
      return json({
        success: false,
        error: 'Review not found'
      }, { status: 404 });
    }

    const reviewData = {
      ...review[0],
      generatedContent: JSON.parse(review[0].generatedContent),
      aiConfig: JSON.parse(review[0].aiConfig)
    };

    return json({
      success: true,
      data: reviewData
    });
  } catch (error) {
    logger.error('Error fetching AI content review:', error);
    return json({
      success: false,
      error: 'Failed to fetch AI content review'
    }, { status: 500 });
  }
};

// PATCH /api/admin/ai-content-reviews/[id] - Update AI content review
export const PATCH: RequestHandler = async ({ params, request, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const reviewId = parseInt(params.id);
    if (isNaN(reviewId)) {
      return json({
        success: false,
        error: 'Invalid review ID'
      }, { status: 400 });
    }

    const body = await request.json();
    const { reviewStatus, reviewNotes, generatedContent } = body;

    // Validate review status
    if (reviewStatus && !['pending', 'approved', 'rejected', 'needs_revision'].includes(reviewStatus)) {
      return json({
        success: false,
        error: 'Invalid review status'
      }, { status: 400 });
    }

    // Check if review exists
    const existingReview = await db
      .select()
      .from(aiContentReviews)
      .where(eq(aiContentReviews.id, reviewId))
      .limit(1);

    if (existingReview.length === 0) {
      return json({
        success: false,
        error: 'Review not found'
      }, { status: 404 });
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date().toISOString()
    };

    if (reviewStatus) {
      updateData.reviewStatus = reviewStatus;
      updateData.reviewedById = locals.user.id;
      updateData.reviewedAt = new Date().toISOString();
    }

    if (reviewNotes !== undefined) {
      updateData.reviewNotes = reviewNotes;
    }

    if (generatedContent) {
      updateData.generatedContent = JSON.stringify(generatedContent);
    }

    // Update the review
    const result = await db
      .update(aiContentReviews)
      .set(updateData)
      .where(eq(aiContentReviews.id, reviewId))
      .returning();

    logger.info('AI content review updated', {
      reviewId,
      reviewStatus,
      reviewedBy: locals.user.username,
      hasNotes: !!reviewNotes
    });

    return json({
      success: true,
      data: result[0]
    });
  } catch (error) {
    logger.error('Error updating AI content review:', error);
    return json({
      success: false,
      error: 'Failed to update AI content review'
    }, { status: 500 });
  }
};

// DELETE /api/admin/ai-content-reviews/[id] - Delete AI content review
export const DELETE: RequestHandler = async ({ params, locals }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const reviewId = parseInt(params.id);
    if (isNaN(reviewId)) {
      return json({
        success: false,
        error: 'Invalid review ID'
      }, { status: 400 });
    }

    // Check if review exists
    const existingReview = await db
      .select()
      .from(aiContentReviews)
      .where(eq(aiContentReviews.id, reviewId))
      .limit(1);

    if (existingReview.length === 0) {
      return json({
        success: false,
        error: 'Review not found'
      }, { status: 404 });
    }

    // Delete the review
    await db
      .delete(aiContentReviews)
      .where(eq(aiContentReviews.id, reviewId));

    logger.info('AI content review deleted', {
      reviewId,
      deletedBy: locals.user.username
    });

    return json({
      success: true,
      message: 'Review deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting AI content review:', error);
    return json({
      success: false,
      error: 'Failed to delete AI content review'
    }, { status: 500 });
  }
};
