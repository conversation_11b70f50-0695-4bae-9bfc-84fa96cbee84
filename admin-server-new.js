import AdminJS from 'adminjs';
import { Database, Resource } from '@adminjs/sql';
import express from 'express';
import AdminJSExpress from '@adminjs/express';
import session from 'express-session';
import formidable from 'express-formidable';
import Database_SQLite from 'better-sqlite3';
import { ComponentLoader } from 'adminjs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Register the SQL adapter
AdminJS.registerAdapter({ Database, Resource });

// Create component loader
const componentLoader = new ComponentLoader();
const Components = {
  Dashboard: componentLoader.add('Dashboard', path.join(__dirname, 'src/lib/server/admin/components/dashboard.jsx')),
};

// Connect to the database
const databaseUrl = process.env.DATABASE_URL || 'local.db';
const sqlite = new Database_SQLite(databaseUrl);

// Simple authentication function
const authenticate = async (email, password) => {
  // In a real app, you'd check against the database
  if (email === '<EMAIL>' && password === 'admin') {
    return {
      email: '<EMAIL>',
      role: 'admin',
    };
  }
  return null;
};

// Create AdminJS instance
const admin = new AdminJS({
  rootPath: '/admin',
  componentLoader,
  branding: {
    companyName: 'Finn Wolfhard Fan Club Admin',
    logo: '/images/logo.png',
    favicon: '/favicon.png',
    withMadeWithLove: false,
  },
  dashboard: {
    handler: async () => {
      return { message: 'Welcome to Finn Wolfhard Fan Club Admin Panel' };
    },
    component: Components.Dashboard,
  },
  resources: [
    {
      resource: { table: 'users', database: sqlite },
      options: {
        navigation: {
          name: 'User Management',
          icon: 'User',
        },
        properties: {
          password_hash: {
            isVisible: false,
          },
          preferences: {
            type: 'mixed',
          },
        },
      },
    },
    {
      resource: { table: 'news', database: sqlite },
      options: {
        navigation: {
          name: 'Content',
          icon: 'Document',
        },
      },
    },
    {
      resource: { table: 'gallery', database: sqlite },
      options: {
        navigation: {
          name: 'Content',
          icon: 'Document',
        },
      },
    },
    {
      resource: { table: 'messages', database: sqlite },
      options: {
        navigation: {
          name: 'Community',
          icon: 'Chat',
        },
      },
    },
    {
      resource: { table: 'replies', database: sqlite },
      options: {
        navigation: {
          name: 'Community',
          icon: 'Chat',
        },
      },
    },
    {
      resource: { table: 'comments', database: sqlite },
      options: {
        navigation: {
          name: 'Community',
          icon: 'Chat',
        },
      },
    },
    {
      resource: { table: 'site_settings', database: sqlite },
      options: {
        navigation: {
          name: 'Settings',
          icon: 'Settings',
        },
        properties: {
          settings: {
            type: 'mixed',
          },
        },
      },
    },
    {
      resource: { table: 'message_of_the_day', database: sqlite },
      options: {
        navigation: {
          name: 'Settings',
          icon: 'Settings',
        },
      },
    },
  ],
});

// Enable development mode
admin.watch();

// Create Express app
const app = express();
app.use(formidable());

// Create AdminJS router with authentication
const router = AdminJSExpress.buildAuthenticatedRouter(
  admin,
  {
    authenticate,
    cookieName: 'adminjs',
    cookiePassword: 'some-secret-password-used-to-secure-cookie',
  },
  null,
  {
    resave: false,
    saveUninitialized: true,
    secret: 'session-secret',
    cookie: {
      httpOnly: process.env.NODE_ENV === 'production',
      secure: process.env.NODE_ENV === 'production',
    },
    name: 'adminjs',
  }
);

// Use the router
app.use(admin.options.rootPath, router);

// Start the server
const PORT = process.env.ADMIN_PORT || 3001;
app.listen(PORT, () => {
  console.log(`AdminJS started on http://localhost:${PORT}${admin.options.rootPath}`);
});
