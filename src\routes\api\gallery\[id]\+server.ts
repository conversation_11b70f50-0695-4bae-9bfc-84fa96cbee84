import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { gallery } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/gallery/[id] - Get a specific gallery item
export const GET: RequestHandler = async ({ params }) => {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }

    // Fetch the gallery item from the database
    const item = await db.select()
      .from(gallery)
      .where(eq(gallery.id, id))
      .limit(1);

    if (item.length === 0) {
      return json({
        success: false,
        error: 'Gallery item not found'
      }, { status: 404 });
    }

    return json({
      success: true,
      data: item[0]
    });
  } catch (error) {
    console.error('Error fetching gallery item:', error);
    return json({
      success: false,
      error: 'Failed to fetch gallery item'
    }, { status: 500 });
  }
};

// PUT /api/gallery/[id] - Update a gallery item (admin only)
export const PUT: RequestHandler = async ({ params, request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }

    const body = await request.json();

    // Update the gallery item in the database
    const result = await db.update(gallery)
      .set({
        title: body.title,
        description: body.description,
        imageUrl: body.imageUrl,
        thumbnailUrl: body.thumbnailUrl,
        published: body.published,
        updatedAt: new Date().toISOString()
      })
      .where(eq(gallery.id, id))
      .returning();

    if (result.length === 0) {
      return json({
        success: false,
        error: 'Gallery item not found'
      }, { status: 404 });
    }

    return json({
      success: true,
      data: result[0]
    });
  } catch (error) {
    console.error('Error updating gallery item:', error);
    return json({
      success: false,
      error: 'Failed to update gallery item'
    }, { status: 500 });
  }
};

// DELETE /api/gallery/[id] - Delete a gallery item (admin only)
export const DELETE: RequestHandler = async ({ params, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }

    // Delete the gallery item from the database
    const result = await db.delete(gallery)
      .where(eq(gallery.id, id))
      .returning();

    if (result.length === 0) {
      return json({
        success: false,
        error: 'Gallery item not found'
      }, { status: 404 });
    }

    return json({
      success: true,
      data: result[0]
    });
  } catch (error) {
    console.error('Error deleting gallery item:', error);
    return json({
      success: false,
      error: 'Failed to delete gallery item'
    }, { status: 500 });
  }
};
