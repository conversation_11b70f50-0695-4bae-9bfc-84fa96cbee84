import { error } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { news } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
  const id = parseInt(params.id);

  if (isNaN(id)) {
    throw error(400, 'Invalid article ID');
  }

  try {
    // Fetch the news article from the database
    const article = await db.select()
      .from(news)
      .where(eq(news.id, id))
      .limit(1);

    if (article.length === 0) {
      throw error(404, 'News article not found');
    }

    return {
      article: article[0]
    };
  } catch (err) {
    console.error('Error fetching news article:', err);
    throw error(500, 'Failed to fetch news article');
  }
};
