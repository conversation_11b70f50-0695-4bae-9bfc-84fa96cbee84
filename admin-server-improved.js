import AdminJS from 'adminjs';
import express from 'express';
import AdminJSExpress from '@adminjs/express';
import session from 'express-session';
import formidable from 'express-formidable';
import Database_SQLite from 'better-sqlite3';
import { ComponentLoader } from 'adminjs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { SQLiteDatabase, SQLiteResource } from './src/lib/server/admin/sqlite-adapter.js';

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Register our custom SQLite adapter
AdminJS.registerAdapter({ Database: SQLiteDatabase, Resource: SQLiteResource });

// Connect to the database
const databaseUrl = process.env.DATABASE_URL || 'local.db';
const sqlite = new Database_SQLite(databaseUrl);

// Create component loader
const componentLoader = new ComponentLoader();
const Components = {
  Dashboard: componentLoader.add('Dashboard', path.join(__dirname, 'src/lib/server/admin/components/dashboard.jsx')),
  RichTextViewer: componentLoader.add('RichTextViewer', path.join(__dirname, 'src/lib/server/admin/components/richtext-viewer.jsx')),
};

// Define table schemas and relationships
const tables = {
  users: {
    name: 'users',
    properties: {
      id: { type: 'number', isId: true },
      username: { type: 'string' },
      displayName: { type: 'string' },
      email: { type: 'string' },
      passwordHash: { type: 'string', isVisible: { list: false, filter: false, show: false, edit: false } },
      role: {
        type: 'string',
        availableValues: [
          { value: 'admin', label: 'Administrator' },
          { value: 'moderator', label: 'Moderator' },
          { value: 'user', label: 'Regular User' }
        ]
      },
      preferences: { type: 'mixed' },
      createdAt: { type: 'datetime', isVisible: { list: true, filter: true, show: true, edit: false } },
      updatedAt: { type: 'datetime', isVisible: { list: true, filter: true, show: true, edit: false } }
    }
  },
  news: {
    name: 'news',
    properties: {
      id: { type: 'number', isId: true },
      title: { type: 'string' },
      content: { type: 'string', components: { show: Components.RichTextViewer } },
      imageUrl: { type: 'string' },
      authorId: { type: 'reference', reference: 'users' },
      published: { type: 'boolean' },
      createdAt: { type: 'datetime', isVisible: { list: true, filter: true, show: true, edit: false } },
      updatedAt: { type: 'datetime', isVisible: { list: true, filter: true, show: true, edit: false } }
    }
  },
  gallery: {
    name: 'gallery',
    properties: {
      id: { type: 'number', isId: true },
      title: { type: 'string' },
      description: { type: 'string' },
      imageUrl: { type: 'string' },
      thumbnailUrl: { type: 'string' },
      authorId: { type: 'reference', reference: 'users' },
      published: { type: 'boolean' },
      createdAt: { type: 'datetime', isVisible: { list: true, filter: true, show: true, edit: false } },
      updatedAt: { type: 'datetime', isVisible: { list: true, filter: true, show: true, edit: false } }
    }
  },
  site_settings: {
    name: 'site_settings',
    properties: {
      id: { type: 'number', isId: true },
      settings: { type: 'mixed' },
      createdAt: { type: 'datetime', isVisible: { list: true, filter: true, show: true, edit: false } },
      updatedAt: { type: 'datetime', isVisible: { list: true, filter: true, show: true, edit: false } }
    }
  },
  message_of_the_day: {
    name: 'message_of_the_day',
    properties: {
      id: { type: 'number', isId: true },
      content: { type: 'string' },
      active: { type: 'boolean' },
      createdAt: { type: 'datetime', isVisible: { list: true, filter: true, show: true, edit: false } }
    }
  }
};

// Simple authentication function
const authenticate = async (email, password) => {
  // In a real app, you'd check against the database
  if (email === '<EMAIL>' && password === 'admin') {
    return {
      email: '<EMAIL>',
      role: 'admin',
    };
  }
  return null;
};

// Create AdminJS instance
const admin = new AdminJS({
  rootPath: '/admin',
  componentLoader,
  branding: {
    companyName: 'Finn Wolfhard Fan Club Admin',
    logo: '/images/logo.png',
    favicon: '/favicon.png',
    withMadeWithLove: false,
  },
  dashboard: {
    handler: async () => {
      return { message: 'Welcome to Finn Wolfhard Fan Club Admin Panel' };
    },
    component: Components.Dashboard,
  },
  resources: Object.entries(tables).map(([tableName, tableConfig]) => ({
    resource: { database: sqlite, tableName: tableConfig.name },
    options: {
      properties: tableConfig.properties,
      navigation: {
        name: getNavigationCategory(tableConfig.name),
        icon: getNavigationIcon(tableConfig.name),
      },
    },
  })),
});

// Helper functions for navigation
function getNavigationCategory(tableName) {
  switch (tableName) {
    case 'users':
      return 'User Management';
    case 'news':
    case 'gallery':
      return 'Content';
    case 'messages':
    case 'replies':
    case 'comments':
      return 'Community';
    case 'site_settings':
    case 'message_of_the_day':
      return 'Settings';
    default:
      return 'Other';
  }
}

function getNavigationIcon(tableName) {
  switch (tableName) {
    case 'users':
      return 'User';
    case 'news':
    case 'gallery':
      return 'Document';
    case 'messages':
    case 'replies':
    case 'comments':
      return 'Chat';
    case 'site_settings':
    case 'message_of_the_day':
      return 'Settings';
    default:
      return 'Box';
  }
}

// Enable development mode
admin.watch();

// Create Express app
const app = express();
app.use(formidable());

// Create AdminJS router with authentication
const router = AdminJSExpress.buildAuthenticatedRouter(
  admin,
  {
    authenticate,
    cookieName: 'adminjs',
    cookiePassword: 'some-secret-password-used-to-secure-cookie',
  },
  null,
  {
    resave: false,
    saveUninitialized: true,
    secret: 'session-secret',
    cookie: {
      httpOnly: process.env.NODE_ENV === 'production',
      secure: process.env.NODE_ENV === 'production',
    },
    name: 'adminjs',
  }
);

// Use the router
app.use(admin.options.rootPath, router);

// Start the server
const PORT = process.env.ADMIN_PORT || 3001;
app.listen(PORT, () => {
  console.log(`AdminJS started on http://localhost:${PORT}${admin.options.rootPath}`);
});
