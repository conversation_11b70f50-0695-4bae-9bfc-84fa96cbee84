<script lang="ts">
	import { onMount } from 'svelte';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import AIContentReview from '$lib/components/admin/AIContentReview.svelte';
	import { api } from '$lib/utils/api';

	// State
	let reviews: any[] = [];
	let isLoading = false;
	let error = '';
	let successMessage = '';

	// Pagination and filtering
	let currentPage = 1;
	let totalPages = 1;
	let limit = 20;
	let statusFilter = 'all';
	let contentTypeFilter = 'all';
	let searchQuery = '';

	// Review modal
	let showReviewModal = false;
	let selectedReviewId: number | null = null;

	// Filter options
	const statusOptions = [
		{ value: 'all', label: 'All Statuses' },
		{ value: 'pending', label: 'Pending Review' },
		{ value: 'approved', label: 'Approved' },
		{ value: 'rejected', label: 'Rejected' },
		{ value: 'needs_revision', label: 'Needs Revision' }
	];

	const contentTypeOptions = [
		{ value: 'all', label: 'All Types' },
		{ value: 'news', label: 'News Articles' },
		{ value: 'gallery', label: 'Gallery Items' },
		{ value: 'comment', label: 'Comments' },
		{ value: 'message', label: 'Messages' }
	];

	/**
	 * Load reviews with current filters
	 */
	async function loadReviews() {
		isLoading = true;
		error = '';

		try {
			const params = new URLSearchParams({
				page: currentPage.toString(),
				limit: limit.toString(),
				status: statusFilter,
				contentType: contentTypeFilter,
				search: searchQuery,
				sortBy: 'createdAt',
				sortOrder: 'desc'
			});

			const response = await api.get(`/api/admin/ai-content-reviews?${params}`);

			if (response.success && response.data) {
				reviews = response.data.reviews;
				totalPages = response.data.pagination.totalPages;
			} else {
				error = response.error || 'Failed to load reviews';
			}
		} catch (err) {
			console.error('Error loading reviews:', err);
			error = 'An error occurred while loading reviews';
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Handle filter changes
	 */
	function handleFilterChange() {
		currentPage = 1;
		loadReviews();
	}

	/**
	 * Handle pagination
	 */
	function goToPage(page: number) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
			loadReviews();
		}
	}

	/**
	 * Open review modal
	 */
	function openReview(reviewId: number) {
		selectedReviewId = reviewId;
		showReviewModal = true;
	}

	/**
	 * Handle review update
	 */
	function handleReviewUpdate(event: CustomEvent) {
		const { reviewId, status } = event.detail;
		
		// Update the review in the list
		const reviewIndex = reviews.findIndex(r => r.id === reviewId);
		if (reviewIndex !== -1) {
			reviews[reviewIndex].reviewStatus = status;
			reviews = [...reviews]; // Trigger reactivity
		}

		successMessage = `Review ${status} successfully`;
		setTimeout(() => successMessage = '', 3000);
	}

	/**
	 * Handle content approval
	 */
	function handleContentApproval(event: CustomEvent) {
		const { reviewId, content, targetUser } = event.detail;
		
		successMessage = `Content approved and ready for posting as ${targetUser.displayName}`;
		setTimeout(() => successMessage = '', 5000);
		
		// Optionally redirect to post-as-user page with pre-filled content
		// window.location.href = `/admin/post-as-user?reviewId=${reviewId}`;
	}

	/**
	 * Get status badge class
	 */
	function getStatusClass(status: string) {
		switch (status) {
			case 'approved': return 'status-success';
			case 'rejected': return 'status-danger';
			case 'needs_revision': return 'status-info';
			case 'pending': return 'status-warning';
			default: return 'status-secondary';
		}
	}

	/**
	 * Get authenticity score class
	 */
	function getScoreClass(score: number) {
		if (score >= 80) return 'score-high';
		if (score >= 60) return 'score-medium';
		return 'score-low';
	}

	/**
	 * Format date
	 */
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// Load reviews on mount
	onMount(() => {
		loadReviews();
	});
</script>

<svelte:head>
	<title>AI Content Reviews - FWFC Admin</title>
</svelte:head>

<div class="admin-page">
	<div class="page-header">
		<h1>AI Content Reviews</h1>
		<p class="page-description">
			Review and approve AI-generated content before publishing
		</p>
	</div>

	{#if successMessage}
		<div class="success-message" role="alert">
			✅ {successMessage}
		</div>
	{/if}

	{#if error}
		<ErrorMessage
			title="Error"
			message={error}
			type="error"
			dismissible={true}
			onDismiss={() => error = ''}
		/>
	{/if}

	<!-- Filters -->
	<div class="filters-section">
		<div class="filters-row">
			<div class="filter-group">
				<label for="status-filter">Status</label>
				<select
					id="status-filter"
					bind:value={statusFilter}
					onchange={handleFilterChange}
				>
					{#each statusOptions as option}
						<option value={option.value}>{option.label}</option>
					{/each}
				</select>
			</div>

			<div class="filter-group">
				<label for="content-type-filter">Content Type</label>
				<select
					id="content-type-filter"
					bind:value={contentTypeFilter}
					onchange={handleFilterChange}
				>
					{#each contentTypeOptions as option}
						<option value={option.value}>{option.label}</option>
					{/each}
				</select>
			</div>

			<div class="filter-group">
				<label for="search-input">Search</label>
				<input
					id="search-input"
					type="text"
					placeholder="Search prompts or notes..."
					bind:value={searchQuery}
					onkeydown={(e) => e.key === 'Enter' && handleFilterChange()}
				/>
			</div>

			<div class="filter-actions">
				<button class="btn secondary" onclick={handleFilterChange}>
					🔍 Search
				</button>
				<button 
					class="btn secondary" 
					onclick={() => {
						statusFilter = 'all';
						contentTypeFilter = 'all';
						searchQuery = '';
						handleFilterChange();
					}}
				>
					🔄 Reset
				</button>
			</div>
		</div>
	</div>

	<!-- Reviews List -->
	<div class="reviews-section">
		{#if isLoading}
			<LoadingSpinner message="Loading reviews..." />
		{:else if reviews.length === 0}
			<div class="empty-state">
				<h3>No reviews found</h3>
				<p>No AI content reviews match your current filters.</p>
			</div>
		{:else}
			<div class="reviews-grid">
				{#each reviews as review}
					<div class="review-card" onclick={() => openReview(review.id)}>
						<div class="review-header">
							<div class="review-meta">
								<span class="content-type">{review.contentType}</span>
								<span class="status-badge {getStatusClass(review.reviewStatus)}">
									{statusOptions.find(opt => opt.value === review.reviewStatus)?.label || review.reviewStatus}
								</span>
							</div>
							<div class="authenticity-score {getScoreClass(review.authenticityScore)}">
								{review.authenticityScore}%
							</div>
						</div>

						<div class="review-content">
							<h4 class="prompt-preview">
								{review.originalPrompt.length > 100 
									? review.originalPrompt.substring(0, 100) + '...' 
									: review.originalPrompt}
							</h4>
							
							<div class="target-user">
								<strong>Target:</strong> {review.targetUser?.displayName || 'Unknown User'}
								{#if review.targetUser?.isSimulated}🤖{/if}
							</div>

							{#if review.generatedContent.title}
								<div class="generated-title">
									<strong>Generated Title:</strong> {review.generatedContent.title}
								</div>
							{/if}
						</div>

						<div class="review-footer">
							<div class="review-dates">
								<span class="created-date">
									Created: {formatDate(review.createdAt)}
								</span>
								{#if review.reviewedAt}
									<span class="reviewed-date">
										Reviewed: {formatDate(review.reviewedAt)}
									</span>
								{/if}
							</div>
						</div>
					</div>
				{/each}
			</div>

			<!-- Pagination -->
			{#if totalPages > 1}
				<div class="pagination">
					<button 
						class="btn secondary"
						onclick={() => goToPage(currentPage - 1)}
						disabled={currentPage === 1}
					>
						← Previous
					</button>
					
					<span class="page-info">
						Page {currentPage} of {totalPages}
					</span>
					
					<button 
						class="btn secondary"
						onclick={() => goToPage(currentPage + 1)}
						disabled={currentPage === totalPages}
					>
						Next →
					</button>
				</div>
			{/if}
		{/if}
	</div>
</div>

<!-- Review Modal -->
<AIContentReview
	bind:showModal={showReviewModal}
	reviewId={selectedReviewId}
	on:updated={handleReviewUpdate}
	on:approved={handleContentApproval}
	on:close={() => {
		showReviewModal = false;
		selectedReviewId = null;
	}}
/>

<style>
	.admin-page {
		padding: 2rem;
		max-width: 1200px;
		margin: 0 auto;
	}

	.page-header {
		margin-bottom: 2rem;
	}

	.page-header h1 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 2rem;
	}

	.page-description {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 1.1rem;
	}

	.success-message {
		padding: 1rem;
		background: var(--theme-accent-success);
		color: white;
		border-radius: 6px;
		margin-bottom: 1.5rem;
		font-weight: 500;
	}

	.filters-section {
		background: var(--theme-bg-secondary);
		padding: 1.5rem;
		border-radius: 8px;
		margin-bottom: 2rem;
		border: 1px solid var(--theme-border);
	}

	.filters-row {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 1rem;
		align-items: end;
	}

	.filter-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.filter-group label {
		font-weight: 500;
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.filter-group select,
	.filter-group input {
		padding: 0.75rem;
		border: 1px solid var(--theme-border);
		border-radius: 6px;
		background: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.filter-group select:focus,
	.filter-group input:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
		box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
	}

	.filter-actions {
		display: flex;
		gap: 0.5rem;
	}

	.btn {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.75rem 1rem;
		border: none;
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
	}

	.btn.secondary {
		background: var(--theme-bg-tertiary);
		color: var(--theme-text-primary);
		border: 1px solid var(--theme-border);
	}

	.btn.secondary:hover:not(:disabled) {
		background: var(--theme-bg-secondary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-1px);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;
	}

	.reviews-section {
		min-height: 400px;
	}

	.empty-state {
		text-align: center;
		padding: 3rem;
		color: var(--theme-text-secondary);
	}

	.empty-state h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
	}

	.reviews-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
		gap: 1.5rem;
	}

	.review-card {
		background: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		padding: 1.5rem;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.review-card:hover {
		border-color: var(--theme-accent-primary);
		transform: translateY(-2px);
		box-shadow: 0 4px 12px var(--theme-shadow-hover);
	}

	.review-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 1rem;
	}

	.review-meta {
		display: flex;
		gap: 0.5rem;
		flex-wrap: wrap;
	}

	.content-type {
		padding: 0.25rem 0.5rem;
		background: var(--theme-accent-primary);
		color: white;
		border-radius: 4px;
		font-size: 0.8rem;
		text-transform: uppercase;
		font-weight: 600;
	}

	.status-badge {
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.status-success { background: var(--theme-accent-success); color: white; }
	.status-warning { background: var(--theme-accent-warning); color: white; }
	.status-danger { background: var(--theme-accent-danger); color: white; }
	.status-info { background: var(--theme-accent-info); color: white; }
	.status-secondary { background: var(--theme-bg-tertiary); color: var(--theme-text-secondary); }

	.authenticity-score {
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.score-high { background: var(--theme-accent-success); color: white; }
	.score-medium { background: var(--theme-accent-warning); color: white; }
	.score-low { background: var(--theme-accent-danger); color: white; }

	.review-content {
		margin-bottom: 1rem;
	}

	.prompt-preview {
		margin: 0 0 0.75rem 0;
		color: var(--theme-text-primary);
		font-size: 1rem;
		line-height: 1.4;
	}

	.target-user,
	.generated-title {
		margin-bottom: 0.5rem;
		font-size: 0.9rem;
		color: var(--theme-text-secondary);
	}

	.review-footer {
		border-top: 1px solid var(--theme-border);
		padding-top: 1rem;
	}

	.review-dates {
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
		font-size: 0.8rem;
		color: var(--theme-text-muted);
	}

	.pagination {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 1rem;
		margin-top: 2rem;
		padding: 1rem;
	}

	.page-info {
		color: var(--theme-text-secondary);
		font-weight: 500;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.admin-page {
			padding: 1rem;
		}

		.filters-row {
			grid-template-columns: 1fr;
		}

		.filter-actions {
			justify-content: stretch;
		}

		.filter-actions .btn {
			flex: 1;
		}

		.reviews-grid {
			grid-template-columns: 1fr;
		}

		.review-header {
			flex-direction: column;
			gap: 0.5rem;
		}

		.pagination {
			flex-direction: column;
			gap: 0.5rem;
		}
	}

	/* High Contrast Mode */
	@media (prefers-contrast: high) {
		.review-card,
		.filters-section {
			border: 2px solid currentColor;
		}

		.status-badge,
		.authenticity-score,
		.content-type {
			border: 1px solid currentColor;
		}
	}

	/* Reduced Motion */
	@media (prefers-reduced-motion: reduce) {
		.review-card,
		.btn {
			transition: none;
		}
	}
</style>
