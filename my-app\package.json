{"name": "my-app", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test", "db:start": "docker compose up", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@chromatic-com/storybook": "^3", "@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@fontsource/fira-mono": "^5.0.0", "@neoconfetti/svelte": "^2.0.0", "@playwright/test": "^1.49.1", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-svelte-csf": "^5.0.0-next.0", "@storybook/blocks": "^8.6.12", "@storybook/experimental-addon-test": "^8.6.12", "@storybook/svelte": "^8.6.12", "@storybook/sveltekit": "^8.6.12", "@storybook/test": "^8.6.12", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/vite": "^4.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.4", "@types/node": "^22", "@vitest/browser": "^3.1.3", "@vitest/coverage-v8": "^3.1.3", "drizzle-kit": "^0.30.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "jsdom": "^26.0.0", "mdsvex": "^0.12.3", "playwright": "^1.52.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "storybook": "^8.6.12", "svelte": "^5.25.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6", "vitest": "^3.0.0"}, "dependencies": {"@inlang/paraglide-js": "^2.0.0", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "drizzle-orm": "^0.40.0", "postgres": "^3.4.5"}}