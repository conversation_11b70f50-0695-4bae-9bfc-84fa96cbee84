import { beforeAll, afterAll } from 'vitest';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.DATABASE_URL || 'sqlite::memory:';

// Global test setup
beforeAll(async () => {
  // Initialize test database if needed
  console.log('Setting up test environment...');
});

afterAll(async () => {
  // Cleanup test environment
  console.log('Cleaning up test environment...');
});

// Mock fetch for API tests
global.fetch = global.fetch || (() => {
  throw new Error('fetch is not available in test environment');
});

// Mock console methods to reduce noise in tests
const originalConsole = { ...console };
console.log = () => {};
console.info = () => {};
console.warn = () => {};

// Restore console for debugging when needed
export const restoreConsole = () => {
  Object.assign(console, originalConsole);
};
