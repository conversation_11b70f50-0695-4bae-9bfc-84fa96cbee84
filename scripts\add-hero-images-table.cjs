const Database = require('better-sqlite3');
const path = require('path');

// Connect to the database
const dbPath = path.join(__dirname, '..', 'local.db');
const db = new Database(dbPath);

console.log('Adding hero_images table...');

try {
  // Create hero_images table
  db.exec(`
    CREATE TABLE IF NOT EXISTS hero_images (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      subtitle TEXT,
      image_url TEXT NOT NULL,
      active INTEGER NOT NULL DEFAULT 0,
      sort_order INTEGER NOT NULL DEFAULT 0,
      author_id INTEGER REFERENCES users(id),
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  console.log('Hero images table created successfully!');
  
  // Insert a default hero image
  const existingHero = db.prepare('SELECT * FROM hero_images WHERE active = 1').get();
  
  if (!existingHero) {
    console.log('Creating default hero image...');
    
    const stmt = db.prepare(`
      INSERT INTO hero_images (title, subtitle, image_url, active, sort_order, author_id)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      'Welcome to the Finn Wolfhard Fan Club',
      'The official fan community for fans of actor and musician Finn Wolfhard',
      '/images/hero-bg.jpg',
      1, // active
      0, // sort_order
      1  // admin user ID
    );
    
    console.log('Default hero image created successfully!');
  } else {
    console.log('Active hero image already exists.');
  }
  
  // Check tables
  console.log('\nDatabase tables:');
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  tables.forEach(table => {
    const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
    console.log(`- ${table.name}: ${count.count} records`);
  });
  
} catch (error) {
  console.error('Error adding hero_images table:', error);
} finally {
  db.close();
  console.log('Migration complete.');
}
