<script>
	import { onMount } from 'svelte';
	import { api } from '$lib/utils/api';
	import logger from '$lib/client/logger';
	import ImageSelector from '$lib/components/admin/ImageSelector.svelte';

	// Create a logger with context
	const log = logger.withContext('hero-images-admin');

	// State variables
	/** @type {Array<{id: number, title: string, subtitle?: string, imageUrl: string, active: boolean, sortOrder: number, createdAt: string, updatedAt: string}>} */
	let heroImages = [];
	let loading = true;
	/** @type {string|null} */
	let error = null;
	/** @type {string|null} */
	let successMessage = null;

	// Form state for creating new hero image
	let showCreateForm = false;
	let newHeroImage = {
		title: '',
		subtitle: '',
		imageUrl: '',
		thumbnailUrl: '',
		active: false,
		sortOrder: 0
	};

	// Fetch hero images from the API
	async function fetchHeroImages() {
		loading = true;
		error = null;

		log.info('Fetching hero images');
		const { data, error: apiError } = await api.get('/api/hero-images?all=true');

		if (apiError) {
			log.error('Failed to fetch hero images', apiError);
			error = apiError;
		} else {
			log.debug('Hero images fetched successfully', { count: data?.length });
			heroImages = data || [];
		}

		loading = false;
	}

	/**
	 * Delete a hero image
	 * @param {number} id - The ID of the hero image to delete
	 */
	async function deleteHeroImage(id) {
		if (!confirm('Are you sure you want to delete this hero image?')) {
			return;
		}

		log.info(`Deleting hero image ${id}`);
		const { success, error: apiError } = await api.delete(`/api/hero-images/${id}`);

		if (success) {
			log.info(`Hero image ${id} deleted successfully`);
			// Remove the item from the list
			heroImages = heroImages.filter(item => item.id !== id);
			successMessage = 'Hero image deleted successfully';
		} else {
			log.error(`Failed to delete hero image ${id}`, apiError);
			error = apiError || 'Failed to delete hero image';
		}
	}

	/**
	 * Toggle the active status of a hero image
	 * @param {{id: number, title: string, subtitle?: string, imageUrl: string, active: boolean, sortOrder: number}} item - The hero image to update
	 */
	async function toggleActive(item) {
		const newStatus = !item.active;
		log.info(`Toggling hero image ${item.id} active status to ${newStatus}`);

		const { success, data, error: apiError } = await api.put(`/api/hero-images/${item.id}`, {
			...item,
			active: newStatus
		});

		if (success && data) {
			log.info(`Hero image ${item.id} updated successfully`);
			// Update the item in the list
			heroImages = heroImages.map(i =>
				i.id === item.id ? data : i
			);
			successMessage = `Hero image ${newStatus ? 'activated' : 'deactivated'} successfully`;
		} else {
			log.error(`Failed to update hero image ${item.id}`, apiError);
			error = apiError || 'Failed to update hero image';
		}
	}

	/**
	 * Handle image selection from ImageSelector
	 */
	function handleImageSelect(event) {
		const { imageUrl, thumbnailUrl } = event.detail;
		newHeroImage.imageUrl = imageUrl;
		// Store thumbnail URL for potential future use
		newHeroImage.thumbnailUrl = thumbnailUrl;
	}

	/**
	 * Create a new hero image
	 */
	async function createHeroImage() {
		if (!newHeroImage.title || !newHeroImage.imageUrl) {
			error = 'Title and image URL are required';
			return;
		}

		log.info('Creating new hero image', newHeroImage);
		const { success, data, error: apiError } = await api.post('/api/hero-images', newHeroImage);

		if (success && data) {
			log.info('Hero image created successfully', data);
			heroImages = [data, ...heroImages];
			successMessage = 'Hero image created successfully';

			// Reset form
			newHeroImage = {
				title: '',
				subtitle: '',
				imageUrl: '',
				thumbnailUrl: '',
				active: false,
				sortOrder: 0
			};
			showCreateForm = false;
		} else {
			log.error('Failed to create hero image', apiError);
			error = apiError || 'Failed to create hero image';
		}
	}

	// Load hero images on mount
	onMount(() => {
		fetchHeroImages();
	});
</script>

<svelte:head>
	<title>Hero Images Management - Admin</title>
</svelte:head>

<div class="hero-admin-container">
	<div class="header">
		<h1>Hero Images Management</h1>
		<button class="btn primary" on:click={() => showCreateForm = !showCreateForm}>
			{showCreateForm ? 'Cancel' : 'Add New Hero Image'}
		</button>
	</div>

	{#if error}
		<div class="error-message">
			<p>{error}</p>
			<button class="btn secondary" on:click={() => error = null}>Dismiss</button>
		</div>
	{/if}

	{#if successMessage}
		<div class="success-message">
			<p>{successMessage}</p>
			<button class="btn secondary" on:click={() => successMessage = null}>Dismiss</button>
		</div>
	{/if}

	{#if showCreateForm}
		<div class="create-form">
			<h2>Create New Hero Image</h2>
			<form on:submit|preventDefault={createHeroImage}>
				<div class="form-group">
					<label for="title">Title *</label>
					<input
						type="text"
						id="title"
						bind:value={newHeroImage.title}
						required
						placeholder="Enter hero image title"
					/>
				</div>

				<div class="form-group">
					<label for="subtitle">Subtitle</label>
					<input
						type="text"
						id="subtitle"
						bind:value={newHeroImage.subtitle}
						placeholder="Enter hero image subtitle (optional)"
					/>
				</div>

				<div class="form-group">
					<label>Hero Image *</label>
					<ImageSelector
						selectedImageUrl={newHeroImage.imageUrl}
						on:select={handleImageSelect}
					/>
				</div>

				<div class="form-group">
					<label for="sortOrder">Sort Order</label>
					<input
						type="number"
						id="sortOrder"
						bind:value={newHeroImage.sortOrder}
						placeholder="0"
					/>
				</div>

				<div class="form-group checkbox">
					<label>
						<input
							type="checkbox"
							bind:checked={newHeroImage.active}
						/>
						Set as active hero image
					</label>
				</div>

				<div class="form-actions">
					<button type="submit" class="btn primary">Create Hero Image</button>
					<button type="button" class="btn secondary" on:click={() => showCreateForm = false}>Cancel</button>
				</div>
			</form>
		</div>
	{/if}

	{#if loading}
		<div class="loading">
			<p>Loading hero images...</p>
		</div>
	{:else if heroImages.length === 0}
		<div class="empty-state">
			<p>No hero images found. Create one to get started!</p>
		</div>
	{:else}
		<div class="hero-grid">
			{#each heroImages as item}
				<div class="hero-item" class:active={item.active}>
					<div class="hero-image">
						<img src={item.imageUrl} alt={item.title} />
						{#if item.active}
							<div class="active-badge">Active</div>
						{/if}
					</div>
					<div class="hero-info">
						<h3>{item.title}</h3>
						{#if item.subtitle}
							<p class="subtitle">{item.subtitle}</p>
						{/if}
						<p class="date">Created: {new Date(item.createdAt).toLocaleDateString()}</p>
						<p class="sort-order">Sort Order: {item.sortOrder}</p>
					</div>
					<div class="hero-actions">
						<button
							class="btn icon-btn"
							title={item.active ? 'Deactivate' : 'Activate'}
							on:click={() => toggleActive(item)}
						>
							{item.active ? '🔴' : '🟢'}
						</button>
						<button
							class="btn icon-btn delete"
							title="Delete"
							on:click={() => deleteHeroImage(item.id)}
						>
							🗑️
						</button>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>

<style>
	.hero-admin-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border: 1px solid var(--theme-accent-primary);
	}

	.primary:hover {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
	}

	.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border: 1px solid var(--theme-border);
	}

	.secondary:hover {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
	}

	.icon-btn {
		padding: 0.5rem;
		font-size: 1.2rem;
		background: none;
		border: 1px solid var(--theme-border);
		color: var(--theme-text-primary);
	}

	.delete {
		color: var(--theme-accent-danger);
	}

	.error-message {
		background-color: var(--theme-accent-danger);
		color: white;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border: 1px solid var(--theme-accent-danger);
	}

	.success-message {
		background-color: var(--theme-accent-success);
		color: white;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border: 1px solid var(--theme-accent-success);
	}

	.loading, .empty-state {
		text-align: center;
		padding: 3rem;
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		color: var(--theme-text-primary);
	}

	.create-form {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		padding: 2rem;
		border-radius: 8px;
		margin-bottom: 2rem;
		color: var(--theme-text-primary);
	}

	.form-group {
		margin-bottom: 1rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: bold;
	}

	.form-group input {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid var(--theme-input-border);
		border-radius: 4px;
		font-size: 1rem;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
	}

	.form-group input:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
	}

	.form-group.checkbox label {
		display: flex;
		align-items: center;
		font-weight: normal;
	}

	.form-group.checkbox input {
		width: auto;
		margin-right: 0.5rem;
	}

	.form-actions {
		display: flex;
		gap: 1rem;
		margin-top: 1.5rem;
	}

	.hero-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
		gap: 1.5rem;
	}

	.hero-item {
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 2px 4px var(--theme-shadow);
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		position: relative;
		color: var(--theme-text-primary);
	}

	.hero-item.active {
		border: 2px solid var(--theme-accent-primary);
	}

	.hero-image {
		height: 200px;
		overflow: hidden;
		position: relative;
	}

	.hero-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.active-badge {
		position: absolute;
		top: 0.5rem;
		right: 0.5rem;
		background-color: var(--theme-accent-primary);
		color: white;
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-size: 0.8rem;
		font-weight: bold;
	}

	.hero-info {
		padding: 1rem;
	}

	.hero-info h3 {
		margin: 0 0 0.5rem 0;
	}

	.subtitle {
		margin: 0.5rem 0;
		color: var(--theme-text-secondary);
		font-style: italic;
	}

	.date, .sort-order {
		font-size: 0.9rem;
		color: var(--theme-text-muted);
		margin: 0.25rem 0;
	}

	.hero-actions {
		display: flex;
		justify-content: flex-end;
		padding: 0.5rem;
		background-color: var(--theme-bg-secondary);
		border-top: 1px solid var(--theme-border);
	}
</style>
