import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { media } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/media/[id] - Get a specific media item
export const GET: RequestHandler = async ({ params }) => {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }
    
    const item = await db.select()
      .from(media)
      .where(eq(media.id, id))
      .limit(1);
    
    if (item.length === 0) {
      return json({
        success: false,
        error: 'Media item not found'
      }, { status: 404 });
    }
    
    return json({
      success: true,
      data: item[0]
    });
  } catch (error) {
    console.error('Error fetching media item:', error);
    return json({
      success: false,
      error: 'Failed to fetch media item'
    }, { status: 500 });
  }
};

// PUT /api/media/[id] - Update a media item (admin only)
export const PUT: RequestHandler = async ({ params, request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }
    
    const body = await request.json();
    
    // Check if media item exists
    const existingItem = await db.select()
      .from(media)
      .where(eq(media.id, id))
      .limit(1);
    
    if (existingItem.length === 0) {
      return json({
        success: false,
        error: 'Media item not found'
      }, { status: 404 });
    }
    
    // Update the media item
    const result = await db.update(media)
      .set({
        alt: body.alt,
        caption: body.caption,
        updatedAt: new Date().toISOString()
      })
      .where(eq(media.id, id))
      .returning();
    
    return json({
      success: true,
      data: result[0]
    });
  } catch (error) {
    console.error('Error updating media item:', error);
    return json({
      success: false,
      error: 'Failed to update media item'
    }, { status: 500 });
  }
};

// DELETE /api/media/[id] - Delete a media item (admin only)
export const DELETE: RequestHandler = async ({ params, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }
    
    // Check if media item exists
    const existingItem = await db.select()
      .from(media)
      .where(eq(media.id, id))
      .limit(1);
    
    if (existingItem.length === 0) {
      return json({
        success: false,
        error: 'Media item not found'
      }, { status: 404 });
    }
    
    // Delete the media item
    await db.delete(media)
      .where(eq(media.id, id));
    
    return json({
      success: true,
      message: 'Media item deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting media item:', error);
    return json({
      success: false,
      error: 'Failed to delete media item'
    }, { status: 500 });
  }
};
